import { Injectable, NotFoundException, ForbiddenException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, Between, MoreThanOrEqual, LessThanOrEqual, In } from 'typeorm';
import { Event, EventStatus, EventResponseStatus } from '../../entities/event.entity';
import { SpaceMember } from '../../entities/space-member.entity';
import { User } from '../../entities/user.entity';
import { Tag } from '../../entities/tag.entity';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
import { CreateEventResponseDto, UpdateEventResponseDto } from './dto/event-response.dto';
import { GetEventsDto } from './dto/get-events.dto';
import { JoinEventDto, ConfirmEventDateDto, SuggestEventDatesDto } from './dto/event-join.dto';
import { TagService } from '../tag/tag.service';

@Injectable()
export class EventService {
  constructor(
    @InjectRepository(Event)
    private eventRepository: Repository<Event>,
    @InjectRepository(SpaceMember)
    private spaceMemberRepository: Repository<SpaceMember>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Tag)
    private tagRepository: Repository<Tag>,
    private tagService: TagService,
  ) {}

  async createEvent(
    spaceId: string,
    userId: string,
    createEventDto: CreateEventDto,
  ): Promise<Event> {
    // Check if user is a member of the space
    await this.checkSpaceMembership(spaceId, userId);

    const { tags, ...eventData } = createEventDto;

    const event = this.eventRepository.create({
      spaceId,
      creatorId: userId,
      ...eventData,
    });

    const savedEvent = await this.eventRepository.save(event);

    // Add creation history
    const creatorName = await this.getUserDisplayName(userId);
    savedEvent.addCreatedHistory(userId, creatorName);

    // Handle tags if provided
    if (tags && tags.length > 0) {
      try {
        const tagEntities = await this.tagService.getOrCreateTags(
          spaceId,
          userId,
          tags,
        );

        // Store tag IDs as JSON array in the events table
        const tagIds = tagEntities.map(tag => tag.id);
        savedEvent.tagIds = JSON.stringify(tagIds);
      } catch (error) {
        console.error('❌ Error processing tags:', error);
        throw error;
      }
    }

    // Record creator's date selections in user_date_selections
    if (eventData.eventDates && eventData.eventDates.length > 0) {
      savedEvent.userDateSelections = {};
      eventData.eventDates.forEach(date => {
        if (!savedEvent.userDateSelections[date]) {
          savedEvent.userDateSelections[date] = [];
        }
        savedEvent.userDateSelections[date].push(userId);
      });
    }

    // Save with history, tags, and user date selections
    await this.eventRepository.save(savedEvent);

    console.log('✅ Event created:', savedEvent.id);

    return this.getEventById(savedEvent.id, userId);
  }

  async getEvents(
    spaceId: string,
    userId: string,
    getEventsDto: GetEventsDto,
  ): Promise<{ events: Event[]; total: number }> {
    // Check if user is a member of the space
    await this.checkSpaceMembership(spaceId, userId);

    const { status, fromDate, toDate, limit = 20, offset = 0 } = getEventsDto;

    const whereConditions: any = { spaceId };

    // Filter by status
    if (status) {
      whereConditions.status = status;
    }

    // Build query with date range filtering for JSON array
    const queryBuilder = this.eventRepository.createQueryBuilder('event')
      .leftJoinAndSelect('event.creator', 'creator')
      .leftJoinAndSelect('event.linkedTodo', 'linkedTodo')
      .leftJoinAndSelect('event.linkedToGo', 'linkedToGo')
      .where('event.spaceId = :spaceId', { spaceId });

    // Filter by status
    if (status) {
      queryBuilder.andWhere('event.status = :status', { status });
    }

    // Filter by date range - simplified for SQLite compatibility
    if (fromDate && toDate) {
      queryBuilder.andWhere(`
        event.eventDates LIKE '%' || :fromDateStr || '%' OR
        event.eventDates LIKE '%' || :toDateStr || '%'
      `, {
        fromDateStr: fromDate,
        toDateStr: toDate
      });
    } else if (fromDate) {
      queryBuilder.andWhere(`
        event.eventDates LIKE '%' || :fromDateStr || '%'
      `, { fromDateStr: fromDate });
    } else if (toDate) {
      queryBuilder.andWhere(`
        event.eventDates LIKE '%' || :toDateStr || '%'
      `, { toDateStr: toDate });
    }

    // Order by created date for SQLite compatibility
    queryBuilder
      .orderBy('event.createdAt', 'DESC')
      .addOrderBy('event.eventTime', 'ASC')
      .take(limit)
      .skip(offset);

    const [events, total] = await queryBuilder.getManyAndCount();

    // Load tags for all events
    const eventsWithTags = await this.loadTagsForEvents(events);

    return { events: eventsWithTags, total };
  }

  async getEventById(eventId: string, userId: string): Promise<Event> {
    const event = await this.eventRepository.findOne({
      where: { id: eventId },
      relations: ['creator', 'space', 'linkedTodo', 'linkedToGo'],
    });

    if (!event) {
      throw new NotFoundException('Event not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(event.spaceId, userId);

    // Load tags for the event
    const eventWithTags = await this.loadTagsForEvents([event]);

    return eventWithTags[0];
  }

  async updateEvent(
    eventId: string,
    userId: string,
    updateEventDto: UpdateEventDto,
  ): Promise<Event> {
    const event = await this.getEventById(eventId, userId);

    // Only the creator can update the event
    if (event.creatorId !== userId) {
      throw new ForbiddenException('Only the event creator can update this event');
    }

    await this.eventRepository.update(eventId, updateEventDto);
    console.log('✅ Event updated:', eventId);

    return this.getEventById(eventId, userId);
  }

  async deleteEvent(eventId: string, userId: string): Promise<void> {
    const event = await this.getEventById(eventId, userId);

    // Only the creator can delete the event
    if (event.creatorId !== userId) {
      throw new ForbiddenException('Only the event creator can delete this event');
    }

    await this.eventRepository.softDelete(eventId);
    console.log('✅ Event deleted:', eventId);
  }

  async respondToEvent(
    eventId: string,
    userId: string,
    createEventResponseDto: CreateEventResponseDto,
  ): Promise<{ userId: string; status: EventResponseStatus; note?: string; createdAt: string; updatedAt: string }> {
    const event = await this.getEventById(eventId, userId);

    // Set user response using the new integrated method
    event.setUserResponse(userId, createEventResponseDto.status, createEventResponseDto.note);

    await this.eventRepository.save(event);
    console.log('✅ Event response updated for user:', userId);

    return {
      userId,
      ...event.getUserResponse(userId)!,
    };
  }

  async updateEventResponse(
    eventId: string,
    userId: string,
    updateEventResponseDto: UpdateEventResponseDto,
  ): Promise<{ userId: string; status: EventResponseStatus; note?: string; createdAt: string; updatedAt: string }> {
    const event = await this.getEventById(eventId, userId);

    // Check if user has a response
    const existingResponse = event.getUserResponse(userId);
    if (!existingResponse) {
      throw new NotFoundException('Event response not found');
    }

    // Update user response
    event.setUserResponse(userId, updateEventResponseDto.status || existingResponse.status, updateEventResponseDto.note);

    await this.eventRepository.save(event);
    console.log('✅ Event response updated for user:', userId);

    return {
      userId,
      ...event.getUserResponse(userId)!,
    };
  }

  async deleteEventResponse(eventId: string, userId: string): Promise<void> {
    const event = await this.getEventById(eventId, userId);

    // Check if user has a response
    const existingResponse = event.getUserResponse(userId);
    if (!existingResponse) {
      throw new NotFoundException('Event response not found');
    }

    // Remove user response
    event.removeUserResponse(userId);

    await this.eventRepository.save(event);
    console.log('✅ Event response deleted for user:', userId);
  }

  private async checkSpaceMembership(spaceId: string, userId: string): Promise<void> {
    const membership = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!membership) {
      throw new ForbiddenException('You are not a member of this space');
    }
  }

  // User Date Selection Methods
  async selectEventDates(
    eventId: string,
    userId: string,
    dates: string[], // Array of date strings in YYYY-MM-DD format
  ): Promise<Event> {
    const event = await this.eventRepository.findOne({
      where: { id: eventId },
    });

    if (!event) {
      throw new NotFoundException('Event not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(event.spaceId, userId);

    // Initialize userDateSelections if it doesn't exist
    if (!event.userDateSelections) {
      event.userDateSelections = {};
    }

    // Add user to selected dates
    dates.forEach(date => {
      if (!event.userDateSelections[date]) {
        event.userDateSelections[date] = [];
      }

      // Add user if not already selected
      if (!event.userDateSelections[date].includes(userId)) {
        event.userDateSelections[date].push(userId);
      }
    });

    await this.eventRepository.save(event);
    return this.getEventById(eventId, userId);
  }

  async deselectEventDates(
    eventId: string,
    userId: string,
    dates: string[], // Array of date strings in YYYY-MM-DD format
  ): Promise<Event> {
    const event = await this.eventRepository.findOne({
      where: { id: eventId },
    });

    if (!event) {
      throw new NotFoundException('Event not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(event.spaceId, userId);

    if (!event.userDateSelections) {
      return event; // Nothing to deselect
    }

    // Remove user from selected dates
    dates.forEach(date => {
      if (event.userDateSelections[date]) {
        event.userDateSelections[date] = event.userDateSelections[date].filter(
          id => id !== userId
        );

        // Remove empty date arrays
        if (event.userDateSelections[date].length === 0) {
          delete event.userDateSelections[date];
        }
      }
    });

    await this.eventRepository.save(event);
    return this.getEventById(eventId, userId);
  }

  async getUserDateSelections(
    eventId: string,
    userId: string,
  ): Promise<{ selectedDates: string[]; allSelections: Record<string, string[]> }> {
    const event = await this.eventRepository.findOne({
      where: { id: eventId },
    });

    if (!event) {
      throw new NotFoundException('Event not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(event.spaceId, userId);

    const userSelectedDates: string[] = [];
    const allSelections = event.userDateSelections || {};

    // Find dates selected by the current user
    Object.entries(allSelections).forEach(([date, userIds]) => {
      if (userIds.includes(userId)) {
        userSelectedDates.push(date);
      }
    });

    return {
      selectedDates: userSelectedDates,
      allSelections,
    };
  }

  async getEventDateSelectionSummary(
    eventId: string,
    userId: string,
  ): Promise<Record<string, { userIds: string[]; count: number }>> {
    const event = await this.eventRepository.findOne({
      where: { id: eventId },
    });

    if (!event) {
      throw new NotFoundException('Event not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(event.spaceId, userId);

    const summary: Record<string, { userIds: string[]; count: number }> = {};
    const allSelections = event.userDateSelections || {};

    Object.entries(allSelections).forEach(([date, userIds]) => {
      summary[date] = {
        userIds,
        count: userIds.length,
      };
    });

    return summary;
  }

  async joinEvent(
    eventId: string,
    userId: string,
    joinDto: JoinEventDto,
  ): Promise<{ userId: string; status: EventResponseStatus; note?: string; createdAt: string; updatedAt: string }> {
    const event = await this.getEventById(eventId, userId);

    // Verify user is a member of the space
    await this.checkSpaceMembership(event.spaceId, userId);

    // Set user response using the new integrated method
    event.setUserResponse(userId, joinDto.status, joinDto.note);

    // Add user joined history
    const userName = await this.getUserDisplayName(userId);
    event.addUserJoinedHistory(userId, userName, joinDto.status, joinDto.note);

    await this.eventRepository.save(event);

    console.log(`✅ User ${userId} joined event ${eventId} with status: ${joinDto.status}`);

    return {
      userId,
      ...event.getUserResponse(userId)!,
    };
  }

  async confirmEventDate(
    eventId: string,
    userId: string,
    confirmDto: ConfirmEventDateDto,
  ): Promise<Event> {
    const event = await this.getEventById(eventId, userId);

    // Only event creator can confirm dates
    if (event.creatorId !== userId) {
      throw new ForbiddenException('Only the event creator can confirm dates');
    }

    // Update confirmed date
    event.confirmedDate = confirmDto.confirmedDate;

    // If confirmed time is provided, use it; otherwise use the suggested time from the event
    if (confirmDto.confirmedTime) {
      event.confirmedTime = confirmDto.confirmedTime;
    } else if (event.eventTime) {
      // Use suggested time as confirmed time
      event.confirmedTime = event.eventTime;
    }

    // Add date confirmed history
    const userName = await this.getUserDisplayName(userId);
    event.addDateConfirmedHistory(userId, userName, confirmDto.confirmedDate, event.confirmedTime);

    const savedEvent = await this.eventRepository.save(event);

    console.log(`✅ Event ${eventId} date confirmed: ${confirmDto.confirmedDate} ${savedEvent.confirmedTime || 'no time'}`);

    return savedEvent;
  }

  async suggestEventDates(
    eventId: string,
    userId: string,
    suggestDto: SuggestEventDatesDto,
  ): Promise<void> {
    const event = await this.getEventById(eventId, userId);

    // Verify user is a member of the space
    await this.checkSpaceMembership(event.spaceId, userId);

    // Add suggested dates to the event's date selection
    for (const suggestedDate of suggestDto.suggestedDates) {
      await this.selectEventDates(eventId, userId, [suggestedDate]);
    }

    // Update event's eventDates to include all suggested dates from all users
    const updatedEvent = await this.eventRepository.findOne({
      where: { id: eventId },
    });

    if (updatedEvent && updatedEvent.userDateSelections) {
      // Collect all unique dates from userDateSelections
      const allSuggestedDates = new Set<string>();
      Object.keys(updatedEvent.userDateSelections).forEach(date => {
        allSuggestedDates.add(date);
      });

      // Update eventDates with all suggested dates
      updatedEvent.eventDates = Array.from(allSuggestedDates).sort();

      // Save the updated event
      await this.eventRepository.save(updatedEvent);
    }

    // Add suggested dates history
    const userName = await this.getUserDisplayName(userId);
    event.addUserSuggestedDatesHistory(userId, userName, suggestDto.suggestedDates);

    // Update user's response status to 'suggested'
    await this.joinEvent(eventId, userId, {
      status: EventResponseStatus.SUGGESTED,
      note: suggestDto.note,
    });

    console.log(`✅ User ${userId} suggested ${suggestDto.suggestedDates.length} dates for event ${eventId}`);
  }

  async getEventParticipants(eventId: string, userId: string): Promise<Array<{
    userId: string;
    status: EventResponseStatus;
    note?: string;
    createdAt: string;
    updatedAt: string;
  }>> {
    const event = await this.getEventById(eventId, userId);

    const participants = event.getAllUserResponses();

    // Sort by createdAt
    participants.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

    return participants;
  }

  async getEventDateTimeSummary(eventId: string, userId: string): Promise<{
    suggestedDates: string[];
    suggestedTime: string | null;
    confirmedDate: string | null;
    confirmedTime: string | null;
    isConfirmed: boolean;
  }> {
    const event = await this.getEventById(eventId, userId);

    // Get suggested dates from eventDates field
    const suggestedDates = event.eventDates || [];

    // Get suggested time from eventTime field
    const suggestedTime = event.eventTime || null;

    // Get confirmed date and time
    const confirmedDate = event.confirmedDate || null;
    const confirmedTime = event.confirmedTime || null;

    // Check if event is confirmed (has confirmed date)
    const isConfirmed = !!confirmedDate;

    return {
      suggestedDates,
      suggestedTime,
      confirmedDate,
      confirmedTime,
      isConfirmed,
    };
  }

  async getEventHistory(eventId: string, userId: string): Promise<any[]> {
    const event = await this.getEventById(eventId, userId);
    return event.getHistoryArray();
  }

  // Helper method to get user display name
  private async getUserDisplayName(userId: string): Promise<string> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['displayName'],
    });
    return user?.displayName || 'Unknown User';
  }

  /**
   * Load tags for events based on their tagIds field
   */
  private async loadTagsForEvents(events: Event[]): Promise<Event[]> {
    if (!events.length) return events;

    // Collect all unique tag IDs
    const allTagIds = new Set<string>();
    events.forEach(event => {
      if (event.tagIds) {
        try {
          const tagIds = JSON.parse(event.tagIds);
          tagIds.forEach((id: string) => allTagIds.add(id));
        } catch {
          // Ignore invalid JSON
        }
      }
    });

    if (allTagIds.size === 0) return events;

    // Load all tags in one query
    const tags = await this.tagRepository.find({
      where: { id: In(Array.from(allTagIds)) }
    });
    const tagMap = new Map(tags.map(tag => [tag.id, tag]));

    // Attach tags to events
    return events.map(event => {
      if (event.tagIds) {
        try {
          const tagIds = JSON.parse(event.tagIds);
          (event as any).tags = tagIds
            .map((id: string) => tagMap.get(id))
            .filter(Boolean)
            .map((tag: any) => tag.name); // Convert tag objects to tag names
        } catch {
          (event as any).tags = [];
        }
      } else {
        (event as any).tags = [];
      }
      return event;
    });
  }
}
