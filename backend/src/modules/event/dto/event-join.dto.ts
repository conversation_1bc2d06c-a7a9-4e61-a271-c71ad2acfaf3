import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsArray } from 'class-validator';
import { EventResponseStatus } from '../../../entities/event.entity';

export class JoinEventDto {
  @ApiProperty({
    description: 'Response status for joining the event',
    example: 'pending',
    enum: EventResponseStatus,
  })
  @IsEnum(EventResponseStatus)
  status: EventResponseStatus;

  @ApiProperty({
    description: 'Optional note from the user',
    example: 'Looking forward to this event!',
    required: false,
  })
  @IsOptional()
  @IsString()
  note?: string;
}

export class ConfirmEventDateDto {
  @ApiProperty({
    description: 'Confirmed date for the event',
    example: '2024-01-15',
  })
  @IsString()
  confirmedDate: string;

  @ApiProperty({
    description: 'Confirmed time for the event',
    example: '14:00',
    required: false,
  })
  @IsOptional()
  @IsString()
  confirmedTime?: string;
}

export class SuggestEventDatesDto {
  @ApiProperty({
    description: 'Additional dates to suggest for the event',
    example: ['2024-01-16', '2024-01-17'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  suggestedDates: string[];

  @ApiProperty({
    description: 'Optional note about the suggested dates',
    example: 'These dates work better for me',
    required: false,
  })
  @IsOptional()
  @IsString()
  note?: string;
}
