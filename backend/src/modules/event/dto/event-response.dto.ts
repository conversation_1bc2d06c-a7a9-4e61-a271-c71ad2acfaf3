import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { EventResponseStatus } from '../../../entities/event.entity';

export class CreateEventResponseDto {
  @ApiProperty({
    description: 'Response status',
    enum: EventResponseStatus,
    example: EventResponseStatus.CONFIRMED,
  })
  @IsEnum(EventResponseStatus)
  status: EventResponseStatus;

  @ApiProperty({
    description: 'Optional note for the response',
    example: 'Looking forward to it!',
    required: false,
  })
  @IsOptional()
  @IsString()
  note?: string;
}

export class UpdateEventResponseDto {
  @ApiProperty({
    description: 'Response status',
    enum: EventResponseStatus,
    example: EventResponseStatus.CONFIRMED,
    required: false,
  })
  @IsOptional()
  @IsEnum(EventResponseStatus)
  status?: EventResponseStatus;

  @ApiProperty({
    description: 'Optional note for the response',
    example: 'Looking forward to it!',
    required: false,
  })
  @IsOptional()
  @IsString()
  note?: string;
}
