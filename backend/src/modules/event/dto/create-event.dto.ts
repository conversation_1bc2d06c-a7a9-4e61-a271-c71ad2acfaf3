import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsDateString, IsObject, IsUUID, IsArray } from 'class-validator';

export class CreateEventDto {
  @ApiProperty({
    description: 'Event title',
    example: 'Team Meeting',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Event description',
    example: 'Weekly team sync meeting to discuss project progress',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Event dates in YYYY-MM-DD format (array for multiple dates)',
    example: ['2025-01-30', '2025-01-31'],
    type: [String],
  })
  @IsArray()
  @IsDateString({}, { each: true })
  eventDates: string[];

  @ApiProperty({
    description: 'Event time in HH:MM format, time range (HH:MM-HH:MM), time of day period, or "full-day"',
    example: '14:30',
    examples: {
      specificTime: { value: '14:30', description: 'Specific time' },
      timeRange: { value: '14:00-16:00', description: 'Time range' },
      timeOfDay: { value: 'morning', description: 'Time of day period (morning, afternoon, evening, midnight)' },
      fullDay: { value: 'full-day', description: 'Full day event' },
    },
    required: false,
  })
  @IsOptional()
  @IsString()
  eventTime?: string;

  @ApiProperty({
    description: 'Event location',
    example: 'Conference Room A',
    required: false,
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiProperty({
    description: 'Linked Todo ID',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  linkedTodoId?: string;

  @ApiProperty({
    description: 'Linked ToGo ID',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  linkedToGoId?: string;

  @ApiProperty({
    description: 'Tags for the event',
    example: ['meeting', 'work', 'important'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'Additional metadata for the event',
    example: { reminder: true, recurring: false },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
