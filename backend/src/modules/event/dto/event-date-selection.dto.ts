import { IsArray, IsS<PERSON>, IsNotEmpty, ArrayMinSize } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class SelectEventDatesDto {
  @ApiProperty({
    description: 'Array of date strings in YYYY-MM-DD format',
    example: ['2025-01-30', '2025-01-31'],
    type: [String],
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  dates: string[];
}

export class DeselectEventDatesDto {
  @ApiProperty({
    description: 'Array of date strings in YYYY-MM-DD format to deselect',
    example: ['2025-01-30'],
    type: [String],
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  dates: string[];
}

export class EventDateSelectionResponseDto {
  @ApiProperty({
    description: 'Dates selected by the current user',
    example: ['2025-01-30', '2025-01-31'],
    type: [String],
  })
  selectedDates: string[];

  @ApiProperty({
    description: 'All date selections for the event',
    example: {
      '2025-01-30': ['userId1', 'userId2'],
      '2025-01-31': ['userId3'],
    },
  })
  allSelections: Record<string, string[]>;
}

export class EventDateSelectionSummaryDto {
  @ApiProperty({
    description: 'Summary of date selections with user counts',
    example: {
      '2025-01-30': { userIds: ['userId1', 'userId2'], count: 2 },
      '2025-01-31': { userIds: ['userId3'], count: 1 },
    },
  })
  summary: Record<string, { userIds: string[]; count: number }>;
}
