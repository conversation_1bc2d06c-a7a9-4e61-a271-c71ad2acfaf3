import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Request,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { EventService } from './event.service';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
import { CreateEventResponseDto, UpdateEventResponseDto } from './dto/event-response.dto';
import {
  SelectEventDatesDto,
  DeselectEventDatesDto,
  EventDateSelectionResponseDto,
  EventDateSelectionSummaryDto
} from './dto/event-date-selection.dto';
import { JoinEventDto, ConfirmEventDateDto, SuggestEventDatesDto } from './dto/event-join.dto';
import { GetEventsDto } from './dto/get-events.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

interface ApiResponseDto<T> {
  success: boolean;
  data: T;
  message: string;
  timestamp: string;
}

@ApiTags('Events')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('spaces/:spaceId/events')
export class EventController {
  constructor(
    private readonly eventService: EventService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new event' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiResponse({ status: 201, description: 'Event created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 403, description: 'Not a member of this space' })
  async createEvent(
    @Param('spaceId') spaceId: string,
    @Body() createEventDto: CreateEventDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const event = await this.eventService.createEvent(spaceId, req.user.id, createEventDto);
    
    return {
      success: true,
      data: event,
      message: 'Event created successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get events in a space' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiResponse({ status: 200, description: 'Events retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Not a member of this space' })
  async getEvents(
    @Param('spaceId') spaceId: string,
    @Query() getEventsDto: GetEventsDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.eventService.getEvents(spaceId, req.user.id, getEventsDto);
    
    return {
      success: true,
      data: result,
      message: 'Events retrieved successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Get(':eventId')
  @ApiOperation({ summary: 'Get event by ID' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'eventId', description: 'Event ID' })
  @ApiResponse({ status: 200, description: 'Event retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Event not found' })
  @ApiResponse({ status: 403, description: 'Not a member of this space' })
  async getEventById(
    @Param('spaceId') spaceId: string,
    @Param('eventId') eventId: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const event = await this.eventService.getEventById(eventId, req.user.id);
    
    return {
      success: true,
      data: event,
      message: 'Event retrieved successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Put(':eventId')
  @ApiOperation({ summary: 'Update an event' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'eventId', description: 'Event ID' })
  @ApiResponse({ status: 200, description: 'Event updated successfully' })
  @ApiResponse({ status: 404, description: 'Event not found' })
  @ApiResponse({ status: 403, description: 'Only event creator can update' })
  async updateEvent(
    @Param('spaceId') spaceId: string,
    @Param('eventId') eventId: string,
    @Body() updateEventDto: UpdateEventDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const event = await this.eventService.updateEvent(eventId, req.user.id, updateEventDto);
    
    return {
      success: true,
      data: event,
      message: 'Event updated successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Delete(':eventId')
  @ApiOperation({ summary: 'Delete an event' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'eventId', description: 'Event ID' })
  @ApiResponse({ status: 200, description: 'Event deleted successfully' })
  @ApiResponse({ status: 404, description: 'Event not found' })
  @ApiResponse({ status: 403, description: 'Only event creator can delete' })
  async deleteEvent(
    @Param('spaceId') spaceId: string,
    @Param('eventId') eventId: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    await this.eventService.deleteEvent(eventId, req.user.id);
    
    return {
      success: true,
      data: null,
      message: 'Event deleted successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Post(':eventId/responses')
  @ApiOperation({ summary: 'Respond to an event' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'eventId', description: 'Event ID' })
  @ApiResponse({ status: 201, description: 'Response created successfully' })
  @ApiResponse({ status: 404, description: 'Event not found' })
  @ApiResponse({ status: 403, description: 'Not a member of this space' })
  async respondToEvent(
    @Param('spaceId') spaceId: string,
    @Param('eventId') eventId: string,
    @Body() createEventResponseDto: CreateEventResponseDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const response = await this.eventService.respondToEvent(
      eventId,
      req.user.id,
      createEventResponseDto,
    );
    
    return {
      success: true,
      data: response,
      message: 'Response created successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Put(':eventId/responses')
  @ApiOperation({ summary: 'Update event response' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'eventId', description: 'Event ID' })
  @ApiResponse({ status: 200, description: 'Response updated successfully' })
  @ApiResponse({ status: 404, description: 'Response not found' })
  @ApiResponse({ status: 403, description: 'Can only update own response' })
  async updateEventResponse(
    @Param('spaceId') spaceId: string,
    @Param('eventId') eventId: string,
    @Body() updateEventResponseDto: UpdateEventResponseDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    const response = await this.eventService.updateEventResponse(
      eventId,
      req.user.id,
      updateEventResponseDto,
    );

    return {
      success: true,
      data: response,
      message: 'Response updated successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Delete(':eventId/responses')
  @ApiOperation({ summary: 'Delete event response' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'eventId', description: 'Event ID' })
  @ApiResponse({ status: 200, description: 'Response deleted successfully' })
  @ApiResponse({ status: 404, description: 'Response not found' })
  @ApiResponse({ status: 403, description: 'Can only delete own response' })
  async deleteEventResponse(
    @Param('spaceId') spaceId: string,
    @Param('eventId') eventId: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<any>> {
    await this.eventService.deleteEventResponse(eventId, req.user.id);

    return {
      success: true,
      data: null,
      message: 'Response deleted successfully',
      timestamp: new Date().toISOString(),
    };
  }

  // Event Date Selection Endpoints

  @Post(':eventId/date-selections/select')
  @ApiOperation({ summary: 'Select event dates' })
  @ApiResponse({ status: 200, description: 'Dates selected successfully', type: EventDateSelectionResponseDto })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'eventId', description: 'Event ID' })
  async selectEventDates(
    @Param('spaceId') spaceId: string,
    @Param('eventId') eventId: string,
    @Body() selectEventDatesDto: SelectEventDatesDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<EventDateSelectionResponseDto>> {
    await this.eventService.selectEventDates(
      eventId,
      req.user.id,
      selectEventDatesDto.dates,
    );

    const selections = await this.eventService.getUserDateSelections(eventId, req.user.id);

    return {
      success: true,
      data: selections,
      message: 'Dates selected successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Post(':eventId/date-selections/deselect')
  @ApiOperation({ summary: 'Deselect event dates' })
  @ApiResponse({ status: 200, description: 'Dates deselected successfully', type: EventDateSelectionResponseDto })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'eventId', description: 'Event ID' })
  async deselectEventDates(
    @Param('spaceId') spaceId: string,
    @Param('eventId') eventId: string,
    @Body() deselectEventDatesDto: DeselectEventDatesDto,
    @Request() req: any,
  ): Promise<ApiResponseDto<EventDateSelectionResponseDto>> {
    await this.eventService.deselectEventDates(
      eventId,
      req.user.id,
      deselectEventDatesDto.dates,
    );

    const selections = await this.eventService.getUserDateSelections(eventId, req.user.id);

    return {
      success: true,
      data: selections,
      message: 'Dates deselected successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Get(':eventId/date-selections')
  @ApiOperation({ summary: 'Get user date selections for event' })
  @ApiResponse({ status: 200, description: 'Date selections retrieved successfully', type: EventDateSelectionResponseDto })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'eventId', description: 'Event ID' })
  async getUserDateSelections(
    @Param('spaceId') spaceId: string,
    @Param('eventId') eventId: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<EventDateSelectionResponseDto>> {
    const selections = await this.eventService.getUserDateSelections(eventId, req.user.id);

    return {
      success: true,
      data: selections,
      message: 'Date selections retrieved successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Get(':eventId/date-selections/summary')
  @ApiOperation({ summary: 'Get event date selection summary' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'eventId', description: 'Event ID' })
  @ApiResponse({ status: 200, description: 'Date selection summary retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Event not found' })
  async getEventDateSelectionSummary(
    @Param('spaceId') spaceId: string,
    @Param('eventId') eventId: string,
    @Request() req: any,
  ): Promise<ApiResponseDto<EventDateSelectionSummaryDto>> {
    const summary = await this.eventService.getEventDateSelectionSummary(eventId, req.user.id);

    return {
      success: true,
      data: { summary },
      message: 'Date selection summary retrieved successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Post(':id/join')
  @ApiOperation({ summary: 'Join an event' })
  @ApiParam({ name: 'id', description: 'Event ID' })
  @ApiResponse({
    status: 200,
    description: 'Successfully joined event',
    type: Object,
  })
  async joinEvent(
    @Param('id') eventId: string,
    @Body() joinDto: JoinEventDto,
    @Req() req: any,
  ): Promise<ApiResponseDto<any>> {
    const response = await this.eventService.joinEvent(eventId, req.user.id, joinDto);

    return {
      success: true,
      data: response,
      message: 'Successfully joined event',
      timestamp: new Date().toISOString(),
    };
  }

  @Post(':id/confirm-date')
  @ApiOperation({ summary: 'Confirm event date (creator only)' })
  @ApiParam({ name: 'id', description: 'Event ID' })
  @ApiResponse({
    status: 200,
    description: 'Successfully confirmed event date',
    type: Object,
  })
  async confirmEventDate(
    @Param('id') eventId: string,
    @Body() confirmDto: ConfirmEventDateDto,
    @Req() req: any,
  ): Promise<ApiResponseDto<any>> {
    const event = await this.eventService.confirmEventDate(eventId, req.user.id, confirmDto);

    return {
      success: true,
      data: event,
      message: 'Successfully confirmed event date',
      timestamp: new Date().toISOString(),
    };
  }

  @Post(':id/suggest-dates')
  @ApiOperation({ summary: 'Suggest alternative dates for event' })
  @ApiParam({ name: 'id', description: 'Event ID' })
  @ApiResponse({
    status: 200,
    description: 'Successfully suggested alternative dates',
    type: Object,
  })
  async suggestEventDates(
    @Param('id') eventId: string,
    @Body() suggestDto: SuggestEventDatesDto,
    @Req() req: any,
  ): Promise<ApiResponseDto<any>> {
    await this.eventService.suggestEventDates(eventId, req.user.id, suggestDto);

    return {
      success: true,
      data: null,
      message: 'Successfully suggested alternative dates',
      timestamp: new Date().toISOString(),
    };
  }

  @Get(':id/participants')
  @ApiOperation({ summary: 'Get event participants' })
  @ApiParam({ name: 'id', description: 'Event ID' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved event participants',
    type: Object,
  })
  async getEventParticipants(
    @Param('id') eventId: string,
    @Req() req: any,
  ): Promise<ApiResponseDto<any>> {
    const participants = await this.eventService.getEventParticipants(eventId, req.user.id);

    return {
      success: true,
      data: participants,
      message: 'Successfully retrieved event participants',
      timestamp: new Date().toISOString(),
    };
  }

  @Get(':id/datetime-summary')
  @ApiOperation({ summary: 'Get event date/time summary (suggested vs confirmed)' })
  @ApiParam({ name: 'id', description: 'Event ID' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved event date/time summary',
    type: Object,
  })
  async getEventDateTimeSummary(
    @Param('id') eventId: string,
    @Req() req: any,
  ): Promise<ApiResponseDto<any>> {
    const summary = await this.eventService.getEventDateTimeSummary(eventId, req.user.id);

    return {
      success: true,
      data: summary,
      message: 'Successfully retrieved event date/time summary',
      timestamp: new Date().toISOString(),
    };
  }

  @Get(':id/history')
  @ApiOperation({ summary: 'Get event history' })
  @ApiParam({ name: 'id', description: 'Event ID' })
  @ApiResponse({
    status: 200,
    description: 'Event history retrieved successfully',
  })
  async getEventHistory(
    @Param('id') eventId: string,
    @Req() req: any,
  ): Promise<ApiResponseDto<any>> {
    const history = await this.eventService.getEventHistory(eventId, req.user.id);

    return {
      success: true,
      data: history,
      message: 'Successfully retrieved event history',
      timestamp: new Date().toISOString(),
    };
  }
}
