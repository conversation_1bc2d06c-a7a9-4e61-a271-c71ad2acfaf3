import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Space } from '../../entities/space.entity';
import { SpaceMember } from '../../entities/space-member.entity';
import { SpaceInvite } from '../../entities/space-invite.entity';
import { SpaceInviteUsage } from '../../entities/space-invite-usage.entity';
import { UserSpaceInvite } from '../../entities/user-space-invite.entity';
import { User } from '../../entities/user.entity';
import { Message } from '../../entities/message.entity';
import { Todo } from '../../entities/todo.entity';

import { ToGo } from '../../entities/togo.entity';
import { Tag } from '../../entities/tag.entity';
import { Split } from '../../entities/split.entity';
import { Settle } from '../../entities/settle.entity';
import { Balance } from '../balance/entities/balance.entity';
import { Event } from '../../entities/event.entity';
import { CommonModule } from '../../common/common.module';
import { UsersModule } from '../users/users.module';
import { SpacesService } from './spaces.service';
import { SpacesController } from './spaces.controller';
import { InviteCodeService } from './services/invite-code.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Space, SpaceMember, SpaceInvite, SpaceInviteUsage, UserSpaceInvite, User,
      Message, Todo, ToGo, Tag, Split, Settle, Balance, Event
    ]),
    CommonModule,
    UsersModule,
  ],
  controllers: [SpacesController],
  providers: [SpacesService, InviteCodeService],
  exports: [SpacesService, InviteCodeService],
})
export class SpacesModule {}
