import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Space } from '../../entities/space.entity';
import { SpaceMember, SpaceMemberRole } from '../../entities/space-member.entity';
import { SpaceInvite } from '../../entities/space-invite.entity';
import { SpaceInviteUsage } from '../../entities/space-invite-usage.entity';
import { UserSpaceInvite, UserSpaceInviteStatus } from '../../entities/user-space-invite.entity';
import { User } from '../../entities/user.entity';
import { Message } from '../../entities/message.entity';
import { Todo } from '../../entities/todo.entity';

import { ToGo } from '../../entities/togo.entity';
import { Tag } from '../../entities/tag.entity';
import { Split } from '../../entities/split.entity';
import { Settle } from '../../entities/settle.entity';
import { Balance } from '../balance/entities/balance.entity';
import { Event } from '../../entities/event.entity';
import { CreateSpaceDto } from './dto/create-space.dto';
import { UpdateSpaceDto } from './dto/update-space.dto';
import { CreateInviteDto } from './dto/create-invite.dto';
import { UserInviteDto, BatchUserInviteDto, UpdateSpaceInviteSettingsDto } from './dto/user-invite.dto';
import { BatchInviteDto } from './dto/batch-invite.dto';
import { InviteCodeService } from './services/invite-code.service';
import { UsersService } from '../users/users.service';
import { SpaceCurrencyChangedEvent } from './events/space-currency-changed.event';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class SpacesService {
  constructor(
    @InjectRepository(Space)
    private readonly spaceRepository: Repository<Space>,
    @InjectRepository(SpaceMember)
    private readonly spaceMemberRepository: Repository<SpaceMember>,
    @InjectRepository(SpaceInvite)
    private readonly spaceInviteRepository: Repository<SpaceInvite>,
    @InjectRepository(SpaceInviteUsage)
    private readonly spaceInviteUsageRepository: Repository<SpaceInviteUsage>,
    @InjectRepository(UserSpaceInvite)
    private readonly userSpaceInviteRepository: Repository<UserSpaceInvite>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(Todo)
    private readonly todoRepository: Repository<Todo>,
    @InjectRepository(ToGo)
    private readonly togoRepository: Repository<ToGo>,
    @InjectRepository(Tag)
    private readonly tagRepository: Repository<Tag>,
    @InjectRepository(Split)
    private readonly splitRepository: Repository<Split>,
    @InjectRepository(Settle)
    private readonly settleRepository: Repository<Settle>,
    @InjectRepository(Balance)
    private readonly balanceRepository: Repository<Balance>,
    @InjectRepository(Event)
    private readonly eventRepository: Repository<Event>,
    private readonly inviteCodeService: InviteCodeService,
    private readonly usersService: UsersService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async create(createSpaceDto: CreateSpaceDto, userId: string): Promise<Space> {
    // Generate unique invite code
    const inviteCode = await this.generateUniqueSpaceInviteCode();

    // Create space
    const space = this.spaceRepository.create({
      ...createSpaceDto,
      currency: createSpaceDto.currency || 'USD', // Default to USD if not provided
      createdById: userId,
      inviteCode,
    });
    const savedSpace = await this.spaceRepository.save(space);

    // Add creator as owner
    const spaceMember = this.spaceMemberRepository.create({
      spaceId: savedSpace.id,
      userId: userId,
      role: SpaceMemberRole.OWNER,
    });
    await this.spaceMemberRepository.save(spaceMember);

    return savedSpace;
  }

  async createPersonalSpace(userId: string, displayName: string): Promise<Space> {
    // Generate unique invite code
    const inviteCode = await this.generateUniqueSpaceInviteCode();

    // Create personal space
    const space = this.spaceRepository.create({
      name: `${displayName}'s Space`,
      description: 'My personal space for private thoughts and memories',
      isPrivate: true,
      isPersonal: true,
      createdById: userId,
      inviteCode,
    });
    const savedSpace = await this.spaceRepository.save(space);

    // Add creator as owner
    const spaceMember = this.spaceMemberRepository.create({
      spaceId: savedSpace.id,
      userId: userId,
      role: SpaceMemberRole.OWNER,
    });
    await this.spaceMemberRepository.save(spaceMember);

    return savedSpace;
  }

  async findAllForUser(userId: string): Promise<any[]> {
    // Get spaces where user is a member
    const memberSpaces = await this.spaceRepository
      .createQueryBuilder('space')
      .leftJoin('space.members', 'member')
      .leftJoinAndSelect('space.createdBy', 'creator')
      .leftJoin('space.members', 'allMembers')
      .addSelect('COUNT(DISTINCT allMembers.id)', 'memberCount')
      .where('member.userId = :userId', { userId })
      .groupBy('space.id')
      .addGroupBy('creator.id')
      .orderBy('space.isPersonal', 'DESC') // Personal spaces first
      .addOrderBy('space.createdAt', 'DESC') // Then by creation date
      .getRawAndEntities();

    // Get pending invites for the user
    const pendingInvites = await this.userSpaceInviteRepository
      .createQueryBuilder('invite')
      .leftJoinAndSelect('invite.space', 'space')
      .leftJoinAndSelect('space.createdBy', 'creator')
      .leftJoinAndSelect('invite.invitedByUser', 'invitedBy')
      .leftJoin('space.members', 'allMembers')
      .addSelect('COUNT(DISTINCT allMembers.id)', 'memberCount')
      .where('invite.invitedUserId = :userId', { userId })
      .andWhere('invite.status = :status', { status: UserSpaceInviteStatus.PENDING })
      .andWhere('invite.expiresAt > :now', { now: new Date() })
      .groupBy('space.id')
      .addGroupBy('creator.id')
      .addGroupBy('invite.id')
      .addGroupBy('invitedBy.id')
      .getRawAndEntities();

    // Transform member spaces
    const memberSpacesResult = memberSpaces.entities.map((space, index) => {
      const raw = memberSpaces.raw[index];
      return {
        ...space,
        memberCount: parseInt(raw.memberCount) || 0,
        inviteStatus: 'none',
      };
    });

    // Transform pending invite spaces
    const inviteSpacesResult = pendingInvites.entities.map((invite, index) => {
      const raw = pendingInvites.raw[index];
      return {
        ...invite.space,
        memberCount: parseInt(raw.memberCount) || 0,
        inviteStatus: 'pending',
        invitedBy: invite.invitedByUser.displayName,
        invitedAt: invite.createdAt,
      };
    });

    // Combine and sort: pending invites first, then member spaces
    return [...inviteSpacesResult, ...memberSpacesResult];
  }

  async findOne(id: string, userId: string): Promise<any> {
    // First check if user has access to this space
    const userMember = await this.spaceMemberRepository.findOne({
      where: { spaceId: id, userId },
    });

    if (!userMember) {
      throw new NotFoundException(`Space with ID ${id} not found or access denied`);
    }

    // Get space with creator info
    const space = await this.spaceRepository.findOne({
      where: { id },
      relations: ['createdBy'],
    });

    if (!space) {
      throw new NotFoundException(`Space with ID ${id} not found`);
    }

    // Calculate member count
    const memberCount = await this.spaceMemberRepository.count({
      where: { spaceId: id },
    });

    // Return space with calculated counts
    return {
      ...space,
      memberCount,
    };
  }

  async update(id: string, updateSpaceDto: UpdateSpaceDto, userId: string): Promise<Space> {
    const space = await this.findOne(id, userId);

    // Check if user is owner or admin
    const member = await this.spaceMemberRepository.findOne({
      where: { spaceId: id, userId },
    });

    if (!member || (member.role !== SpaceMemberRole.OWNER && member.role !== SpaceMemberRole.ADMIN)) {
      throw new ForbiddenException('Only space owners and admins can update space details');
    }

    // Check if currency is being changed
    const oldCurrency = space.currency;
    const newCurrency = updateSpaceDto.currency;

    Object.assign(space, updateSpaceDto);
    const updatedSpace = await this.spaceRepository.save(space);

    // If currency changed, emit event to trigger balance conversion
    if (newCurrency && oldCurrency !== newCurrency) {
      console.log(`Space ${id} currency changed from ${oldCurrency} to ${newCurrency}`);
      this.eventEmitter.emit(
        'space.currency.changed',
        new SpaceCurrencyChangedEvent(id, oldCurrency, newCurrency)
      );
    }

    return updatedSpace;
  }

  async remove(id: string, userId: string): Promise<void> {
    const space = await this.findOne(id, userId);
    
    // Check if user is owner
    const member = await this.spaceMemberRepository.findOne({
      where: { spaceId: id, userId },
    });

    if (!member || member.role !== SpaceMemberRole.OWNER) {
      throw new ForbiddenException('Only space owners can delete spaces');
    }

    await this.spaceRepository.softRemove(space);
  }

  async generateInvite(spaceId: string, userId: string): Promise<SpaceInvite> {
    const space = await this.findOne(spaceId, userId);

    // Check if space is personal (cannot invite others)
    if (space.isPersonal) {
      throw new ForbiddenException('Cannot create invites for personal spaces');
    }

    // Check if user can create invites (owner or admin)
    const member = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!member || (member.role !== SpaceMemberRole.OWNER && member.role !== SpaceMemberRole.ADMIN)) {
      throw new ForbiddenException('Only space owners and admins can create invites');
    }

    // Generate unique invite code
    const inviteCode = uuidv4().replace(/-/g, '').substring(0, 12);
    
    // Set expiration to 7 days from now
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    const invite = this.spaceInviteRepository.create({
      spaceId,
      createdById: userId,
      inviteCode,
      expiresAt,
    });

    return await this.spaceInviteRepository.save(invite);
  }

  async joinByInvite(inviteCode: string, userId: string): Promise<Space> {
    const invite = await this.spaceInviteRepository.findOne({
      where: { inviteCode, isActive: true },
      relations: ['space'],
    });

    if (!invite) {
      throw new NotFoundException('Invalid or expired invite code');
    }

    if (invite.expiresAt < new Date()) {
      throw new ForbiddenException('Invite code has expired');
    }

    if (invite.maxUses && invite.currentUses >= invite.maxUses) {
      throw new ForbiddenException('Invite code has reached maximum uses');
    }

    // Check if user is already a member
    const existingMember = await this.spaceMemberRepository.findOne({
      where: { spaceId: invite.spaceId, userId },
    });

    if (existingMember) {
      throw new ForbiddenException('You are already a member of this space');
    }

    // Add user as member
    const spaceMember = this.spaceMemberRepository.create({
      spaceId: invite.spaceId,
      userId,
      role: SpaceMemberRole.MEMBER,
    });
    await this.spaceMemberRepository.save(spaceMember);

    // Update invite usage
    invite.currentUses += 1;
    await this.spaceInviteRepository.save(invite);

    return invite.space;
  }

  async leaveSpace(spaceId: string, userId: string): Promise<void> {
    // Check if user is a member
    const member = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!member) {
      throw new NotFoundException('You are not a member of this space');
    }

    // Prevent owner from leaving (they must transfer ownership or delete the space)
    if (member.role === SpaceMemberRole.OWNER) {
      throw new ForbiddenException('Space owners cannot leave. Transfer ownership or delete the space instead.');
    }

    // Remove the member
    await this.spaceMemberRepository.remove(member);
  }

  async getSpaceMembers(spaceId: string, userId: string): Promise<any[]> {
    // Check if user has access to this space
    const userMember = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!userMember) {
      throw new ForbiddenException('You do not have access to this space');
    }

    // Get all members with user details
    const members = await this.spaceMemberRepository
      .createQueryBuilder('member')
      .leftJoinAndSelect('member.user', 'user')
      .where('member.spaceId = :spaceId', { spaceId })
      .orderBy('member.role', 'ASC') // Owner first, then admin, then member
      .addOrderBy('member.joinedAt', 'ASC')
      .getMany();

    // Get pending invites for this space
    const pendingInvites = await this.userSpaceInviteRepository
      .createQueryBuilder('invite')
      .leftJoinAndSelect('invite.invitedUser', 'invitedUser')
      .leftJoinAndSelect('invite.invitedByUser', 'invitedByUser')
      .where('invite.spaceId = :spaceId', { spaceId })
      .andWhere('invite.status = :status', { status: UserSpaceInviteStatus.PENDING })
      .andWhere('invite.expiresAt > :now', { now: new Date() })
      .orderBy('invite.createdAt', 'DESC')
      .getMany();

    // Map members
    const memberList = members.map(member => ({
      id: member.id,
      userId: member.userId,
      role: member.role,
      joinedAt: member.joinedAt,
      status: 'active',
      user: {
        id: member.user.id,
        displayName: member.user.displayName,
        email: member.user.email,
        avatarUrl: member.user.avatarUrl,
      },
    }));

    // Map pending invites
    const pendingInviteList = pendingInvites.map(invite => ({
      id: `invite-${invite.id}`,
      userId: invite.invitedUserId,
      role: 'pending',
      joinedAt: null,
      invitedAt: invite.createdAt,
      expiresAt: invite.expiresAt,
      status: 'pending',
      invitedBy: invite.invitedByUser.displayName,
      user: {
        id: invite.invitedUser.id,
        displayName: invite.invitedUser.displayName,
        email: invite.invitedUser.email,
        avatarUrl: invite.invitedUser.avatarUrl,
      },
    }));

    // Return pending invites first, then active members
    return [...pendingInviteList, ...memberList];
  }



  async removeMember(spaceId: string, targetUserId: string, requesterId: string): Promise<void> {
    // Check if requester has permission (must be owner or admin)
    const requesterMember = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId: requesterId },
    });

    if (!requesterMember || (requesterMember.role !== SpaceMemberRole.OWNER && requesterMember.role !== SpaceMemberRole.ADMIN)) {
      throw new ForbiddenException('Only space owners and admins can remove members');
    }

    // Get target member
    const targetMember = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId: targetUserId },
    });

    if (!targetMember) {
      throw new NotFoundException('Member not found in this space');
    }

    // Prevent removing owner
    if (targetMember.role === SpaceMemberRole.OWNER) {
      throw new ForbiddenException('Cannot remove space owner. Transfer ownership first.');
    }

    // Prevent admins from removing other admins (only owners can do that)
    if (targetMember.role === SpaceMemberRole.ADMIN && requesterMember.role !== SpaceMemberRole.OWNER) {
      throw new ForbiddenException('Only space owners can remove admins');
    }

    // Remove the member
    await this.spaceMemberRepository.remove(targetMember);
  }

  async updateMemberRole(
    spaceId: string,
    userId: string,
    newRole: string,
    requesterId: string,
  ): Promise<any> {
    // Validate the new role
    if (!Object.values(SpaceMemberRole).includes(newRole as SpaceMemberRole)) {
      throw new BadRequestException('Invalid role specified');
    }

    const role = newRole as SpaceMemberRole;

    // Get the space with members
    const space = await this.spaceRepository.findOne({
      where: { id: spaceId },
      relations: ['members', 'members.user'],
    });

    if (!space) {
      throw new NotFoundException('Space not found');
    }

    // Find the target member
    const targetMember = space.members.find(member => member.userId === userId);
    if (!targetMember) {
      throw new NotFoundException('Member not found in this space');
    }

    // Find the requester member
    const requesterMember = space.members.find(member => member.userId === requesterId);
    if (!requesterMember) {
      throw new ForbiddenException('You are not a member of this space');
    }

    // Check permissions - only owners can change roles
    if (requesterMember.role !== SpaceMemberRole.OWNER) {
      throw new ForbiddenException('Only space owners can change member roles');
    }

    // Prevent changing owner role
    if (targetMember.role === SpaceMemberRole.OWNER && role !== SpaceMemberRole.OWNER) {
      throw new ForbiddenException('Cannot demote space owner');
    }

    // Prevent promoting to owner (would need special handling for ownership transfer)
    if (role === SpaceMemberRole.OWNER) {
      throw new ForbiddenException('Cannot promote to owner role. Use ownership transfer instead');
    }

    // Cannot change own role
    if (targetMember.userId === requesterId) {
      throw new ForbiddenException('Cannot change your own role');
    }

    // Update the member role
    targetMember.role = role;
    await this.spaceMemberRepository.save(targetMember);

    // Return updated member information
    return {
      id: targetMember.id,
      userId: targetMember.userId,
      role: targetMember.role,
      joinedAt: targetMember.joinedAt,
      user: targetMember.user ? {
        id: targetMember.user.id,
        username: targetMember.user.username,
        email: targetMember.user.email,
        displayName: targetMember.user.displayName,
      } : null,
    };
  }

  async transferOwnership(
    spaceId: string,
    newOwnerId: string,
    currentOwnerId: string,
  ): Promise<any> {
    // Get the space with members
    const space = await this.spaceRepository.findOne({
      where: { id: spaceId },
      relations: ['members', 'members.user'],
    });

    if (!space) {
      throw new NotFoundException('Space not found');
    }

    // Find the current owner
    const currentOwner = space.members.find(member => member.userId === currentOwnerId);
    if (!currentOwner || currentOwner.role !== SpaceMemberRole.OWNER) {
      throw new ForbiddenException('Only the space owner can transfer ownership');
    }

    // Find the new owner
    const newOwner = space.members.find(member => member.userId === newOwnerId);
    if (!newOwner) {
      throw new NotFoundException('New owner must be a member of this space');
    }

    // Cannot transfer to pending member
    if (newOwner.role === SpaceMemberRole.PENDING) {
      throw new BadRequestException('Cannot transfer ownership to a pending member');
    }

    // Cannot transfer to self
    if (newOwnerId === currentOwnerId) {
      throw new BadRequestException('Cannot transfer ownership to yourself');
    }

    // Perform the transfer
    currentOwner.role = SpaceMemberRole.MEMBER; // Demote current owner to member
    newOwner.role = SpaceMemberRole.OWNER; // Promote new owner

    // Update space's createdById to reflect new ownership
    space.createdById = newOwnerId;

    // Save changes
    await this.spaceMemberRepository.save([currentOwner, newOwner]);
    await this.spaceRepository.save(space);

    return {
      spaceId,
      previousOwner: {
        id: currentOwner.user.id,
        displayName: currentOwner.user.displayName,
        newRole: currentOwner.role,
      },
      newOwner: {
        id: newOwner.user.id,
        displayName: newOwner.user.displayName,
        newRole: newOwner.role,
      },
      transferredAt: new Date(),
    };
  }

  async getSpaceStats(spaceId: string, userId: string): Promise<any> {
    // Check if user has access to this space
    const userMember = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!userMember) {
      throw new ForbiddenException('You do not have access to this space');
    }

    // Get basic space info
    const space = await this.spaceRepository.findOne({
      where: { id: spaceId },
      select: ['id', 'name', 'createdAt'],
    });

    if (!space) {
      throw new NotFoundException('Space not found');
    }

    // Get member count
    const memberCount = await this.spaceMemberRepository.count({
      where: { spaceId },
    });

    // Since posts are removed, set default values
    const postCount = 0;
    const recentPostCount = 0;
    const activeMemberCount = { count: '0' };

    // Calculate days since creation
    const daysSinceCreation = Math.floor(
      (Date.now() - space.createdAt.getTime()) / (1000 * 60 * 60 * 24)
    );

    return {
      spaceId,
      spaceName: space.name,
      memberCount,
      postCount,
      recentPostCount,
      activeMemberCount: parseInt(activeMemberCount?.count) || 0,
      daysSinceCreation,
      createdAt: space.createdAt,
    };
  }

  // Enhanced invite management methods

  async createInviteWithOptions(spaceId: string, userId: string, options: CreateInviteDto): Promise<SpaceInvite> {
    // Check if user has permission to create invites
    await this.checkSpaceAdmin(spaceId, userId);

    // Generate invite code
    const inviteCode = options.customCode || this.generateInviteCode();

    // Check if custom code is already in use
    if (options.customCode) {
      const existingInvite = await this.spaceInviteRepository.findOne({
        where: { inviteCode: options.customCode },
      });
      if (existingInvite) {
        throw new BadRequestException('Custom invite code is already in use');
      }
    }

    // Set default expiration (7 days from now)
    const expiresAt = options.expiresAt
      ? new Date(options.expiresAt)
      : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

    const invite = this.spaceInviteRepository.create({
      spaceId,
      createdById: userId,
      inviteCode,
      expiresAt,
      maxUses: options.maxUses,
      currentUses: 0,
      isActive: true,
    });

    return await this.spaceInviteRepository.save(invite);
  }

  async getSpaceInvites(spaceId: string, userId: string): Promise<any[]> {
    // Check if user has permission to view invites
    await this.checkSpaceMember(spaceId, userId);

    const invites = await this.spaceInviteRepository
      .createQueryBuilder('invite')
      .leftJoinAndSelect('invite.createdBy', 'creator')
      .leftJoinAndSelect('invite.usages', 'usage')
      .leftJoinAndSelect('usage.user', 'usageUser')
      .where('invite.spaceId = :spaceId', { spaceId })
      .orderBy('invite.createdAt', 'DESC')
      .getMany();

    return invites.map(invite => ({
      id: invite.id,
      inviteCode: invite.inviteCode,
      expiresAt: invite.expiresAt,
      maxUses: invite.maxUses,
      currentUses: invite.currentUses,
      isActive: invite.isActive,
      createdAt: invite.createdAt,
      creator: {
        id: invite.createdBy.id,
        displayName: invite.createdBy.displayName,
      },
      usages: invite.usages?.map(usage => ({
        id: usage.id,
        usedAt: usage.usedAt,
        user: {
          id: usage.user.id,
          displayName: usage.user.displayName,
        },
      })) || [],
    }));
  }

  async deactivateInvite(spaceId: string, inviteId: string, userId: string): Promise<void> {
    // Check if user has permission to manage invites
    await this.checkSpaceAdmin(spaceId, userId);

    const invite = await this.spaceInviteRepository.findOne({
      where: { id: inviteId, spaceId },
    });

    if (!invite) {
      throw new NotFoundException('Invite not found');
    }

    invite.isActive = false;
    await this.spaceInviteRepository.save(invite);
  }

  async deleteInvite(spaceId: string, inviteId: string, userId: string): Promise<void> {
    // Check if user has permission to manage invites
    await this.checkSpaceAdmin(spaceId, userId);

    const invite = await this.spaceInviteRepository.findOne({
      where: { id: inviteId, spaceId },
    });

    if (!invite) {
      throw new NotFoundException('Invite not found');
    }

    await this.spaceInviteRepository.remove(invite);
  }

  async getInviteStats(spaceId: string, userId: string): Promise<any> {
    // Check if user has permission to view stats
    await this.checkSpaceMember(spaceId, userId);

    const totalInvites = await this.spaceInviteRepository.count({
      where: { spaceId },
    });

    const activeInvites = await this.spaceInviteRepository.count({
      where: { spaceId, isActive: true },
    });

    const totalUsages = await this.spaceInviteUsageRepository.count({
      where: { spaceId },
    });

    // Get usage stats for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentUsages = await this.spaceInviteUsageRepository
      .createQueryBuilder('usage')
      .where('usage.spaceId = :spaceId', { spaceId })
      .andWhere('usage.usedAt >= :thirtyDaysAgo', { thirtyDaysAgo })
      .getCount();

    // Get top performing invites
    const topInvites = await this.spaceInviteRepository
      .createQueryBuilder('invite')
      .leftJoinAndSelect('invite.createdBy', 'creator')
      .where('invite.spaceId = :spaceId', { spaceId })
      .orderBy('invite.currentUses', 'DESC')
      .limit(5)
      .getMany();

    return {
      totalInvites,
      activeInvites,
      totalUsages,
      recentUsages,
      successRate: totalInvites > 0 ? (totalUsages / totalInvites) * 100 : 0,
      topInvites: topInvites.map(invite => ({
        id: invite.id,
        inviteCode: invite.inviteCode,
        currentUses: invite.currentUses,
        maxUses: invite.maxUses,
        creator: invite.createdBy.displayName,
      })),
    };
  }

  // Enhanced joinByInvite method with usage tracking
  async joinByInviteWithTracking(inviteCode: string, userId: string, userIp?: string, userAgent?: string): Promise<any> {
    const invite = await this.spaceInviteRepository.findOne({
      where: { inviteCode, isActive: true },
      relations: ['space'],
    });

    if (!invite) {
      throw new NotFoundException('Invalid or expired invite code');
    }

    // Check if invite is expired
    if (invite.expiresAt < new Date()) {
      throw new BadRequestException('Invite code has expired');
    }

    // Check if invite has reached max uses
    if (invite.maxUses && invite.currentUses >= invite.maxUses) {
      throw new BadRequestException('Invite code has reached maximum uses');
    }

    // Check if user is already a member
    const existingMember = await this.spaceMemberRepository.findOne({
      where: { spaceId: invite.spaceId, userId },
    });

    if (existingMember) {
      throw new BadRequestException('You are already a member of this space');
    }

    // Create space member
    const spaceMember = this.spaceMemberRepository.create({
      spaceId: invite.spaceId,
      userId,
      role: SpaceMemberRole.MEMBER,
    });

    await this.spaceMemberRepository.save(spaceMember);

    // Record invite usage
    const usage = this.spaceInviteUsageRepository.create({
      inviteId: invite.id,
      spaceId: invite.spaceId,
      userId,
      userIp,
      userAgent,
    });

    await this.spaceInviteUsageRepository.save(usage);

    // Update invite usage count
    invite.currentUses += 1;
    await this.spaceInviteRepository.save(invite);

    // Return space details
    return this.findOne(invite.spaceId, userId);
  }

  // Helper methods
  private generateInviteCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  private async generateUniqueSpaceInviteCode(): Promise<string> {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      let code = '';
      for (let i = 0; i < 8; i++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length));
      }

      // Check if code already exists in spaces
      const existing = await this.spaceRepository.findOne({
        where: { inviteCode: code },
      });

      if (!existing) {
        return code;
      }

      attempts++;
    }

    throw new Error('Unable to generate unique space invite code');
  }

  async joinBySpaceInviteCode(inviteCode: string, userId: string): Promise<Space> {
    // Find space by invite code
    const space = await this.spaceRepository.findOne({
      where: { inviteCode },
      relations: ['members', 'members.user'],
    });

    if (!space) {
      throw new NotFoundException('Invalid invite code');
    }

    // Check if space is personal (cannot join personal spaces)
    if (space.isPersonal) {
      throw new ForbiddenException('Cannot join personal spaces');
    }

    // Check if user is already a member
    const existingMember = await this.spaceMemberRepository.findOne({
      where: { spaceId: space.id, userId },
    });

    if (existingMember) {
      throw new BadRequestException('You are already a member of this space');
    }

    // Add user as member
    const spaceMember = this.spaceMemberRepository.create({
      spaceId: space.id,
      userId,
      role: SpaceMemberRole.MEMBER,
    });
    await this.spaceMemberRepository.save(spaceMember);

    // Return space details
    return this.findOne(space.id, userId);
  }

  async getSpaceInfoByInviteCode(inviteCode: string): Promise<any> {
    // Find space by invite code
    const space = await this.spaceRepository.findOne({
      where: { inviteCode },
      relations: ['createdBy', 'members'],
    });

    if (!space) {
      throw new NotFoundException('Invalid invite code');
    }

    // Check if space is personal (cannot join personal spaces)
    if (space.isPersonal) {
      throw new ForbiddenException('Cannot join personal spaces');
    }

    return {
      space: {
        id: space.id,
        name: space.name,
        description: space.description,
        isPrivate: space.isPrivate,
        memberCount: space.members.length,
        createdAt: space.createdAt,
      },
    };
  }

  private async checkSpaceAdmin(spaceId: string, userId: string): Promise<void> {
    const member = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!member) {
      throw new ForbiddenException('You are not a member of this space');
    }

    if (member.role !== SpaceMemberRole.OWNER && member.role !== SpaceMemberRole.ADMIN) {
      throw new ForbiddenException('Only space owners and admins can perform this action');
    }
  }

  private async checkSpaceMember(spaceId: string, userId: string): Promise<void> {
    const member = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!member) {
      throw new ForbiddenException('You are not a member of this space');
    }
  }

  // Additional invite management methods

  async findInviteById(inviteId: string): Promise<SpaceInvite> {
    const invite = await this.spaceInviteRepository.findOne({
      where: { id: inviteId },
    });

    if (!invite) {
      throw new NotFoundException('Invite not found');
    }

    return invite;
  }

  async getInviteAnalytics(inviteId: string, userId: string): Promise<any> {
    const invite = await this.findInviteById(inviteId);

    // Check if user has permission to view analytics
    await this.checkSpaceMember(invite.spaceId, userId);

    return await this.inviteCodeService.getInviteAnalytics(inviteId);
  }

  async bulkDeactivateInvites(spaceId: string, inviteIds: string[], userId: string): Promise<void> {
    // Check if user has permission to manage invites
    await this.checkSpaceAdmin(spaceId, userId);

    // Verify all invites belong to the space
    const invites = await this.spaceInviteRepository.find({
      where: { id: In(inviteIds) },
    });

    const invalidInvites = invites.filter(invite => invite.spaceId !== spaceId);
    if (invalidInvites.length > 0) {
      throw new BadRequestException('Some invites do not belong to this space');
    }

    await this.inviteCodeService.bulkDeactivateInvites(inviteIds);
  }

  async getInviteInfo(inviteCode: string): Promise<any> {
    const invite = await this.inviteCodeService.getValidInvite(inviteCode);

    if (!invite) {
      throw new NotFoundException('Invalid or expired invite code');
    }

    return {
      inviteCode: invite.inviteCode,
      space: {
        id: invite.space.id,
        name: invite.space.name,
        description: invite.space.description,
        isPrivate: invite.space.isPrivate,
        memberCount: await this.spaceMemberRepository.count({
          where: { spaceId: invite.spaceId },
        }),
      },
      creator: {
        displayName: invite.createdBy.displayName,
      },
      expiresAt: invite.expiresAt,
      maxUses: invite.maxUses,
      currentUses: invite.currentUses,
    };
  }

  async getInviteQRCode(spaceId: string, inviteId: string, userId: string): Promise<string> {
    // Check if user has permission to view invite
    await this.checkSpaceMember(spaceId, userId);

    const invite = await this.findInviteById(inviteId);

    if (invite.spaceId !== spaceId) {
      throw new BadRequestException('Invite does not belong to this space');
    }

    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    return this.inviteCodeService.generateQRCodeData(invite.inviteCode, baseUrl);
  }

  // User invitation methods

  async inviteUserByEmail(spaceId: string, inviterUserId: string, userInviteDto: UserInviteDto): Promise<any> {
    // Check permissions
    await this.checkInvitePermissions(spaceId, inviterUserId);

    if (!userInviteDto.email && !userInviteDto.username) {
      throw new BadRequestException('Either email or username must be provided');
    }

    let targetUser: any = null;

    // Find user by email or username
    if (userInviteDto.email) {
      targetUser = await this.usersService.findByEmail(userInviteDto.email);
    } else if (userInviteDto.username) {
      targetUser = await this.usersService.findByUsername(userInviteDto.username);
    }

    if (!targetUser) {
      throw new NotFoundException('User not found');
    }

    // Check if user is already a member
    const existingMember = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId: targetUser.id },
    });

    if (existingMember) {
      throw new BadRequestException('User is already a member of this space');
    }

    // Check if there's already an invitation (any status)
    const existingInvite = await this.userSpaceInviteRepository.findOne({
      where: {
        spaceId,
        invitedUserId: targetUser.id,
      },
      order: { createdAt: 'DESC' }, // Get the most recent invite
    });

    if (existingInvite) {
      // If there's a pending invitation that's not expired, reject
      if (existingInvite.status === UserSpaceInviteStatus.PENDING && !existingInvite.isExpired) {
        throw new BadRequestException('User already has a pending invitation to this space');
      }

      // If there's an existing invite (expired, declined, or accepted), update it to a new pending invite
      existingInvite.status = UserSpaceInviteStatus.PENDING;
      existingInvite.expiresAt = new Date();
      existingInvite.expiresAt.setDate(existingInvite.expiresAt.getDate() + 7);
      existingInvite.invitedByUserId = inviterUserId;
      existingInvite.respondedAt = null;
      await this.userSpaceInviteRepository.save(existingInvite);

      return {
        success: true,
        message: `Invitation sent to ${targetUser.displayName}`,
        user: {
          id: targetUser.id,
          email: targetUser.email,
          username: targetUser.username,
          displayName: targetUser.displayName,
        },
        invite: {
          id: existingInvite.id,
          status: existingInvite.status,
          expiresAt: existingInvite.expiresAt,
        },
      };
    }

    // Get space info
    const space = await this.findOne(spaceId, inviterUserId);

    // Create a pending invitation instead of adding user directly
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // Expires in 7 days

    const userSpaceInvite = this.userSpaceInviteRepository.create({
      spaceId,
      invitedUserId: targetUser.id,
      invitedByUserId: inviterUserId,
      status: UserSpaceInviteStatus.PENDING,
      expiresAt,
    });

    await this.userSpaceInviteRepository.save(userSpaceInvite);

    return {
      success: true,
      message: `Invitation sent to ${targetUser.displayName}`,
      user: {
        id: targetUser.id,
        email: targetUser.email,
        username: targetUser.username,
        displayName: targetUser.displayName,
      },
      invite: {
        id: userSpaceInvite.id,
        status: userSpaceInvite.status,
        expiresAt: userSpaceInvite.expiresAt,
      },
    };
  }

  async cancelUserInvite(spaceId: string, inviteId: string, requesterId: string): Promise<void> {
    // Check permissions
    await this.checkInvitePermissions(spaceId, requesterId);

    // Find the invite
    const invite = await this.userSpaceInviteRepository.findOne({
      where: {
        id: inviteId,
        spaceId,
        status: UserSpaceInviteStatus.PENDING
      },
    });

    if (!invite) {
      throw new NotFoundException('Pending invitation not found');
    }

    // Update status to cancelled
    invite.status = UserSpaceInviteStatus.DECLINED;
    await this.userSpaceInviteRepository.save(invite);
  }

  async acceptSpaceInvite(spaceId: string, userId: string): Promise<any> {
    // Find the pending invite
    const invite = await this.userSpaceInviteRepository.findOne({
      where: {
        spaceId,
        invitedUserId: userId,
        status: UserSpaceInviteStatus.PENDING,
      },
      relations: ['space'],
    });

    if (!invite) {
      throw new NotFoundException('No pending invitation found for this space');
    }

    if (invite.isExpired) {
      throw new BadRequestException('Invitation has expired');
    }

    // Check if user is already a member
    const existingMember = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (existingMember) {
      throw new BadRequestException('You are already a member of this space');
    }

    // Add user as member
    const spaceMember = this.spaceMemberRepository.create({
      spaceId,
      userId,
      role: SpaceMemberRole.MEMBER,
    });
    await this.spaceMemberRepository.save(spaceMember);

    // Update invite status
    invite.status = UserSpaceInviteStatus.ACCEPTED;
    invite.respondedAt = new Date();
    await this.userSpaceInviteRepository.save(invite);

    // Get the complete space information after joining
    const completeSpace = await this.spaceRepository.findOne({
      where: { id: spaceId },
      relations: ['createdBy', 'members'],
    });

    if (!completeSpace) {
      throw new NotFoundException('Space not found after joining');
    }

    // Calculate member count (posts removed)
    const memberCount = completeSpace.members.length;
    const postCount = 0;

    return {
      success: true,
      message: `Successfully joined ${completeSpace.name}`,
      space: {
        id: completeSpace.id,
        name: completeSpace.name,
        description: completeSpace.description,
        isPrivate: completeSpace.isPrivate,
        isPersonal: completeSpace.isPersonal,
        createdById: completeSpace.createdById,
        createdBy: completeSpace.createdById, // For compatibility
        createdAt: completeSpace.createdAt,
        updatedAt: completeSpace.updatedAt,
        memberCount,
        postCount,
        inviteStatus: 'none', // User is now a member
        invitedBy: null,
        invitedAt: null,
      },
    };
  }

  async declineSpaceInvite(spaceId: string, userId: string): Promise<any> {
    // Find the pending invite
    const invite = await this.userSpaceInviteRepository.findOne({
      where: {
        spaceId,
        invitedUserId: userId,
        status: UserSpaceInviteStatus.PENDING,
      },
      relations: ['space'],
    });

    if (!invite) {
      throw new NotFoundException('No pending invitation found for this space');
    }

    // Update invite status
    invite.status = UserSpaceInviteStatus.DECLINED;
    invite.respondedAt = new Date();
    await this.userSpaceInviteRepository.save(invite);

    return {
      success: true,
      message: `Declined invitation to ${invite.space.name}`,
    };
  }

  async batchInviteUsers(spaceId: string, inviterUserId: string, batchInvite: BatchUserInviteDto): Promise<any> {
    // Check permissions
    await this.checkInvitePermissions(spaceId, inviterUserId);

    const results = [];
    const errors = [];

    for (const userInvite of batchInvite.invites) {
      try {
        // Use default message if not provided
        const inviteWithMessage = {
          ...userInvite,
          message: userInvite.message || batchInvite.defaultMessage,
        };

        const result = await this.inviteUserByEmail(spaceId, inviterUserId, inviteWithMessage);
        results.push(result);
      } catch (error) {
        errors.push({
          email: userInvite.email,
          username: userInvite.username,
          error: error.message,
        });
      }
    }

    return {
      success: true,
      results,
      errors,
      summary: {
        total: batchInvite.invites.length,
        successful: results.length,
        failed: errors.length,
      },
    };
  }

  async updateInviteSettings(spaceId: string, userId: string, settings: UpdateSpaceInviteSettingsDto): Promise<Space> {
    // Check if user has admin permissions
    await this.checkSpaceAdmin(spaceId, userId);

    const space = await this.spaceRepository.findOne({
      where: { id: spaceId },
    });

    if (!space) {
      throw new NotFoundException('Space not found');
    }

    // Update settings
    if (settings.allowMemberInvites !== undefined) {
      space.allowMemberInvites = settings.allowMemberInvites;
    }

    if (settings.requireApproval !== undefined) {
      space.requireApproval = settings.requireApproval;
    }

    if (settings.customInviteCode) {
      // Check if custom code is already in use
      const existingSpace = await this.spaceRepository.findOne({
        where: { inviteCode: settings.customInviteCode },
      });

      if (existingSpace && existingSpace.id !== spaceId) {
        throw new BadRequestException('Custom invite code is already in use');
      }

      space.inviteCode = settings.customInviteCode;
    }

    return await this.spaceRepository.save(space);
  }

  async getSpaceInviteSettings(spaceId: string, userId: string): Promise<any> {
    // Check if user is a member
    await this.checkSpaceMember(spaceId, userId);

    const space = await this.spaceRepository.findOne({
      where: { id: spaceId },
    });

    if (!space) {
      throw new NotFoundException('Space not found');
    }

    return {
      inviteCode: space.inviteCode,
      allowMemberInvites: space.allowMemberInvites,
      requireApproval: space.requireApproval,
      inviteUrl: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/invite/${space.inviteCode}`,
    };
  }

  private async checkInvitePermissions(spaceId: string, userId: string): Promise<void> {
    const space = await this.spaceRepository.findOne({
      where: { id: spaceId },
    });

    if (!space) {
      throw new NotFoundException('Space not found');
    }

    const member = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!member) {
      throw new ForbiddenException('You are not a member of this space');
    }

    // Check if user has permission to invite
    const isAdmin = member.role === SpaceMemberRole.OWNER || member.role === SpaceMemberRole.ADMIN;
    const canInvite = isAdmin || space.allowMemberInvites;

    if (!canInvite) {
      throw new ForbiddenException('You do not have permission to invite users to this space');
    }
  }

  async clearAllSpaceData(spaceId: string, userId: string): Promise<{ deletedCounts: Record<string, number> }> {
    // Check if user is the space owner
    const space = await this.spaceRepository.findOne({
      where: { id: spaceId },
    });

    if (!space) {
      throw new NotFoundException('Space not found');
    }

    const member = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!member) {
      throw new ForbiddenException('You must be a member of this space to clear data');
    }

    console.log(`🧹 Starting to clear all data for space: ${spaceId}`);

    const deletedCounts: Record<string, number> = {};

    try {
      // Delete in order to respect foreign key constraints

      // First, get all todo IDs for this space
      const todos = await this.todoRepository.find({
        where: { spaceId },
        select: ['id']
      });
      const todoIds = todos.map(todo => todo.id);
      console.log(`📋 Found ${todoIds.length} todos to clean up`);

      // Note: TodoTags and TodoParticipants are now stored as JSON in todos table
      // No need to delete from separate relation tables
      console.log(`📋 Found ${todoIds.length} todos with embedded tags and participants data`);

      // 3. Delete Todos
      const todosResult = await this.todoRepository.delete({ spaceId });
      deletedCounts.todos = todosResult.affected || 0;
      console.log(`✅ Deleted ${deletedCounts.todos} todos`);

      // 4. Delete ToGos
      const togosResult = await this.togoRepository.delete({ spaceId });
      deletedCounts.togos = togosResult.affected || 0;
      console.log(`✅ Deleted ${deletedCounts.togos} togos`);

      // 5. Delete Settles (references splits)
      const settlesResult = await this.settleRepository.delete({ spaceId });
      deletedCounts.settles = settlesResult.affected || 0;
      console.log(`✅ Deleted ${deletedCounts.settles} settles`);

      // 6. Delete Splits
      const splitsResult = await this.splitRepository.delete({ spaceId });
      deletedCounts.splits = splitsResult.affected || 0;
      console.log(`✅ Deleted ${deletedCounts.splits} splits`);

      // 7. Delete Balances
      const balancesResult = await this.balanceRepository.delete({ spaceId });
      deletedCounts.balances = balancesResult.affected || 0;
      console.log(`✅ Deleted ${deletedCounts.balances} balances`);

      // 8. Delete Event Responses (first get event IDs, then delete responses)
      const events = await this.eventRepository.find({
        where: { spaceId },
        select: ['id'],
      });
      const eventIds = events.map(event => event.id);

      // Event responses are now integrated into events table as JSON, no separate deletion needed
      deletedCounts.eventResponses = 0;
      console.log(`✅ Event responses are integrated into events table`);

      // 9. Delete Events
      const eventsResult = await this.eventRepository.delete({ spaceId });
      deletedCounts.events = eventsResult.affected || 0;
      console.log(`✅ Deleted ${deletedCounts.events} events`);

      // 10. Delete Messages
      const messagesResult = await this.messageRepository.delete({ spaceId });
      deletedCounts.messages = messagesResult.affected || 0;
      console.log(`✅ Deleted ${deletedCounts.messages} messages`);

      // 11. Delete Tags (space-specific tags)
      const tagsResult = await this.tagRepository.delete({ spaceId });
      deletedCounts.tags = tagsResult.affected || 0;
      console.log(`✅ Deleted ${deletedCounts.tags} tags`);

      console.log(`🎉 Successfully cleared all data for space: ${spaceId}`, deletedCounts);

      return { deletedCounts };
    } catch (error) {
      console.error(`❌ Error clearing space data:`, error);
      throw new BadRequestException(`Failed to clear space data: ${error.message}`);
    }
  }
}
