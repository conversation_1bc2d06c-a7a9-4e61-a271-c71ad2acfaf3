import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { <PERSON>t<PERSON>ontroller } from './chat.controller';
import { ChatService } from './chat.service';
import { ChatGateway } from './chat.gateway';
import { Message } from '../../entities/message.entity';
import { Space } from '../../entities/space.entity';
import { SpaceMember } from '../../entities/space-member.entity';
import { User } from '../../entities/user.entity';
import { Split } from '../../entities/split.entity';
import { Settle } from '../../entities/settle.entity';
import { Todo } from '../../entities/todo.entity';
import { Tag } from '../../entities/tag.entity';
import { ToGo } from '../../entities/togo.entity';
import { Event } from '../../entities/event.entity';
import { BalanceModule } from '../balance/balance.module';
import { TodoModule } from '../todo/todo.module';
import { TagModule } from '../tag/tag.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Message, Space, SpaceMember, User, Split, Settle, Todo, Tag, ToGo, Event]),
    BalanceModule,
    forwardRef(() => TodoModule),
    TagModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('jwt.secret'),
        signOptions: {
          expiresIn: configService.get<string>('jwt.expiresIn'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [ChatController],
  providers: [
    ChatService,
    ChatGateway,
    {
      provide: 'JWT_CONFIG',
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('jwt.secret'),
        expiresIn: configService.get<string>('jwt.expiresIn'),
      }),
      inject: [ConfigService],
    },
  ],
  exports: [ChatService, ChatGateway],
})
export class ChatModule {}
