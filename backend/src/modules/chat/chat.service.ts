import { Injectable, NotFoundException, ForbiddenException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, <PERSON><PERSON><PERSON>, <PERSON><PERSON>han, In } from 'typeorm';
import { Message, MessageType } from '../../entities/message.entity';
import { Space } from '../../entities/space.entity';
import { SpaceMember } from '../../entities/space-member.entity';
import { User } from '../../entities/user.entity';
import { Split } from '../../entities/split.entity';
import { Settle } from '../../entities/settle.entity';
import { Todo, TodoStatus } from '../../entities/todo.entity';
import { Tag } from '../../entities/tag.entity';
import { ToGo } from '../../entities/togo.entity';
import { Event } from '../../entities/event.entity';
import { CreateMessageDto } from './dto/create-message.dto';
import { UpdateMessageDto } from './dto/update-message.dto';
import { GetMessagesDto } from './dto/get-messages.dto';
import { BalanceService } from '../balance/balance.service';
import { TagService } from '../tag/tag.service';


@Injectable()
export class ChatService {
  constructor(
    @InjectRepository(Message)
    private messageRepository: Repository<Message>,
    @InjectRepository(Space)
    private spaceRepository: Repository<Space>,
    @InjectRepository(SpaceMember)
    private spaceMemberRepository: Repository<SpaceMember>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Split)
    private splitRepository: Repository<Split>,
    @InjectRepository(Settle)
    private settleRepository: Repository<Settle>,
    @InjectRepository(Todo)
    private todoRepository: Repository<Todo>,
    @InjectRepository(Tag)
    private tagRepository: Repository<Tag>,
    @InjectRepository(ToGo)
    private togoRepository: Repository<ToGo>,
    @InjectRepository(Event)
    private eventRepository: Repository<Event>,

    private balanceService: BalanceService,
    private tagService: TagService,
  ) {}

  async createMessage(
    spaceId: string,
    userId: string,
    createMessageDto: CreateMessageDto,
  ): Promise<Message> {
    // Check if user is a member of the space
    await this.checkSpaceMembership(spaceId, userId);

    // Handle poll, dice, lucky draw, split, todo, and togo message metadata
    let metadata = createMessageDto.metadata;
    if ((createMessageDto.type === MessageType.POLL ||
         createMessageDto.type === MessageType.DICE ||
         createMessageDto.type === MessageType.LUCKY_DRAW ||
         createMessageDto.type === MessageType.SPLIT ||
         createMessageDto.type === MessageType.TODO ||
         createMessageDto.type === MessageType.TOGO ||
         createMessageDto.type === MessageType.EVENT) && metadata) {
      // Set the creatorId for poll/dice/lucky draw/todo data
      metadata = {
        ...metadata,
        creatorId: userId,
      };

      // For invite-only lucky draws, auto-add invited users to participants

      if (createMessageDto.type === MessageType.LUCKY_DRAW &&
          metadata.isInviteOnly &&
          metadata.invitedUserIds &&
          metadata.invitedUserIds.length > 0) {

        const participants = metadata.participants || [];

        // Create a Set to track added users and prevent duplicates
        const addedUserIds = new Set();
        const finalParticipants = [];

        // Add all invited users (this includes creator if they're in the invited list)
        for (const invitedUserId of metadata.invitedUserIds) {
          if (!addedUserIds.has(invitedUserId)) {
            const invitedUser = await this.userRepository.findOne({
              where: { id: invitedUserId },
            });
            if (invitedUser) {
              finalParticipants.push({
                userId: invitedUser.id,
                userName: invitedUser.displayName,
                joinedAt: new Date().toISOString(),
              });
              addedUserIds.add(invitedUser.id);
            }
          }
        }

        metadata.participants = finalParticipants;
      }

      // Handle split message metadata
      if (createMessageDto.type === MessageType.SPLIT && metadata) {
        // Get paid by user information
        if (metadata.paidByUserId) {
          const paidByUser = await this.userRepository.findOne({
            where: { id: metadata.paidByUserId },
          });
          if (paidByUser) {
            metadata.paidByUserName = paidByUser.displayName;
          }
        }

        // Process participants
        if (metadata.participantUserIds && Array.isArray(metadata.participantUserIds)) {
          const participants = [];
          const totalAmount = metadata.totalAmount || 0;
          const splitType = metadata.splitType || 'equal';
          const customAmounts = metadata.customAmounts || {};

          for (const participantUserId of metadata.participantUserIds) {
            const participant = await this.userRepository.findOne({
              where: { id: participantUserId },
            });

            if (participant) {
              let amount = 0;

              // Calculate amount based on split type
              if (splitType === 'equal') {
                amount = totalAmount / metadata.participantUserIds.length;
              } else if (splitType === 'custom' && customAmounts[participantUserId]) {
                amount = customAmounts[participantUserId];
              }

              // If this participant is the payer, mark as paid
              const isPayer = participant.id === metadata.paidByUserId;

              participants.push({
                userId: participant.id,
                userName: participant.displayName,
                amount: amount,
                isPaid: isPayer, // Payer is automatically marked as paid
                paidAt: isPayer ? new Date() : null,
              });
            }
          }

          metadata.participants = participants;
        }
      }
    }

    const message = this.messageRepository.create({
      spaceId,
      senderId: userId,
      ...createMessageDto,
      metadata,
    });

    try {
      const savedMessage = await this.messageRepository.save(message);

      // Update balance if this is a split message
      if (savedMessage.type === MessageType.SPLIT && savedMessage.metadata) {
        const splitData = savedMessage.metadata as any;
        if (splitData.paidByUserId && splitData.paidByUserName && splitData.participants) {
          // First create the split record in the splits table
          const splitRecord = await this.balanceService.createSplitRecord({
            spaceId,
            messageId: savedMessage.id,
            creatorId: userId,
            paidByUserId: splitData.paidByUserId,
            paidByUserName: splitData.paidByUserName,
            title: splitData.title || 'Split',
            description: splitData.description,
            category: splitData.category || 'Other',
            splitType: splitData.splitType || 'equal',
            totalAmount: splitData.totalAmount,
            originalCurrency: splitData.currency || 'USD',
            participants: splitData.participants,
            date: new Date(),
          });

          // Then update balance records with the split ID
          await this.balanceService.updateBalanceFromSplit(
            spaceId,
            splitData.paidByUserId,
            splitData.paidByUserName,
            splitData.participants,
            splitData.currency, // Pass the currency from split data
            splitRecord.id, // Pass the split ID
          );
        }
      }

      // Update balance if this is a settle message
      if (savedMessage.type === MessageType.SETTLE && savedMessage.metadata) {
        console.log('🔄 Processing SETTLE message:', savedMessage.id, savedMessage.metadata);
        const settleData = savedMessage.metadata as any;
        if (settleData.fromUserId && settleData.toUserId && settleData.amount) {
          console.log('✅ Creating settle record from message:', {
            messageId: savedMessage.id,
            fromUserId: settleData.fromUserId,
            toUserId: settleData.toUserId,
            amount: settleData.amount,
          });
          // Create settle record and balance record
          await this.balanceService.createSettleFromMessage({
            spaceId,
            messageId: savedMessage.id,
            fromUserId: settleData.fromUserId,
            fromUserName: settleData.fromUserName,
            toUserId: settleData.toUserId,
            toUserName: settleData.toUserName,
            amount: settleData.amount,
            currency: settleData.currency || 'USD',
            description: settleData.description,
          });
          console.log('✅ Settle record created successfully');
        } else {
          console.warn('⚠️ Missing required settle data:', settleData);
        }
      }

      // Create todo record if this is a todo message
      if (savedMessage.type === MessageType.TODO && savedMessage.metadata) {
        const todoData = savedMessage.metadata as any;

        try {
          // Create the todo record
          const todo = this.todoRepository.create({
            spaceId,
            messageId: savedMessage.id,
            creatorId: userId,
            title: todoData.title || savedMessage.content,
            details: todoData.details,
            dueDate: todoData.dueDate ? new Date(todoData.dueDate) : undefined,
            location: todoData.location,
            status: TodoStatus.PENDING,
          });

          const savedTodo = await this.todoRepository.save(todo);

          // Handle tags if provided - store tag IDs directly in todos table
          if (todoData.tags && todoData.tags.length > 0) {
            try {
              const tags = await this.tagService.getOrCreateTags(
                spaceId,
                userId,
                todoData.tags,
              );
              console.log('✅ Tags retrieved/created:', tags.map(t => ({ id: t.id, name: t.name })));

              // Store tag IDs as JSON array directly in the todos table
              const tagIds = tags.map(tag => tag.id);
              savedTodo.tagIds = JSON.stringify(tagIds);
              await this.todoRepository.save(savedTodo);
              console.log('✅ Tag IDs stored in todos table:', tagIds);
            } catch (error) {
              console.error('❌ Error processing tags in ChatService:', error);
            }
          }

          // Create participants if provided - store participant data directly in todos table
          if (todoData.participantUserIds && todoData.participantUserIds.length > 0) {
            const participantsData = [];

            for (const participantUserId of todoData.participantUserIds) {
              const user = await this.userRepository.findOne({
                where: { id: participantUserId },
              });

              if (user) {
                participantsData.push({
                  userId: user.id,
                  userName: user.displayName,
                  role: 'assignee',
                  isNotified: true,
                });
              }
            }

            if (participantsData.length > 0) {
              savedTodo.participantData = JSON.stringify(participantsData);
              await this.todoRepository.save(savedTodo);
              console.log('✅ Todo participants stored:', participantsData.length);
            }
          }
        } catch (error) {
          console.error('❌ Failed to create todo record:', error);
          // Don't throw error to prevent message creation failure
        }
      }

      // Create togo record if this is a togo message
      if (savedMessage.type === MessageType.TOGO && savedMessage.metadata) {
        const togoData = savedMessage.metadata as any;

        try {
          // Create the togo record
          const togo = this.togoRepository.create({
            spaceId,
            messageId: savedMessage.id,
            creatorId: userId,
            name: togoData.name || savedMessage.content,
            description: togoData.description,
            address: togoData.address,
            latitude: togoData.latitude,
            longitude: togoData.longitude,
          });

          const savedTogo = await this.togoRepository.save(togo);

          // Handle tags if provided - store tag IDs directly in togos table
          if (togoData.tags && togoData.tags.length > 0) {
            try {
              const tags = await this.tagService.getOrCreateTags(
                spaceId,
                userId,
                togoData.tags,
              );

              // Store tag IDs as JSON array in the togos table
              const tagIds = tags.map(tag => tag.id);
              savedTogo.tagIds = JSON.stringify(tagIds);
              await this.togoRepository.save(savedTogo);
              console.log('✅ Tag IDs stored in togos table:', tagIds);
            } catch (error) {
              console.error('❌ Error processing tags in ChatService:', error);
            }
          }

          console.log('✅ ToGo record created:', savedTogo.id);
        } catch (error) {
          console.error('❌ Failed to create togo record:', error);
          // Don't throw error to prevent message creation failure
        }
      }

      // Create event record if this is an event message
      if (savedMessage.type === MessageType.EVENT && savedMessage.metadata) {
        const eventData = savedMessage.metadata as any;

        try {
          // Create the event record
          const event = this.eventRepository.create({
            spaceId,
            messageId: savedMessage.id,
            creatorId: userId,
            title: eventData.title || savedMessage.content,
            description: eventData.description,
            eventDates: eventData.eventDates || [],
            eventTime: eventData.eventTime,
            location: eventData.location,
            metadata: eventData.metadata,
            tagIds: eventData.tags ? JSON.stringify(eventData.tags) : null,
            linkedTodoId: eventData.linkedTodoId,
            linkedToGoId: eventData.linkedToGoId,
          });

          const savedEvent = await this.eventRepository.save(event);
          console.log('✅ Event record created:', savedEvent.id);

          // Get user information for the event
          const creator = await this.userRepository.findOne({
            where: { id: userId },
            select: ['id', 'displayName'],
          });

          // Update message metadata with complete event data
          const completeEventData = {
            id: savedEvent.id,
            spaceId: savedEvent.spaceId,
            messageId: savedEvent.messageId,
            creatorId: savedEvent.creatorId,
            creatorName: creator?.displayName || 'Unknown',
            title: savedEvent.title,
            description: savedEvent.description,
            eventDates: savedEvent.eventDates,
            eventTime: savedEvent.eventTime,
            location: savedEvent.location,
            linkedTodoId: savedEvent.linkedTodoId,
            linkedToGoId: savedEvent.linkedToGoId,
            tags: eventData.tags || [],
            confirmedDate: savedEvent.confirmedDate,
            confirmedTime: savedEvent.confirmedTime,
            status: savedEvent.status,
            createdAt: savedEvent.createdAt.toISOString(),
            updatedAt: savedEvent.updatedAt.toISOString(),
            responses: [],
          };

          // Update the message metadata
          await this.messageRepository.update(savedMessage.id, {
            metadata: completeEventData as any,
          });

          console.log('✅ Message metadata updated with complete event data');
        } catch (error) {
          console.error('❌ Failed to create event record:', error);
          // Don't throw error to prevent message creation failure
        }
      }

      // Return message with sender information
      const fullMessage = await this.messageRepository.findOne({
        where: { id: savedMessage.id },
        relations: ['sender', 'replyTo', 'replyTo.sender', 'todo', 'event'],
      });

      // If message has a todo, load its tags
      if (fullMessage && fullMessage.todo) {
        try {
          await this.loadTagsForTodo(fullMessage.todo);
        } catch (error) {
          console.warn('Failed to load todo tags for message:', error);
        }
      }

      return fullMessage;
    } catch (error) {
      console.error(`[ERROR] Failed to save message:`, error);
      throw error;
    }
  }

  async getMessages(
    spaceId: string,
    userId: string,
    getMessagesDto: GetMessagesDto,
  ): Promise<{ messages: Message[]; total: number }> {
    // Check if user is a member of the space
    await this.checkSpaceMembership(spaceId, userId);

    const { limit, offset, before, after, types } = getMessagesDto;

    const whereConditions: any = { spaceId };

    if (before) {
      whereConditions.createdAt = LessThan(new Date(before));
    }

    if (after) {
      whereConditions.createdAt = MoreThan(new Date(after));
    }

    // Filter by message types if specified
    if (types) {
      const typeArray = types.split(',').map(type => type.trim());
      whereConditions.type = In(typeArray);
    }

    // For initial load (offset=0), get the latest messages
    // For pagination (offset>0), get older messages
    const isInitialLoad = offset === 0 && !before && !after;

    if (isInitialLoad) {
      // Initial load: get the latest messages first, then reverse to chronological order
      const findOptions: FindManyOptions<Message> = {
        where: whereConditions,
        relations: ['sender', 'replyTo', 'replyTo.sender', 'todo', 'event'],
        order: { createdAt: 'DESC' }, // Get newest messages first
        take: limit,
        withDeleted: true, // Include soft-deleted messages
      };

      const [messages, total] = await this.messageRepository.findAndCount(findOptions);

      // Load tags for todos in messages
      await this.loadTagsForTodoMessages(messages, userId);

      // Reverse to chronological order (oldest first) for frontend display
      const messagesWithDeletedFlag = messages.reverse().map(message => ({
        ...message,
        isDeleted: !!message.deletedAt,
      }));

      return {
        messages: messagesWithDeletedFlag,
        total,
      };
    } else {
      // Pagination: get older messages (before a specific timestamp)
      const findOptions: FindManyOptions<Message> = {
        where: whereConditions,
        relations: ['sender', 'replyTo', 'replyTo.sender', 'todo', 'event'],
        order: { createdAt: 'DESC' }, // Get messages in descending order first
        take: limit,
        skip: offset,
        withDeleted: true, // Include soft-deleted messages
      };

      const [messages, total] = await this.messageRepository.findAndCount(findOptions);

      // Load tags for todos in messages
      await this.loadTagsForTodoMessages(messages, userId);

      // If using 'before' parameter, reverse to chronological order
      // If using offset, keep DESC order and reverse
      const finalMessages = before ? messages.reverse() : messages.reverse();
      const messagesWithDeletedFlag = finalMessages.map(message => ({
        ...message,
        isDeleted: !!message.deletedAt,
      }));

      return {
        messages: messagesWithDeletedFlag,
        total,
      };
    }
  }

  async updateMessage(
    messageId: string,
    userId: string,
    updateMessageDto: UpdateMessageDto,
  ): Promise<Message> {
    const message = await this.messageRepository.findOne({
      where: { id: messageId },
      relations: ['sender'],
    });

    if (!message) {
      throw new NotFoundException('Message not found');
    }

    if (message.senderId !== userId) {
      throw new ForbiddenException('You can only edit your own messages');
    }

    message.content = updateMessageDto.content;
    message.editedAt = new Date();

    const updatedMessage = await this.messageRepository.save(message);

    return this.messageRepository.findOne({
      where: { id: updatedMessage.id },
      relations: ['sender', 'replyTo', 'replyTo.sender'],
    });
  }

  async deleteMessage(messageId: string, userId: string): Promise<void> {
    console.log(`[INFO] Attempting to delete message: ${messageId} by user: ${userId}`);

    // First check if message exists (including soft-deleted ones)
    const message = await this.messageRepository.findOne({
      where: { id: messageId },
      relations: ['sender'],
      withDeleted: true, // Include soft-deleted records
    });

    if (!message) {
      console.log(`[ERROR] Message not found in database: ${messageId}`);
      // Let's also check if there are any messages with similar IDs for debugging
      const similarMessages = await this.messageRepository.find({
        where: {},
        select: ['id', 'spaceId', 'senderId', 'type', 'createdAt', 'deletedAt'],
        order: { createdAt: 'DESC' },
        take: 10,
        withDeleted: true,
      });
      console.log(`[DEBUG] Recent messages in database:`, similarMessages.map(m => ({
        id: m.id,
        spaceId: m.spaceId,
        senderId: m.senderId,
        type: m.type,
        createdAt: m.createdAt,
        deletedAt: m.deletedAt
      })));
      throw new NotFoundException('Message not found');
    }

    console.log(`[INFO] Found message: ${messageId}, type: ${message.type}, senderId: ${message.senderId}, deletedAt: ${message.deletedAt}`);

    // Check if message is already deleted
    if (message.deletedAt) {
      console.log(`[ERROR] Message already deleted: ${messageId} at ${message.deletedAt}`);
      throw new NotFoundException('Message has already been deleted');
    }

    if (message.senderId !== userId) {
      console.log(`[ERROR] Permission denied: User ${userId} cannot delete message ${messageId} owned by ${message.senderId}`);
      throw new ForbiddenException('You can only delete your own messages');
    }

    // If this is a split message, we need to delete the associated split and balance records
    if (message.type === MessageType.SPLIT && message.metadata) {
      const splitData = message.metadata as any;
      if (splitData.paidByUserId && splitData.participants) {
        try {
          // Delete balance records first
          await this.balanceService.deleteBalanceFromSplit(
            message.spaceId,
            splitData.paidByUserId,
            splitData.participants,
          );

          // Find and soft delete the corresponding split record
          // Try multiple methods to find the split record
          let splitRecord = null;

          // Method 1: Try to find by messageId if it exists
          if (splitData.messageId) {
            splitRecord = await this.splitRepository.findOne({
              where: { messageId: splitData.messageId },
            });
          }

          // Method 2: Find by matching creation time, space, and amount (within 1 second)
          if (!splitRecord) {
            const timeWindow = 1000; // 1 second in milliseconds
            const startTime = new Date(message.createdAt.getTime() - timeWindow);
            const endTime = new Date(message.createdAt.getTime() + timeWindow);

            splitRecord = await this.splitRepository.findOne({
              where: {
                spaceId: message.spaceId,
                totalAmount: splitData.totalAmount,
                paidByUserId: splitData.paidByUserId,
              },
              order: { createdAt: 'DESC' },
            });
          }

          if (splitRecord) {
            await this.splitRepository.softDelete(splitRecord.id);
            console.log(`Soft deleted split record: ${splitRecord.id} for message: ${messageId}`);
          } else {
            console.warn(`Split record not found for message: ${messageId}, splitData:`, splitData);
          }
        } catch (error) {
          // Log the error but don't fail the message deletion
          console.error('Failed to delete split/balance records for split message:', error);
        }
      }
    }

    // If this is a settle message, we need to delete the associated settle and balance records
    if (message.type === MessageType.SETTLE && message.metadata) {
      const settleData = message.metadata as any;
      if (settleData.fromUserId && settleData.toUserId && settleData.amount) {
        try {
          // Find and soft delete the corresponding settle record
          let settleRecord = null;

          // Method 1: Try to find by messageId
          settleRecord = await this.settleRepository.findOne({
            where: { messageId: messageId },
          });

          // Method 2: Find by matching creation time, space, and amount (within 1 second)
          if (!settleRecord) {
            const timeWindow = 1000; // 1 second in milliseconds
            const startTime = new Date(message.createdAt.getTime() - timeWindow);
            const endTime = new Date(message.createdAt.getTime() + timeWindow);

            settleRecord = await this.settleRepository.findOne({
              where: {
                spaceId: message.spaceId,
                amount: settleData.amount,
                fromUserId: settleData.fromUserId,
                toUserId: settleData.toUserId,
              },
              order: { createdAt: 'DESC' },
            });
          }

          if (settleRecord) {
            // Soft delete the settle record
            await this.settleRepository.softDelete(settleRecord.id);
            console.log(`Soft deleted settle record: ${settleRecord.id} for message: ${messageId}`);

            // Delete associated balance records
            await this.balanceService.deleteBalanceFromSettle(settleRecord.id);
          } else {
            console.warn(`Settle record not found for message: ${messageId}, settleData:`, settleData);
          }
        } catch (error) {
          // Log the error but don't fail the message deletion
          console.error('Failed to delete settle/balance records for settle message:', error);
        }
      }
    }

    await this.messageRepository.softDelete(messageId);
    console.log(`[INFO] Successfully soft deleted message: ${messageId}`);
  }

  async getMessageById(messageId: string, userId: string): Promise<Message> {
    const message = await this.messageRepository.findOne({
      where: { id: messageId },
      relations: ['sender', 'space', 'replyTo', 'replyTo.sender', 'todo', 'todo.creator', 'todo.completedByUser', 'event', 'event.creator'],
    });

    if (!message) {
      throw new NotFoundException('Message not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(message.spaceId, userId);

    return message;
  }

  async votePoll(messageId: string, userId: string, optionIds: string[]): Promise<void> {
    const message = await this.messageRepository.findOne({
      where: { id: messageId, type: MessageType.POLL },
      relations: ['sender'],
    });

    if (!message) {
      throw new NotFoundException('Poll message not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(message.spaceId, userId);

    // Get current poll data
    const pollData = message.metadata || {};
    const options = pollData.options || [];

    // Get user info for voter names
    const membership = await this.spaceMemberRepository.findOne({
      where: { spaceId: message.spaceId, userId },
      relations: ['user'],
    });

    const voterName = membership?.user?.username || 'Unknown';

    // Remove user's previous votes
    options.forEach((option: any) => {
      if (option.voterIds && option.voterNames) {
        const voterIndex = option.voterIds.indexOf(userId);
        if (voterIndex > -1) {
          option.voterIds.splice(voterIndex, 1);
          option.voterNames.splice(voterIndex, 1);
        }
      }
    });

    // Add new votes
    optionIds.forEach(optionId => {
      const option = options.find((opt: any) => opt.id === optionId);
      if (option) {
        if (!option.voterIds) option.voterIds = [];
        if (!option.voterNames) option.voterNames = [];

        option.voterIds.push(userId);
        option.voterNames.push(voterName);
      }
    });

    // Update poll data
    pollData.options = options;
    message.metadata = pollData;

    await this.messageRepository.save(message);
  }

  async getPollResults(messageId: string, userId: string): Promise<any> {
    const message = await this.messageRepository.findOne({
      where: { id: messageId, type: MessageType.POLL },
    });

    if (!message) {
      throw new NotFoundException('Poll message not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(message.spaceId, userId);

    return message.metadata;
  }

  async joinLuckyDraw(messageId: string, userId: string): Promise<any> {
    // Debug: Check if message exists with any type first
    const anyMessage = await this.messageRepository.findOne({
      where: { id: messageId },
    });



    const message = await this.messageRepository.findOne({
      where: { id: messageId, type: MessageType.LUCKY_DRAW },
    });

    if (!message) {
      throw new NotFoundException('Lucky draw message not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(message.spaceId, userId);

    const luckyDrawData = message.metadata as any;
    if (!luckyDrawData) {
      throw new NotFoundException('Lucky draw data not found');
    }

    // Get user information
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if lucky draw is still active
    if (!luckyDrawData.isActive) {
      throw new ForbiddenException('Lucky draw is no longer active');
    }

    // Check if lucky draw has expired
    if (luckyDrawData.expiresAt && new Date() > new Date(luckyDrawData.expiresAt)) {
      throw new ForbiddenException('Lucky draw has expired');
    }

    // Check if user is already participating (for non-multiple entry draws)
    const participants = luckyDrawData.participants || [];
    const isAlreadyParticipating = participants.some((p: any) => p.userId === userId);

    // Users can only join once regardless of allowMultipleEntries setting
    // allowMultipleEntries only affects whether they can win multiple prizes
    if (isAlreadyParticipating) {
      throw new ForbiddenException('You have already joined this lucky draw');
    }

    // Check if max participants limit is reached
    if (luckyDrawData.maxParticipants > 0 && participants.length >= luckyDrawData.maxParticipants) {
      throw new ForbiddenException('Lucky draw has reached maximum participants');
    }

    // Check if it's invite-only and user is invited
    if (luckyDrawData.isInviteOnly) {
      const invitedUserIds = luckyDrawData.invitedUserIds || [];
      if (!invitedUserIds.includes(userId) && userId !== luckyDrawData.creatorId) {
        throw new ForbiddenException('You are not invited to this lucky draw');
      }
    }

    // Add participant
    const newParticipant = {
      userId: userId,
      userName: user.displayName,
      joinedAt: new Date().toISOString(),
    };

    luckyDrawData.participants = [...participants, newParticipant];
    message.metadata = luckyDrawData;

    await this.messageRepository.save(message);

    return luckyDrawData;
  }

  async performLuckyDraw(messageId: string, userId: string): Promise<any> {
    const message = await this.messageRepository.findOne({
      where: { id: messageId, type: MessageType.LUCKY_DRAW },
    });

    if (!message) {
      throw new NotFoundException('Lucky draw message not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(message.spaceId, userId);

    const luckyDrawData = message.metadata as any;
    if (!luckyDrawData) {
      throw new NotFoundException('Lucky draw data not found');
    }

    // Check if user is the creator
    if (luckyDrawData.creatorId !== userId) {
      throw new ForbiddenException('Only the creator can perform the draw');
    }

    // Check if lucky draw is still active
    if (!luckyDrawData.isActive) {
      throw new ForbiddenException('Lucky draw is no longer active');
    }

    // Check if there are participants
    const participants = luckyDrawData.participants || [];
    if (participants.length === 0) {
      throw new ForbiddenException('No participants in the lucky draw');
    }

    // Check if there are available prizes
    const prizes = luckyDrawData.prizes || [];
    // Get available prizes in order (by prize ID to ensure consistent ordering)
    const availablePrizes = prizes.filter((prize: any) => prize.remainingQuantity > 0)
      .sort((a: any, b: any) => a.id.localeCompare(b.id));
    if (availablePrizes.length === 0) {
      throw new ForbiddenException('No available prizes left');
    }

    // Perform the draw
    const winners: any[] = [];
    const updatedPrizes = [...prizes];

    // Track eligible participants for multiple entries logic
    let eligibleParticipants = [...participants];

    for (const prize of availablePrizes) {
      if (eligibleParticipants.length === 0) break;

      // Simple random selection using current time as seed
      const randomIndex = Math.floor(Math.random() * eligibleParticipants.length);
      const winner = eligibleParticipants[randomIndex];

      winners.push({
        userId: winner.userId,
        userName: winner.userName,
        prizeId: prize.id,
        prizeName: prize.name,
        wonAt: new Date().toISOString(),
      });

      // Update prize quantity
      const prizeIndex = updatedPrizes.findIndex((p: any) => p.id === prize.id);
      if (prizeIndex !== -1) {
        updatedPrizes[prizeIndex] = {
          ...updatedPrizes[prizeIndex],
          remainingQuantity: updatedPrizes[prizeIndex].remainingQuantity - 1,
        };
      }

      // If multiple entries not allowed, remove winner from eligible participants
      // This prevents the same user from winning multiple prizes
      if (!luckyDrawData.allowMultipleEntries) {
        eligibleParticipants = eligibleParticipants.filter(p => p.userId !== winner.userId);
      }
    }

    // Add to draw history
    const drawHistory = luckyDrawData.drawHistory || [];
    const newHistory = {
      winners: winners,
      drawnAt: new Date().toISOString(),
    };

    luckyDrawData.prizes = updatedPrizes;
    luckyDrawData.drawHistory = [...drawHistory, newHistory];
    message.metadata = luckyDrawData;

    await this.messageRepository.save(message);

    return {
      winners: winners,
      luckyDrawData: luckyDrawData,
    };
  }

  async getLuckyDrawResults(messageId: string, userId: string): Promise<any> {
    const message = await this.messageRepository.findOne({
      where: { id: messageId, type: MessageType.LUCKY_DRAW },
    });

    if (!message) {
      throw new NotFoundException('Lucky draw message not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(message.spaceId, userId);

    return message.metadata;
  }

  async rollDice(messageId: string, userId: string, diceData: any): Promise<any> {
    const message = await this.messageRepository.findOne({
      where: { id: messageId, type: MessageType.DICE },
    });

    if (!message) {
      throw new NotFoundException('Dice message not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(message.spaceId, userId);

    // Update the message metadata with new dice data
    message.metadata = diceData;
    message.updatedAt = new Date();

    await this.messageRepository.save(message);

    return diceData;
  }

  async getDiceResults(messageId: string, userId: string): Promise<any> {
    const message = await this.messageRepository.findOne({
      where: { id: messageId, type: MessageType.DICE },
    });

    if (!message) {
      throw new NotFoundException('Dice message not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(message.spaceId, userId);

    return message.metadata;
  }

  async updateSplitPaymentStatus(
    messageId: string,
    userId: string,
    participantId: string,
    isPaid: boolean,
  ): Promise<void> {
    const message = await this.messageRepository.findOne({
      where: { id: messageId },
      relations: ['sender'],
    });

    if (!message) {
      throw new NotFoundException('Message not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(message.spaceId, userId);

    // Verify this is a split message
    if (message.type !== MessageType.SPLIT || !message.metadata) {
      throw new ForbiddenException('This is not a split message');
    }

    const splitData = message.metadata as any;

    // Find and update the participant's payment status
    if (splitData.participants && Array.isArray(splitData.participants)) {
      const participantIndex = splitData.participants.findIndex(
        (p: any) => p.userId === participantId
      );

      if (participantIndex === -1) {
        throw new NotFoundException('Participant not found in this split');
      }

      // Update payment status
      splitData.participants[participantIndex].isPaid = isPaid;
      splitData.participants[participantIndex].paidAt = isPaid ? new Date().toISOString() : null;

      // Save the updated message
      message.metadata = splitData;
      await this.messageRepository.save(message);
    } else {
      throw new ForbiddenException('Invalid split data structure');
    }
  }

  async settleDebts(
    spaceId: string,
    fromUserId: string,
    toUserId: string,
    requestingUserId: string,
  ): Promise<any> {
    // Use the new balance service to settle debts
    return this.balanceService.settleDebts(spaceId, fromUserId, toUserId, requestingUserId);
  }

  private async checkSpaceMembership(spaceId: string, userId: string): Promise<void> {
    const membership = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!membership) {
      throw new ForbiddenException('You are not a member of this space');
    }
  }

  /**
   * Load tags for todos in messages
   */
  private async loadTagsForTodoMessages(messages: Message[], userId: string): Promise<void> {
    for (const message of messages) {
      if (message.todo) {
        try {
          await this.loadTagsForTodo(message.todo);
        } catch (error) {
          console.warn('Failed to load todo tags for message:', error);
        }
      }
    }
  }

  /**
   * Load tags for a single todo
   */
  private async loadTagsForTodo(todo: Todo): Promise<void> {
    if (!todo.tagIds) {
      (todo as any).tags = [];
      return;
    }

    try {
      const tagIds = JSON.parse(todo.tagIds);
      if (!Array.isArray(tagIds) || tagIds.length === 0) {
        (todo as any).tags = [];
        return;
      }

      const tags = await this.tagRepository.find({
        where: { id: In(tagIds) }
      });

      const tagMap = new Map(tags.map(tag => [tag.id, tag]));
      (todo as any).tags = tagIds
        .map((id: string) => tagMap.get(id))
        .filter(Boolean);
    } catch (error) {
      console.warn('Failed to parse todo tagIds:', error);
      (todo as any).tags = [];
    }
  }
}
