import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TagService } from './tag.service';
import { TagController } from './tag.controller';
import { Tag } from '../../entities/tag.entity';
import { Space } from '../../entities/space.entity';
import { SpaceMember } from '../../entities/space-member.entity';
import { Todo } from '../../entities/todo.entity';
import { ToGo } from '../../entities/togo.entity';
import { Event } from '../../entities/event.entity';


@Module({
  imports: [TypeOrmModule.forFeature([Tag, Space, SpaceMember, Todo, ToGo, Event])],
  controllers: [TagController],
  providers: [TagService],
  exports: [TagService],
})
export class TagModule {}
