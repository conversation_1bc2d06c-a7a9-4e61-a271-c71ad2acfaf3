import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, In } from 'typeorm';
import { Tag } from '../../entities/tag.entity';
import { Space } from '../../entities/space.entity';
import { SpaceMember } from '../../entities/space-member.entity';
import { Todo } from '../../entities/todo.entity';
import { ToGo } from '../../entities/togo.entity';
import { Event } from '../../entities/event.entity';

import { User } from '../../entities/user.entity';
import {
  CreateTagDto,
  UpdateTagDto,
  GetTagsDto,
  TagResponseDto,
  TagStatsDto,
  TagRelationsDto,
  TagRelatedItemDto,
} from './dto/tag.dto';

@Injectable()
export class TagService {
  constructor(
    @InjectRepository(Tag)
    private tagRepository: Repository<Tag>,
    @InjectRepository(Space)
    private spaceRepository: Repository<Space>,
    @InjectRepository(SpaceMember)
    private spaceMemberRepository: Repository<SpaceMember>,
    @InjectRepository(Todo)
    private todoRepository: Repository<Todo>,
    @InjectRepository(ToGo)
    private togoRepository: Repository<ToGo>,
    @InjectRepository(Event)
    private eventRepository: Repository<Event>,
  ) {}

  async create(
    spaceId: string,
    userId: string,
    createTagDto: CreateTagDto,
  ): Promise<TagResponseDto> {
    // Verify space exists and user is a member
    await this.verifySpaceAccess(spaceId, userId);

    // Check if tag with same name already exists in this space (excluding soft-deleted)
    const existingTag = await this.tagRepository.findOne({
      where: { spaceId, name: createTagDto.name },
      withDeleted: false,
    });

    if (existingTag) {
      throw new ConflictException('Tag with this name already exists in this space');
    }

    const tag = this.tagRepository.create({
      ...createTagDto,
      spaceId,
      createdByUserId: userId,
      color: createTagDto.color || '#2196F3',
    });

    const savedTag = await this.tagRepository.save(tag);
    return this.mapToResponseDto(savedTag);
  }

  async findBySpace(
    spaceId: string,
    userId: string,
    getTagsDto: GetTagsDto = {},
  ): Promise<TagResponseDto[]> {
    // Verify space access
    await this.verifySpaceAccess(spaceId, userId);

    const {
      search,
      sortBy = 'usageCount',
      sortOrder = 'DESC',
      limit = 50,
    } = getTagsDto;

    const queryBuilder = this.tagRepository
      .createQueryBuilder('tag')
      .where('tag.spaceId = :spaceId', { spaceId });

    if (search) {
      queryBuilder.andWhere('tag.name ILIKE :search', {
        search: `%${search}%`,
      });
    }

    queryBuilder
      .orderBy(`tag.${sortBy}`, sortOrder)
      .limit(limit);

    const tags = await queryBuilder.getMany();
    return tags.map(tag => this.mapToResponseDto(tag));
  }

  async findOne(id: string, userId: string): Promise<TagResponseDto> {
    const tag = await this.tagRepository.findOne({
      where: { id },
      relations: ['space'],
    });

    if (!tag) {
      throw new NotFoundException('Tag not found');
    }

    // Verify user has access to the space
    await this.verifySpaceAccess(tag.spaceId, userId);

    return this.mapToResponseDto(tag);
  }

  async update(
    id: string,
    userId: string,
    updateTagDto: UpdateTagDto,
  ): Promise<TagResponseDto> {
    const tag = await this.findOne(id, userId);

    // Check if updating name and new name conflicts (excluding soft-deleted)
    if (updateTagDto.name && updateTagDto.name !== tag.name) {
      const existingTag = await this.tagRepository.findOne({
        where: { spaceId: tag.spaceId, name: updateTagDto.name },
        withDeleted: false,
      });

      if (existingTag) {
        throw new ConflictException('Tag with this name already exists in this space');
      }
    }

    await this.tagRepository.update(id, updateTagDto);
    return this.findOne(id, userId);
  }

  async remove(id: string, userId: string): Promise<void> {
    const tag = await this.findOne(id, userId);

    // Remove tag from all todos that reference it
    const todosWithTag = await this.todoRepository
      .createQueryBuilder('todo')
      .where('todo.tag_ids LIKE :tagId', { tagId: `%"${id}"%` })
      .getMany();

    for (const todo of todosWithTag) {
      if (todo.tagIds) {
        try {
          const tagIds = JSON.parse(todo.tagIds);
          const updatedTagIds = tagIds.filter((tagId: string) => tagId !== id);
          todo.tagIds = updatedTagIds.length > 0 ? JSON.stringify(updatedTagIds) : null;
          await this.todoRepository.save(todo);
        } catch {
          // Ignore invalid JSON
        }
      }
    }

    // Remove tag from all togos that reference it
    const togosWithTag = await this.togoRepository
      .createQueryBuilder('togo')
      .select([
        'togo.id',
        'togo.tagIds',
      ])
      .where('togo.tag_ids LIKE :tagId', { tagId: `%"${id}"%` })
      .getMany();

    for (const togo of togosWithTag) {
      if (togo.tagIds) {
        try {
          const tagIds = JSON.parse(togo.tagIds);
          const updatedTagIds = tagIds.filter((tagId: string) => tagId !== id);
          togo.tagIds = updatedTagIds.length > 0 ? JSON.stringify(updatedTagIds) : null;
          await this.togoRepository.save(togo);
        } catch {
          // Ignore invalid JSON
        }
      }
    }

    // Remove tag from all events that reference it
    const eventsWithTag = await this.eventRepository
      .createQueryBuilder('event')
      .select([
        'event.id',
        'event.tagIds',
      ])
      .where('event.tag_ids LIKE :tagId', { tagId: `%"${id}"%` })
      .getMany();

    for (const event of eventsWithTag) {
      if (event.tagIds) {
        try {
          const tagIds = JSON.parse(event.tagIds);
          const updatedTagIds = tagIds.filter((tagId: string) => tagId !== id);
          event.tagIds = updatedTagIds.length > 0 ? JSON.stringify(updatedTagIds) : null;
          await this.eventRepository.save(event);
        } catch {
          // Ignore invalid JSON
        }
      }
    }

    // Then soft delete the tag itself
    await this.tagRepository.softDelete(id);
  }

  async incrementUsageCount(tagId: string): Promise<void> {
    await this.tagRepository.increment({ id: tagId }, 'usageCount', 1);
  }

  async decrementUsageCount(tagId: string): Promise<void> {
    await this.tagRepository.decrement({ id: tagId }, 'usageCount', 1);
  }

  async getOrCreateTags(
    spaceId: string,
    userId: string,
    tagNames: string[],
  ): Promise<Tag[]> {
    if (!tagNames || tagNames.length === 0) {
      return [];
    }

    // Verify space access
    await this.verifySpaceAccess(spaceId, userId);

    const tags: Tag[] = [];

    for (const tagName of tagNames) {
      let tag = await this.tagRepository.findOne({
        where: { spaceId, name: tagName.trim() },
        withDeleted: false,
      });

      if (!tag) {
        // Create new tag
        tag = this.tagRepository.create({
          spaceId,
          name: tagName.trim(),
          createdByUserId: userId,
          color: this.generateRandomColor(),
        });
        tag = await this.tagRepository.save(tag);
      } else {
        // Increment usage count for existing tag
        await this.incrementUsageCount(tag.id);
      }

      tags.push(tag);
    }

    return tags;
  }

  async getStats(spaceId: string, userId: string): Promise<TagStatsDto> {
    // Verify space access
    await this.verifySpaceAccess(spaceId, userId);

    const totalTags = await this.tagRepository.count({ where: { spaceId } });

    // Get most used tags
    const mostUsedTags = await this.tagRepository.find({
      where: { spaceId },
      order: { usageCount: 'DESC' },
      take: 10,
    });

    // Get recent tags
    const recentTags = await this.tagRepository.find({
      where: { spaceId },
      order: { createdAt: 'DESC' },
      take: 10,
    });

    return {
      totalTags,
      mostUsedTags: mostUsedTags.map(tag => this.mapToResponseDto(tag)),
      recentTags: recentTags.map(tag => this.mapToResponseDto(tag)),
    };
  }

  async getTagRelations(
    spaceId: string,
    tagId: string,
    userId: string,
  ): Promise<TagRelationsDto> {
    // Verify space access
    await this.verifySpaceAccess(spaceId, userId);

    // Get the tag
    const tag = await this.tagRepository.findOne({
      where: { id: tagId, spaceId },
    });

    if (!tag) {
      throw new NotFoundException('Tag not found');
    }

    const relatedItems: TagRelatedItemDto[] = [];

    // Get related TODOs that have this tag in their tag_ids JSON array
    const todosWithTag = await this.todoRepository
      .createQueryBuilder('todo')
      .select([
        'todo.id',
        'todo.title',
        'todo.details',
        'todo.status',
        'todo.dueDate',
        'todo.dueTime',
        'todo.location',
        'todo.tagIds',
        'todo.createdAt',
        'creator.displayName',
      ])
      .leftJoin('todo.creator', 'creator')
      .where('todo.spaceId = :spaceId', { spaceId })
      .andWhere('todo.tag_ids LIKE :tagId', { tagId: `%"${tagId}"%` })
      .getMany();

    for (const todo of todosWithTag) {
      // Verify this todo actually contains the tag ID
      if (todo.tagIds) {
        try {
          const tagIds = JSON.parse(todo.tagIds);
          if (tagIds.includes(tagId)) {
            // Combine all relevant TODO information
            const statusParts: string[] = [todo.status];

            if (todo.details && todo.details.trim()) {
              statusParts.push(todo.details);
            }

            if (todo.dueDate) {
              const dueText = todo.dueTime
                ? `Due: ${todo.dueDate} ${todo.dueTime}`
                : `Due: ${todo.dueDate}`;
              statusParts.push(dueText);
            }

            if (todo.location && todo.location.trim()) {
              statusParts.push(`Location: ${todo.location}`);
            }

            relatedItems.push({
              id: todo.id,
              type: 'todo',
              title: todo.title,
              status: statusParts.join(' • '),
              createdAt: todo.createdAt,
              creatorName: todo.creator?.displayName,
            });
          }
        } catch {
          // Ignore invalid JSON
        }
      }
    }

    // Get related ToGos that have this tag in their tag_ids JSON array
    const togosWithTag = await this.togoRepository
      .createQueryBuilder('togo')
      .select([
        'togo.id',
        'togo.name',
        'togo.description',
        'togo.address',
        'togo.tagIds',
        'togo.createdAt',
        'creator.displayName',
      ])
      .leftJoin('togo.creator', 'creator')
      .where('togo.spaceId = :spaceId', { spaceId })
      .andWhere('togo.tag_ids LIKE :tagId', { tagId: `%"${tagId}"%` })
      .getMany();

    for (const togo of togosWithTag) {
      // Verify this togo actually contains the tag ID
      if (togo.tagIds) {
        try {
          const tagIds = JSON.parse(togo.tagIds);
          if (tagIds.includes(tagId)) {
            // Combine relevant ToGo information (excluding original link)
            const statusParts: string[] = [];

            if (togo.description && togo.description.trim()) {
              statusParts.push(togo.description);
            }

            if (togo.address && togo.address.trim()) {
              statusParts.push(`Address: ${togo.address}`);
            }

            relatedItems.push({
              id: togo.id,
              type: 'togo',
              title: togo.name,
              status: statusParts.length > 0 ? statusParts.join(' • ') : 'No description',
              createdAt: togo.createdAt,
              creatorName: togo.creator?.displayName,
            });
          }
        } catch {
          // Ignore invalid JSON
        }
      }
    }

    // Sort by creation date (newest first)
    relatedItems.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    return {
      tag: this.mapToResponseDto(tag),
      relatedItems,
      totalCount: relatedItems.length,
    };
  }

  private async verifySpaceAccess(spaceId: string, userId: string): Promise<void> {
    const space = await this.spaceRepository.findOne({ where: { id: spaceId } });
    if (!space) {
      throw new NotFoundException('Space not found');
    }

    const membership = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!membership) {
      throw new ForbiddenException('User is not a member of this space');
    }
  }

  private mapToResponseDto(tag: Tag): TagResponseDto {
    return {
      id: tag.id,
      spaceId: tag.spaceId,
      name: tag.name,
      color: tag.color,
      usageCount: tag.usageCount,
      createdByUserId: tag.createdByUserId,
      description: tag.description,
      createdAt: tag.createdAt,
      updatedAt: tag.updatedAt,
    };
  }

  private generateRandomColor(): string {
    const colors = [
      '#2196F3', '#4CAF50', '#FF9800', '#E91E63', '#9C27B0',
      '#FF5722', '#3F51B5', '#00BCD4', '#795548', '#607D8B',
      '#673AB7', '#8BC34A', '#FFC107', '#FFEB3B', '#F44336',
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }
}
