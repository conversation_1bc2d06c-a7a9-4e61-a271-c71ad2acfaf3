import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateEventSchema1738300000000 implements MigrationInterface {
  name = 'UpdateEventSchema1738300000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new columns to events table
    await queryRunner.query(`
      ALTER TABLE events
      ADD COLUMN event_dates TEXT DEFAULT '[]',
      ADD COLUMN user_date_selections TEXT NULL,
      ADD COLUMN linked_todo_id TEXT NULL,
      ADD COLUMN linked_togo_id TEXT NULL,
      ADD COLUMN tag_ids TEXT NULL,
      ADD COLUMN confirmed_date TEXT NULL,
      ADD COLUMN confirmed_time TEXT NULL
    `);

    // Migrate existing event_date to event_dates array
    await queryRunner.query(`
      UPDATE events 
      SET event_dates = '["' || event_date || '"]'
      WHERE event_date IS NOT NULL
    `);

    // Remove old columns that are no longer needed
    await queryRunner.query(`
      ALTER TABLE events
      DROP COLUMN event_date,
      DROP COLUMN max_participants
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back old columns
    await queryRunner.query(`
      ALTER TABLE events
      ADD COLUMN event_date DATE NULL,
      ADD COLUMN max_participants INTEGER NULL
    `);

    // Migrate event_dates back to event_date (take first date)
    await queryRunner.query(`
      UPDATE events 
      SET event_date = CASE 
        WHEN json_extract(event_dates, '$[0]') IS NOT NULL 
        THEN json_extract(event_dates, '$[0]')
        ELSE NULL
      END
      WHERE event_dates IS NOT NULL AND event_dates != '[]'
    `);

    // Remove new columns
    await queryRunner.query(`
      ALTER TABLE events
      DROP COLUMN event_dates,
      DROP COLUMN user_date_selections,
      DROP COLUMN linked_todo_id,
      DROP COLUMN linked_togo_id,
      DROP COLUMN tag_ids,
      DROP COLUMN confirmed_date,
      DROP COLUMN confirmed_time
    `);
  }
}
