import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateEventForJoinWorkflow1738200000000 implements MigrationInterface {
  name = 'UpdateEventForJoinWorkflow1738200000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add confirmed date and time fields
    await queryRunner.query(`
      ALTER TABLE events
      ADD COLUMN confirmed_date TEXT NULL,
      ADD COLUMN confirmed_time TEXT NULL
    `);

    // Update event response status enum to support new workflow
    await queryRunner.query(`
      UPDATE event_responses
      SET status = 'declined'
      WHERE status = 'rejected'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove confirmed date and time fields
    await queryRunner.query(`
      ALTER TABLE events
      DROP COLUMN confirmed_date,
      DROP COLUMN confirmed_time
    `);

    // Revert status changes
    await queryRunner.query(`
      UPDATE event_responses
      SET status = 'rejected'
      WHERE status = 'declined'
    `);
  }
}
