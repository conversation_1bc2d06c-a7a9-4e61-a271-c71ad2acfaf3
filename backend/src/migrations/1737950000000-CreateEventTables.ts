import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateEventTables1737950000000 implements MigrationInterface {
  name = 'CreateEventTables1737950000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create events table
    await queryRunner.createTable(
      new Table({
        name: 'events',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            length: '36',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid()',
          },
          {
            name: 'space_id',
            type: 'varchar',
            length: '36',
            isNullable: false,
          },
          {
            name: 'creator_id',
            type: 'varchar',
            length: '36',
            isNullable: false,
          },
          {
            name: 'message_id',
            type: 'varchar',
            length: '36',
            isNullable: true,
          },
          {
            name: 'title',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'event_date',
            type: 'date',
            isNullable: false,
          },
          {
            name: 'event_time',
            type: 'time',
            isNullable: true,
          },
          {
            name: 'location',
            type: 'varchar',
            length: '500',
            isNullable: true,
          },
          {
            name: 'linked_todo_id',
            type: 'varchar',
            length: '36',
            isNullable: true,
          },
          {
            name: 'linked_togo_id',
            type: 'varchar',
            length: '36',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'varchar',
            length: '20',
            default: "'pending'",
            isNullable: false,
          },

          {
            name: 'metadata',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updated_at',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'deleted_at',
            type: 'datetime',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    // Create event_responses table
    await queryRunner.createTable(
      new Table({
        name: 'event_responses',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            length: '36',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid()',
          },
          {
            name: 'event_id',
            type: 'varchar',
            length: '36',
            isNullable: false,
          },
          {
            name: 'user_id',
            type: 'varchar',
            length: '36',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'varchar',
            length: '20',
            default: "'pending'",
            isNullable: false,
          },
          {
            name: 'note',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updated_at',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
      true,
    );

    // Create indexes for events table
    await queryRunner.query(`CREATE INDEX "IDX_EVENTS_SPACE_ID" ON "events" ("space_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_EVENTS_CREATOR_ID" ON "events" ("creator_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_EVENTS_STATUS" ON "events" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_EVENTS_EVENT_DATE" ON "events" ("event_date")`);
    await queryRunner.query(`CREATE INDEX "IDX_EVENTS_SPACE_CREATED" ON "events" ("space_id", "created_at")`);
    await queryRunner.query(`CREATE INDEX "IDX_EVENTS_CREATOR_CREATED" ON "events" ("creator_id", "created_at")`);

    // Create indexes for event_responses table
    await queryRunner.query(`CREATE INDEX "IDX_EVENT_RESPONSES_EVENT_ID" ON "event_responses" ("event_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_EVENT_RESPONSES_USER_ID" ON "event_responses" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_EVENT_RESPONSES_STATUS" ON "event_responses" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_EVENT_RESPONSES_USER_CREATED" ON "event_responses" ("user_id", "created_at")`);

    // Create unique index for event_responses table
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_EVENT_RESPONSES_EVENT_USER_UNIQUE" ON "event_responses" ("event_id", "user_id")`);

    // Create foreign key constraints for events table
    await queryRunner.query(`
      ALTER TABLE "events" ADD CONSTRAINT "FK_EVENTS_SPACE_ID"
      FOREIGN KEY ("space_id") REFERENCES "spaces" ("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "events" ADD CONSTRAINT "FK_EVENTS_CREATOR_ID"
      FOREIGN KEY ("creator_id") REFERENCES "users" ("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "events" ADD CONSTRAINT "FK_EVENTS_MESSAGE_ID"
      FOREIGN KEY ("message_id") REFERENCES "messages" ("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "events" ADD CONSTRAINT "FK_EVENTS_LINKED_TODO_ID"
      FOREIGN KEY ("linked_todo_id") REFERENCES "todos" ("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "events" ADD CONSTRAINT "FK_EVENTS_LINKED_TOGO_ID"
      FOREIGN KEY ("linked_togo_id") REFERENCES "togos" ("id") ON DELETE SET NULL
    `);

    // Create foreign key constraints for event_responses table
    await queryRunner.query(`
      ALTER TABLE "event_responses" ADD CONSTRAINT "FK_EVENT_RESPONSES_EVENT_ID"
      FOREIGN KEY ("event_id") REFERENCES "events" ("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "event_responses" ADD CONSTRAINT "FK_EVENT_RESPONSES_USER_ID"
      FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "event_responses" DROP FOREIGN KEY "FK_EVENT_RESPONSES_USER_ID"`);
    await queryRunner.query(`ALTER TABLE "event_responses" DROP FOREIGN KEY "FK_EVENT_RESPONSES_EVENT_ID"`);
    await queryRunner.query(`ALTER TABLE "events" DROP FOREIGN KEY "FK_EVENTS_LINKED_TOGO_ID"`);
    await queryRunner.query(`ALTER TABLE "events" DROP FOREIGN KEY "FK_EVENTS_LINKED_TODO_ID"`);
    await queryRunner.query(`ALTER TABLE "events" DROP FOREIGN KEY "FK_EVENTS_MESSAGE_ID"`);
    await queryRunner.query(`ALTER TABLE "events" DROP FOREIGN KEY "FK_EVENTS_CREATOR_ID"`);
    await queryRunner.query(`ALTER TABLE "events" DROP FOREIGN KEY "FK_EVENTS_SPACE_ID"`);

    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_EVENT_RESPONSES_EVENT_USER_UNIQUE" ON "event_responses"`);
    await queryRunner.query(`DROP INDEX "IDX_EVENT_RESPONSES_USER_CREATED" ON "event_responses"`);
    await queryRunner.query(`DROP INDEX "IDX_EVENT_RESPONSES_STATUS" ON "event_responses"`);
    await queryRunner.query(`DROP INDEX "IDX_EVENT_RESPONSES_USER_ID" ON "event_responses"`);
    await queryRunner.query(`DROP INDEX "IDX_EVENT_RESPONSES_EVENT_ID" ON "event_responses"`);
    await queryRunner.query(`DROP INDEX "IDX_EVENTS_CREATOR_CREATED" ON "events"`);
    await queryRunner.query(`DROP INDEX "IDX_EVENTS_SPACE_CREATED" ON "events"`);
    await queryRunner.query(`DROP INDEX "IDX_EVENTS_EVENT_DATE" ON "events"`);
    await queryRunner.query(`DROP INDEX "IDX_EVENTS_STATUS" ON "events"`);
    await queryRunner.query(`DROP INDEX "IDX_EVENTS_CREATOR_ID" ON "events"`);
    await queryRunner.query(`DROP INDEX "IDX_EVENTS_SPACE_ID" ON "events"`);

    // Drop tables
    await queryRunner.dropTable('event_responses');
    await queryRunner.dropTable('events');
  }
}
