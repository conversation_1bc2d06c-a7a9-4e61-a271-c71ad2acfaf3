import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTagIdsToEvent1738100000000 implements MigrationInterface {
  name = 'AddTagIdsToEvent1738100000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE events 
      ADD COLUMN tag_ids TEXT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE events 
      DROP COLUMN tag_ids
    `);
  }
}
