import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateEventMultipleDates1738051200000 implements MigrationInterface {
  name = 'UpdateEventMultipleDates1738051200000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new event_dates column as JSON
    await queryRunner.query(`
      ALTER TABLE events 
      ADD COLUMN event_dates JSON
    `);

    // Migrate existing event_date data to event_dates array
    await queryRunner.query(`
      UPDATE events 
      SET event_dates = JSON_ARRAY(event_date) 
      WHERE event_date IS NOT NULL
    `);

    // Make event_dates NOT NULL after migration
    await queryRunner.query(`
      ALTER TABLE events 
      MODIFY COLUMN event_dates JSON NOT NULL
    `);

    // Drop the old event_date column
    await queryRunner.query(`
      ALTER TABLE events 
      DROP COLUMN event_date
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back event_date column
    await queryRunner.query(`
      ALTER TABLE events 
      ADD COLUMN event_date DATE
    `);

    // Migrate first date from event_dates array back to event_date
    await queryRunner.query(`
      UPDATE events 
      SET event_date = JSON_UNQUOTE(JSON_EXTRACT(event_dates, '$[0]'))
      WHERE event_dates IS NOT NULL
    `);

    // Drop event_dates column
    await queryRunner.query(`
      ALTER TABLE events 
      DROP COLUMN event_dates
    `);
  }
}
