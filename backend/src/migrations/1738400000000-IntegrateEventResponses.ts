import { MigrationInterface, QueryRunner } from 'typeorm';

export class IntegrateEventResponses1738400000000 implements MigrationInterface {
  name = 'IntegrateEventResponses1738400000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add user_responses column to events table
    await queryRunner.query(`
      ALTER TABLE events 
      ADD COLUMN user_responses TEXT NULL
    `);

    // Migrate existing event_responses data to events.user_responses
    await queryRunner.query(`
      UPDATE events 
      SET user_responses = (
        SELECT JSON_OBJECT(
          GROUP_CONCAT(
            CONCAT('"', er.user_id, '"'),
            JSON_OBJECT(
              'status', er.status,
              'note', IFNULL(er.note, NULL),
              'createdAt', DATE_FORMAT(er.created_at, '%Y-%m-%dT%H:%i:%s.000Z'),
              'updatedAt', DATE_FORMAT(er.updated_at, '%Y-%m-%dT%H:%i:%s.000Z')
            )
          )
        )
        FROM event_responses er 
        WHERE er.event_id = events.id
        GROUP BY er.event_id
      )
      WHERE EXISTS (
        SELECT 1 FROM event_responses er2 
        WHERE er2.event_id = events.id
      )
    `);

    // Drop the event_responses table
    await queryRunner.query(`DROP TABLE IF EXISTS event_responses`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Recreate event_responses table
    await queryRunner.query(`
      CREATE TABLE event_responses (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        event_id VARCHAR(36) NOT NULL,
        user_id VARCHAR(36) NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'pending',
        note TEXT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_event_user (event_id, user_id),
        INDEX idx_user_created (user_id, created_at),
        FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);

    // Migrate data back from events.user_responses to event_responses table
    await queryRunner.query(`
      INSERT INTO event_responses (event_id, user_id, status, note, created_at, updated_at)
      SELECT 
        e.id as event_id,
        user_data.user_id,
        user_data.status,
        user_data.note,
        STR_TO_DATE(user_data.createdAt, '%Y-%m-%dT%H:%i:%s.000Z') as created_at,
        STR_TO_DATE(user_data.updatedAt, '%Y-%m-%dT%H:%i:%s.000Z') as updated_at
      FROM events e
      CROSS JOIN JSON_TABLE(
        e.user_responses,
        '$.*' COLUMNS (
          user_id VARCHAR(36) PATH '$.userId',
          status VARCHAR(20) PATH '$.status',
          note TEXT PATH '$.note',
          createdAt VARCHAR(30) PATH '$.createdAt',
          updatedAt VARCHAR(30) PATH '$.updatedAt'
        )
      ) as user_data
      WHERE e.user_responses IS NOT NULL
    `);

    // Remove user_responses column from events table
    await queryRunner.query(`
      ALTER TABLE events 
      DROP COLUMN user_responses
    `);
  }
}
