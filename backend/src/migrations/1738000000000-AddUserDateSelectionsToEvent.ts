import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserDateSelectionsToEvent1738000000000 implements MigrationInterface {
  name = 'AddUserDateSelectionsToEvent1738000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add user_date_selections column to events table
    await queryRunner.query(`
      ALTER TABLE events 
      ADD COLUMN user_date_selections TEXT
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove user_date_selections column from events table
    await queryRunner.query(`
      ALTER TABLE events 
      DROP COLUMN user_date_selections
    `);
  }
}
