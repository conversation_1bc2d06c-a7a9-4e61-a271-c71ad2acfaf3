import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerModule } from '@nestjs/throttler';
import { ServeStaticModule } from '@nestjs/serve-static';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { join } from 'path';

import { DatabaseConfig } from './config/database.config';
import { AppConfig } from './config/app.config';
import { JwtConfig } from './config/jwt.config';

import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { SpacesModule } from './modules/spaces/spaces.module';
import { ChatModule } from './modules/chat/chat.module';
import { MediaModule } from './modules/media/media.module';
import { BalanceModule } from './modules/balance/balance.module';
import { SplitsModule } from './modules/splits/splits.module';
import { SettlesModule } from './modules/settles/settles.module';
import { TodoModule } from './modules/todo/todo.module';
import { ToGoModule } from './modules/togo/togo.module';
import { TagModule } from './modules/tag/tag.module';
import { EventModule } from './modules/event/event.module';
import { CommonModule } from './common/common.module';

import { AppController } from './app.controller';
import { AppService } from './app.service';

// Import all entities
import { User } from './entities/user.entity';
import { Space } from './entities/space.entity';
import { SpaceMember } from './entities/space-member.entity';
import { SpaceInvite } from './entities/space-invite.entity';
import { SpaceInviteUsage } from './entities/space-invite-usage.entity';
import { UserSpaceInvite } from './entities/user-space-invite.entity';

import { MediaFile } from './entities/media-file.entity';
import { Message } from './entities/message.entity';
import { Balance } from './modules/balance/entities/balance.entity';
import { Split, SplitParticipant, Settle, Todo, ToGo, Tag, Event } from './entities';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      load: [AppConfig, DatabaseConfig, JwtConfig],
      envFilePath: ['.env.local', '.env'],
    }),

    // Database
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const dbConfig = configService.get('database');
        return {
          ...dbConfig,
          entities: [
            User,
            Space,
            SpaceMember,
            SpaceInvite,
            SpaceInviteUsage,
            UserSpaceInvite,
            MediaFile,
            Message,
            Balance,
            Split,
            SplitParticipant,
            Settle,
            Todo,
            ToGo,
            Tag,
            Event,
          ],
          ...(dbConfig.type === 'mysql' && {
            timezone: 'Z',
            charset: 'utf8mb4',
            extra: {
              charset: 'utf8mb4_unicode_ci',
            },
          }),
        };
      },
      inject: [ConfigService],
    }),

    // Rate limiting
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => [
        {
          ttl: configService.get('app.throttle.ttl'),
          limit: configService.get('app.throttle.limit'),
        },
      ],
      inject: [ConfigService],
    }),

    // Static file serving for uploads
    ServeStaticModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => [
        {
          rootPath: join(__dirname, '..', configService.get('app.uploadPath')),
          serveRoot: '/uploads',
        },
      ],
      inject: [ConfigService],
    }),

    // Schedule module for cron jobs
    ScheduleModule.forRoot(),

    // Event emitter for async events
    EventEmitterModule.forRoot(),

    // Feature modules
    CommonModule,
    AuthModule,
    UsersModule,
    SpacesModule,
    ChatModule,
    MediaModule,
    BalanceModule,
    SplitsModule,
    SettlesModule,
    TodoModule,
    ToGoModule,
    TagModule,
    EventModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
