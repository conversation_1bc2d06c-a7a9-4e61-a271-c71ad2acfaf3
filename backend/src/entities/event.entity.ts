import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Space } from './space.entity';
import { Message } from './message.entity';
import { Todo } from './todo.entity';
import { ToGo } from './togo.entity';

export enum EventStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  CANCELLED = 'cancelled',
}

export enum EventResponseStatus {
  PENDING = 'pending',           // User joined but hasn't confirmed final date
  SUGGESTED = 'suggested',       // User suggested alternative dates
  CONFIRMED = 'confirmed',       // User confirmed attendance for final date
  DECLINED = 'declined',         // User declined to participate
}

export interface EventHistoryEntry {
  action: string; // 'created', 'updated', 'user_joined', 'user_suggested', 'date_confirmed', etc.
  userId?: string;
  userName?: string;
  details?: any; // Additional details about the action
  timestamp: string;
}

@Entity('events')
@Index(['spaceId', 'createdAt'])
@Index(['creatorId', 'createdAt'])
export class Event {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'space_id' })
  spaceId: string;

  @Column({ name: 'creator_id' })
  creatorId: string;

  @Column({ name: 'message_id', nullable: true })
  messageId?: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ name: 'event_dates', type: 'json' })
  eventDates: string[];

  @Column({ name: 'event_time', type: 'time', nullable: true })
  eventTime?: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  location?: string;

  @Column({
    type: 'varchar',
    length: 20,
    enum: EventStatus,
    default: EventStatus.PENDING,
  })
  status: EventStatus;



  @Column({ type: 'json', nullable: true })
  metadata?: Record<string, any>;

  @Column({ name: 'user_date_selections', type: 'json', nullable: true })
  userDateSelections?: Record<string, string[]>; // { "2025-01-30": ["userId1", "userId2"], "2025-01-31": ["userId3"] }

  @Column({ name: 'user_responses', type: 'json', nullable: true })
  userResponses?: Record<string, {
    status: EventResponseStatus;
    note?: string;
    createdAt: string;
    updatedAt: string;
  }>; // { "userId1": { status: "pending", note: "...", createdAt: "...", updatedAt: "..." } }

  @Column({ name: 'linked_todo_id', nullable: true })
  linkedTodoId?: string;

  @Column({ name: 'linked_togo_id', nullable: true })
  linkedToGoId?: string;

  @Column({ name: 'tag_ids', type: 'text', nullable: true })
  tagIds?: string;

  @Column({ name: 'confirmed_date', nullable: true })
  confirmedDate?: string;

  @Column({ name: 'confirmed_time', nullable: true })
  confirmedTime?: string;

  @Column({ name: 'history', type: 'text', nullable: true })
  history?: string; // JSON array of history entries

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt?: Date;

  // Relations
  @ManyToOne(() => Space, (space) => space.events, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'space_id' })
  space: Space;

  @ManyToOne(() => User, (user) => user.createdEvents, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'creator_id' })
  creator: User;

  @OneToOne(() => Message, (message) => message.event, { nullable: true })
  @JoinColumn({ name: 'message_id' })
  message?: Message;



  @ManyToOne(() => Todo, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'linked_todo_id' })
  linkedTodo?: Todo;

  @ManyToOne(() => ToGo, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'linked_togo_id' })
  linkedToGo?: ToGo;

  // Virtual property to get tag IDs as array
  get tagIdsArray(): string[] {
    if (!this.tagIds) return [];
    try {
      return JSON.parse(this.tagIds);
    } catch {
      return [];
    }
  }

  // Virtual property to set tag IDs from array
  set tagIdsArray(tagIds: string[]) {
    this.tagIds = tagIds.length > 0 ? JSON.stringify(tagIds) : null;
  }

  // Helper methods for user responses
  getUserResponse(userId: string): {
    status: EventResponseStatus;
    note?: string;
    createdAt: string;
    updatedAt: string;
  } | null {
    if (!this.userResponses || !this.userResponses[userId]) {
      return null;
    }
    return this.userResponses[userId];
  }

  setUserResponse(userId: string, status: EventResponseStatus, note?: string): void {
    if (!this.userResponses) {
      this.userResponses = {};
    }

    const now = new Date().toISOString();
    const existingResponse = this.userResponses[userId];

    this.userResponses[userId] = {
      status,
      note,
      createdAt: existingResponse?.createdAt || now,
      updatedAt: now,
    };
  }

  removeUserResponse(userId: string): void {
    if (this.userResponses && this.userResponses[userId]) {
      delete this.userResponses[userId];
    }
  }

  getAllUserResponses(): Array<{
    userId: string;
    status: EventResponseStatus;
    note?: string;
    createdAt: string;
    updatedAt: string;
  }> {
    if (!this.userResponses) {
      return [];
    }

    return Object.entries(this.userResponses).map(([userId, response]) => ({
      userId,
      ...response,
    }));
  }

  // History management methods
  addHistoryEntry(entry: EventHistoryEntry): void {
    const historyArray = this.getHistoryArray();
    historyArray.push(entry);
    this.history = JSON.stringify(historyArray);
  }

  getHistoryArray(): EventHistoryEntry[] {
    if (!this.history) {
      return [];
    }
    try {
      return JSON.parse(this.history);
    } catch (error) {
      console.error('Failed to parse event history:', error);
      return [];
    }
  }

  addCreatedHistory(creatorId: string, creatorName: string): void {
    this.addHistoryEntry({
      action: 'created',
      userId: creatorId,
      userName: creatorName,
      details: {
        title: this.title,
        description: this.description,
        eventDates: this.eventDates,
        location: this.location,
      },
      timestamp: new Date().toISOString(),
    });
  }

  addUserJoinedHistory(userId: string, userName: string, status: EventResponseStatus, note?: string): void {
    this.addHistoryEntry({
      action: 'user_joined',
      userId,
      userName,
      details: {
        status,
        note,
      },
      timestamp: new Date().toISOString(),
    });
  }

  addUserSuggestedDatesHistory(userId: string, userName: string, suggestedDates: string[]): void {
    this.addHistoryEntry({
      action: 'user_suggested_dates',
      userId,
      userName,
      details: {
        suggestedDates,
      },
      timestamp: new Date().toISOString(),
    });
  }

  addDateConfirmedHistory(userId: string, userName: string, confirmedDate: string, confirmedTime?: string): void {
    this.addHistoryEntry({
      action: 'date_confirmed',
      userId,
      userName,
      details: {
        confirmedDate,
        confirmedTime,
      },
      timestamp: new Date().toISOString(),
    });
  }

  addEventUpdatedHistory(userId: string, userName: string, changes: any): void {
    this.addHistoryEntry({
      action: 'event_updated',
      userId,
      userName,
      details: changes,
      timestamp: new Date().toISOString(),
    });
  }

  constructor(partial: Partial<Event>) {
    Object.assign(this, partial);
  }
}


