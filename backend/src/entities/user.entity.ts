import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { Space } from './space.entity';
import { Message } from './message.entity';
import { SpaceMember } from './space-member.entity';
import { SpaceInvite } from './space-invite.entity';
import { UserSpaceInvite } from './user-space-invite.entity';
import { MediaFile } from './media-file.entity';
import { Event } from './event.entity';

@Entity('users')
@Index(['email'])
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, length: 255 })
  email: string;

  @Column({ unique: true, length: 50 })
  username: string;

  @Column({ name: 'password_hash', length: 255 })
  @Exclude()
  passwordHash: string;

  @Column({ name: 'display_name', length: 100 })
  displayName: string;

  @Column({ name: 'avatar_url', length: 500, nullable: true })
  avatarUrl?: string;

  @Column({ name: 'email_verified', default: false })
  emailVerified: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt?: Date;

  // Relations
  @OneToMany(() => Space, (space) => space.createdBy)
  createdSpaces: Space[];

  @OneToMany(() => Message, (message) => message.sender)
  sentMessages: Message[];

  @OneToMany(() => SpaceMember, (spaceMember) => spaceMember.user)
  spaceMembers: SpaceMember[];

  @OneToMany(() => Event, (event) => event.creator)
  createdEvents: Event[];



  @OneToMany(() => SpaceInvite, (spaceInvite) => spaceInvite.createdBy)
  createdInvites: SpaceInvite[];

  @OneToMany(() => UserSpaceInvite, (userSpaceInvite) => userSpaceInvite.invitedUser)
  receivedSpaceInvites: UserSpaceInvite[];

  @OneToMany(() => UserSpaceInvite, (userSpaceInvite) => userSpaceInvite.invitedByUser)
  sentSpaceInvites: UserSpaceInvite[];

  @OneToMany(() => MediaFile, (mediaFile) => mediaFile.uploadedBy)
  uploadedFiles: MediaFile[];

  constructor(partial: Partial<User>) {
    Object.assign(this, partial);
  }
}
