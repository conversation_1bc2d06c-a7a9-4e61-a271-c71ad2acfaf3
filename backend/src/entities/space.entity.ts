import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Message } from './message.entity';
import { SpaceMember } from './space-member.entity';
import { SpaceInvite } from './space-invite.entity';
import { UserSpaceInvite } from './user-space-invite.entity';
import { SpaceCategory } from '../modules/spaces/dto/create-space.dto';
import { Event } from './event.entity';

@Entity('spaces')
@Index(['createdBy'])
@Index(['isPrivate'])
export class Space {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 200 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ name: 'is_private', default: true })
  isPrivate: boolean;

  @Column({ name: 'is_personal', default: false })
  isPersonal: boolean;

  @Column({
    type: 'varchar',
    length: 50,
    default: SpaceCategory.GENERAL
  })
  category: SpaceCategory;

  @Column({ length: 7, nullable: true })
  color?: string; // 新增顏色屬性，存儲hex顏色值如 #FF5722

  @Column({ length: 3, default: 'USD' })
  currency: string; // 新增貨幣屬性，存儲貨幣代碼如 USD, EUR, TWD

  @Column({ name: 'invite_code', unique: true, length: 8 })
  inviteCode: string;

  @Column({ name: 'allow_member_invites', default: true })
  allowMemberInvites: boolean;

  @Column({ name: 'require_approval', default: false })
  requireApproval: boolean;

  @Column({ name: 'created_by' })
  createdById: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt?: Date;

  // Relations
  @ManyToOne(() => User, (user) => user.createdSpaces, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'created_by' })
  createdBy: User;

  @OneToMany(() => Message, (message) => message.space)
  messages: Message[];

  @OneToMany(() => SpaceMember, (spaceMember) => spaceMember.space)
  members: SpaceMember[];

  @OneToMany(() => Event, (event) => event.space)
  events: Event[];

  @OneToMany(() => SpaceInvite, (spaceInvite) => spaceInvite.space)
  invites: SpaceInvite[];

  @OneToMany(() => UserSpaceInvite, (userSpaceInvite) => userSpaceInvite.space)
  userInvites: UserSpaceInvite[];

  constructor(partial: Partial<Space>) {
    Object.assign(this, partial);
  }
}
