import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Space } from './space.entity';
import { Split } from './split.entity';
import { Settle } from './settle.entity';
import { Todo } from './todo.entity';
import { ToGo } from './togo.entity';
import { Event } from './event.entity';

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  LOCATION = 'location',
  POLL = 'poll',
  DICE = 'dice',
  LUCKY_DRAW = 'luckydraw',
  SPLIT = 'split',
  SETTLE = 'settle',
  TODO = 'todo',
  TOGO = 'togo',
  EVENT = 'event',
  SYSTEM = 'system',
}

@Entity('messages')
@Index(['spaceId'])
@Index(['senderId'])
@Index(['createdAt'])
@Index(['spaceId', 'createdAt'])
export class Message {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'space_id' })
  spaceId: string;

  @Column({ name: 'sender_id' })
  senderId: string;

  @Column({
    type: 'varchar',
    length: 20,
    default: MessageType.TEXT,
  })
  type: MessageType;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'json', nullable: true })
  metadata?: Record<string, any>;

  @Column({ name: 'reply_to_id', nullable: true })
  replyToId?: string;

  @Column({ name: 'edited_at', nullable: true })
  editedAt?: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt?: Date;

  // Relations
  @ManyToOne(() => Space, (space) => space.messages, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'space_id' })
  space: Space;

  @ManyToOne(() => User, (user) => user.sentMessages, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'sender_id' })
  sender: User;

  @ManyToOne(() => Message, { nullable: true })
  @JoinColumn({ name: 'reply_to_id' })
  replyTo?: Message;

  // Relation to Split (for split messages)
  @OneToOne(() => Split, (split) => split.message, { nullable: true })
  split?: Split;

  // Relation to Settle (for settle messages)
  @OneToOne(() => Settle, (settle) => settle.message, { nullable: true })
  settle?: Settle;

  // Relation to Todo (for todo messages)
  @OneToOne(() => Todo, (todo) => todo.message, { nullable: true })
  todo?: Todo;

  // Relation to ToGo (for togo messages)
  @OneToOne(() => ToGo, (togo) => togo.message, { nullable: true })
  togo?: ToGo;

  // Relation to Event (for event messages)
  @OneToOne(() => Event, (event) => event.message, { nullable: true })
  event?: Event;

  constructor(partial: Partial<Message>) {
    Object.assign(this, partial);
  }
}
