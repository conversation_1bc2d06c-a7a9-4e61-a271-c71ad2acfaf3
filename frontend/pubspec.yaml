name: melo
description: MeLo - A collaborative memory sharing platform.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  shimmer: ^3.0.0

  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5

  # Navigation
  go_router: ^12.1.3

  # Network & API
  http: ^1.1.0
  dio: ^5.3.2
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # Local Storage
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0

  # Image Handling
  image_picker: ^1.0.4
  cached_network_image: ^3.3.0

  # Utilities
  intl: ^0.20.2
  uuid: ^4.1.0
  logger: ^2.0.2+1
  table_calendar: ^3.0.9
  connectivity_plus: ^6.0.5
  path: ^1.8.3

  # Real-time Communication
  socket_io_client: ^2.0.3+1

  # Rich Text Editor
  flutter_quill: ^11.4.1

  # Permissions & Location
  permission_handler: ^11.0.1
  geolocator: ^13.0.1
  geocoding: ^3.0.0
  google_maps_flutter: ^2.5.0
  app_settings: ^5.1.1
  get_it: ^8.0.3

  # Mobile-specific packages
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.2
  path_provider: ^2.1.4
  url_launcher: ^6.3.1
  share_plus: ^10.0.2

  # Push notifications
  firebase_core: ^3.6.0
  firebase_messaging: ^15.1.3
  flutter_local_notifications: ^18.0.1

  # Biometric authentication
  local_auth: ^2.3.0

  # Camera and media
  camera: ^0.11.0+2
  video_player: ^2.9.2

  # Storage and file handling
  sqflite: ^2.4.1
  file_picker: ^8.1.2
  crypto: ^3.0.6



dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.7
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1

  # Linting
  flutter_lints: ^3.0.1

  # Testing
  mockito: ^5.4.2

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
