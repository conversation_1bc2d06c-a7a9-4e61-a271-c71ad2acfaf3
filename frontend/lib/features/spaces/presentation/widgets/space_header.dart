import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../data/models/space_models.dart';
import '../../data/models/space_category_helper.dart';
import 'online_members_widget.dart';

class SpaceHeader extends StatelessWidget {
  final SpaceModel space;

  const SpaceHeader({
    super.key,
    required this.space,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Space名稱 (title)
        Text(
          space.name,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
                fontSize: 18,
              ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 2),
        // 分類和成員數量在同一行 (category • total member)
        Row(
          children: [
            Text(
              SpaceCategoryHelper.getDisplayName(space.category),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                    fontSize: 14,
                  ),
            ),
            Text(
              ' • ',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                    fontSize: 14,
                  ),
            ),
            Text(
              '${space.memberCount} members',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                    fontSize: 14,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        // 在線成員頭像 (member icon)
        OnlineMembersWidget(
          spaceId: space.id,
          maxVisibleMembers: 3,
          avatarSize: 24,
          spacing: -6,
          showOnlineIndicator: true,
        ),
      ],
    );
  }
}
