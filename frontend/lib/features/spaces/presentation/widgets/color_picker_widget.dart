import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../data/models/space_colors.dart';

class ColorPickerWidget extends StatefulWidget {
  final Color? selectedColor;
  final Function(Color) onColorSelected;
  final String title;

  const ColorPickerWidget({
    super.key,
    this.selectedColor,
    required this.onColorSelected,
    this.title = 'Choose Color',
  });

  @override
  State<ColorPickerWidget> createState() => _ColorPickerWidgetState();
}

class _ColorPickerWidgetState extends State<ColorPickerWidget> {
  Color? _selectedColor;

  @override
  void initState() {
    super.initState();
    _selectedColor = widget.selectedColor;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          widget.title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: 12),

        // Color grid
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 8,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1,
          ),
          itemCount: SpaceColors.predefinedColors.length,
          itemBuilder: (context, index) {
            final color = SpaceColors.predefinedColors[index];
            final isSelected = _selectedColor?.toARGB32() == color.toARGB32();

            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedColor = color;
                });
                widget.onColorSelected(color);
              },
              child: Container(
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color:
                        isSelected ? AppColors.primary : Colors.grey.shade300,
                    width: isSelected ? 3 : 1,
                  ),
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: AppColors.primary.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: isSelected
                    ? Icon(
                        Icons.check,
                        color: SpaceColors.getContrastingTextColor(color),
                        size: 20,
                      )
                    : null,
              ),
            );
          },
        ),
      ],
    );
  }
}

/// Color picker bottom sheet
class ColorPickerBottomSheet extends StatelessWidget {
  final Color? selectedColor;
  final Function(Color) onColorSelected;

  const ColorPickerBottomSheet({
    super.key,
    this.selectedColor,
    required this.onColorSelected,
  });

  static Future<Color?> show(
    BuildContext context, {
    Color? selectedColor,
  }) async {
    return await showModalBottomSheet<Color>(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => ColorPickerBottomSheet(
        selectedColor: selectedColor,
        onColorSelected: (color) {
          Navigator.of(context).pop(color);
        },
      ),
    );
  }

  void _showCustomColorPicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => CustomColorPickerDialog(
        initialColor: selectedColor ?? Colors.blue,
        onColorSelected: onColorSelected,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),

          // Title
          Text(
            'Choose Space Color',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select a color to personalize your space',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.shade600,
                ),
          ),
          const SizedBox(height: 24),

          // Color picker
          ColorPickerWidget(
            selectedColor: selectedColor,
            onColorSelected: onColorSelected,
            title: 'Available Colors',
          ),

          const SizedBox(height: 16),

          // Custom color button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _showCustomColorPicker(context),
              icon: const Icon(Icons.color_lens),
              label: const Text('Choose Custom Color'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Cancel button
          SizedBox(
            width: double.infinity,
            child: TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ),

          // Safe area padding
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }
}

/// Color display widget for showing selected color
class ColorDisplayWidget extends StatelessWidget {
  final Color? color;
  final VoidCallback? onTap;
  final String? label;
  final bool showBorder;

  const ColorDisplayWidget({
    super.key,
    this.color,
    this.onTap,
    this.label,
    this.showBorder = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: color ?? Colors.grey.shade200,
          borderRadius: BorderRadius.circular(12),
          border: showBorder
              ? Border.all(
                  color: Colors.grey.shade300,
                  width: 1,
                )
              : null,
        ),
        child: color == null
            ? Icon(
                Icons.palette,
                color: Colors.grey.shade500,
                size: 24,
              )
            : null,
      ),
    );
  }
}

/// Custom color picker dialog with HSV color picker
class CustomColorPickerDialog extends StatefulWidget {
  final Color initialColor;
  final Function(Color) onColorSelected;

  const CustomColorPickerDialog({
    super.key,
    required this.initialColor,
    required this.onColorSelected,
  });

  @override
  State<CustomColorPickerDialog> createState() =>
      _CustomColorPickerDialogState();
}

class _CustomColorPickerDialogState extends State<CustomColorPickerDialog> {
  late Color _currentColor;
  late HSVColor _currentHSVColor;

  @override
  void initState() {
    super.initState();
    _currentColor = widget.initialColor;
    _currentHSVColor = HSVColor.fromColor(_currentColor);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              // Drag handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                child: Row(
                  children: [
                    const Icon(Icons.palette, color: Colors.blue),
                    const SizedBox(width: 8),
                    const Text(
                      'Choose Custom Color',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      iconSize: 20,
                    ),
                  ],
                ),
              ),
              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    children: [
                      // Color preview
                      Container(
                        width: double.infinity,
                        height: 60,
                        decoration: BoxDecoration(
                          color: _currentColor,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: Center(
                          child: Text(
                            SpaceColors.colorToHex(_currentColor),
                            style: TextStyle(
                              color: SpaceColors.getContrastingTextColor(
                                  _currentColor),
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Hue slider
                      _buildSlider(
                        'Hue',
                        _currentHSVColor.hue,
                        0,
                        360,
                        (value) {
                          setState(() {
                            _currentHSVColor = _currentHSVColor.withHue(value);
                            _currentColor = _currentHSVColor.toColor();
                          });
                        },
                        Colors.red,
                      ),

                      // Saturation slider
                      _buildSlider(
                        'Saturation',
                        _currentHSVColor.saturation,
                        0,
                        1,
                        (value) {
                          setState(() {
                            _currentHSVColor =
                                _currentHSVColor.withSaturation(value);
                            _currentColor = _currentHSVColor.toColor();
                          });
                        },
                        _currentHSVColor.withSaturation(1).toColor(),
                      ),

                      // Value/Brightness slider
                      _buildSlider(
                        'Brightness',
                        _currentHSVColor.value,
                        0,
                        1,
                        (value) {
                          setState(() {
                            _currentHSVColor =
                                _currentHSVColor.withValue(value);
                            _currentColor = _currentHSVColor.toColor();
                          });
                        },
                        _currentHSVColor.withValue(1).toColor(),
                      ),
                    ],
                  ),
                ),
              ),
              // Actions
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  border: Border(
                    top: BorderSide(color: Colors.grey[200]!),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                            side: BorderSide(color: Colors.grey[300]!),
                          ),
                        ),
                        child: const Text('Cancel'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          widget.onColorSelected(_currentColor);
                          Navigator.of(context).pop();
                        },
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text('Select'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSlider(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
    Color thumbColor,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label: ${value.toStringAsFixed(label == 'Hue' ? 0 : 2)}',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            thumbColor: thumbColor,
            activeTrackColor: thumbColor.withValues(alpha: 0.7),
            inactiveTrackColor: thumbColor.withValues(alpha: 0.3),
          ),
          child: Slider(
            value: value,
            min: min,
            max: max,
            onChanged: onChanged,
          ),
        ),
        const SizedBox(height: 8),
      ],
    );
  }
}
