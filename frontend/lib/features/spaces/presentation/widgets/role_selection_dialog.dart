import 'package:flutter/material.dart';
import '../../data/models/space_member_model.dart';
import '../../../../core/theme/app_colors.dart';

class RoleSelectionDialog extends StatefulWidget {
  final SpaceMemberModel member;
  final Function(SpaceMemberRole) onRoleSelected;

  const RoleSelectionDialog({
    super.key,
    required this.member,
    required this.onRoleSelected,
  });

  @override
  State<RoleSelectionDialog> createState() => _RoleSelectionDialogState();
}

class _RoleSelectionDialogState extends State<RoleSelectionDialog> {
  SpaceMemberRole? _selectedRole;

  @override
  void initState() {
    super.initState();
    _selectedRole = widget.member.role;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.8,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              // Drag handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                child: Row(
                  children: [
                    const Icon(Icons.admin_panel_settings,
                        color: AppColors.primary),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Change ${widget.member.user.displayName} Role',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      iconSize: 20,
                    ),
                  ],
                ),
              ),
              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Select new role:',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 16),
                      ...SpaceMemberRole.values
                          .where((role) =>
                              role !=
                                  SpaceMemberRole
                                      .owner && // Don't allow promoting to owner
                              role !=
                                  SpaceMemberRole
                                      .pending) // Don't allow setting to pending
                          .map((role) => RadioListTile<SpaceMemberRole>(
                                title: Text(_getRoleDisplayName(role)),
                                subtitle: Text(_getRoleDescription(role)),
                                value: role,
                                groupValue: _selectedRole,
                                onChanged: (value) {
                                  setState(() {
                                    _selectedRole = value;
                                  });
                                },
                              )),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
              // Actions
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    top: BorderSide(color: Colors.grey[200]!),
                  ),
                ),
                child: SafeArea(
                  child: Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _selectedRole != null &&
                                  _selectedRole != widget.member.role
                              ? () {
                                  widget.onRoleSelected(_selectedRole!);
                                  Navigator.of(context).pop();
                                }
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Confirm'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  String _getRoleDisplayName(SpaceMemberRole role) {
    switch (role) {
      case SpaceMemberRole.owner:
        return '所有者';
      case SpaceMemberRole.admin:
        return '管理员';
      case SpaceMemberRole.member:
        return '普通成员';
      case SpaceMemberRole.pending:
        return '待处理';
    }
  }

  String _getRoleDescription(SpaceMemberRole role) {
    switch (role) {
      case SpaceMemberRole.owner:
        return '拥有所有权限，可以删除空间';
      case SpaceMemberRole.admin:
        return '可以管理成员和空间设置';
      case SpaceMemberRole.member:
        return '可以查看和发布内容';
      case SpaceMemberRole.pending:
        return '等待接受邀请';
    }
  }
}

/// Show role selection dialog
Future<void> showRoleSelectionDialog({
  required BuildContext context,
  required SpaceMemberModel member,
  required Function(SpaceMemberRole) onRoleSelected,
}) {
  return showModalBottomSheet<void>(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => RoleSelectionDialog(
      member: member,
      onRoleSelected: onRoleSelected,
    ),
  );
}
