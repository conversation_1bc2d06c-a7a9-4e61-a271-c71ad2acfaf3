import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../../core/network/api_client.dart';

import '../../data/models/space_member_model.dart';
import '../../data/models/space_models.dart';
import '../../domain/services/space_permissions_service.dart';
import '../../domain/usecases/get_space_members_usecase.dart';
import '../../domain/usecases/get_space_detail_usecase.dart';
import '../../domain/usecases/update_member_role_usecase.dart';
import '../../domain/usecases/remove_member_usecase.dart';
import '../widgets/role_selection_dialog.dart';

class SpaceMembersPage extends StatefulWidget {
  final String spaceId;

  const SpaceMembersPage({
    super.key,
    required this.spaceId,
  });

  @override
  State<SpaceMembersPage> createState() => _SpaceMembersPageState();
}

class _SpaceMembersPageState extends State<SpaceMembersPage> {
  List<SpaceMemberModel> _members = [];

  SpaceModel? _space;
  late final SpacePermissionsService _permissionsService;
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _permissionsService = DependencyInjection.getIt<SpacePermissionsService>();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load space details and members in parallel
      final getSpaceDetailUseCase =
          DependencyInjection.getIt<GetSpaceDetailUseCase>();
      final getMembersUseCase =
          DependencyInjection.getIt<GetSpaceMembersUseCase>();

      final results = await Future.wait([
        getSpaceDetailUseCase.execute(widget.spaceId),
        getMembersUseCase.execute(widget.spaceId),
      ]);

      final spaceDetail = results[0] as SpaceDetailResult;
      final members = results[1] as List<SpaceMemberModel>;

      if (mounted) {
        setState(() {
          _space = spaceDetail.space;
          _members = members;
          // Find current user member
          // Current user member is handled by permissions service
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMembers() async {
    await _loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Space Members'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (GoRouter.of(context).canPop()) {
              GoRouter.of(context).pop();
            } else {
              context.go('/spaces/${widget.spaceId}');
            }
          },
        ),
        actions: [
          if (_space != null &&
              _permissionsService.canInviteMembers(_space!, _members))
            IconButton(
              icon: const Icon(Icons.person_add),
              onPressed: () => _showInviteDialog(context),
              tooltip: 'Invite Members',
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load members',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadMembers,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_members.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No members',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadMembers,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: _members.length,
        itemBuilder: (context, index) {
          final member = _members[index];
          return _buildMemberCard(member);
        },
      ),
    );
  }

  Widget _buildMemberCard(SpaceMemberModel member) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary,
          backgroundImage: member.user.avatarUrl != null
              ? NetworkImage(member.user.avatarUrl!)
              : null,
          child: member.user.avatarUrl == null
              ? Text(
                  member.user.displayName.isNotEmpty
                      ? member.user.displayName[0].toUpperCase()
                      : 'U',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                )
              : null,
        ),
        title: Text(member.user.displayName),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(member.role.displayName),
            if (member.isPending && member.invitedAt != null)
              Text(
                'Invited: ${_formatDate(member.invitedAt!)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.orange[600],
                    ),
              )
            else if (member.joinedAt != null)
              Text(
                'Joined: ${_formatDate(member.joinedAt!)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            if (member.isPending && member.invitedBy != null)
              Text(
                'Invited by: ${member.invitedBy}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
          ],
        ),
        trailing: _space != null &&
                _permissionsService.canManageMembers(_space!, _members)
            ? PopupMenuButton<String>(
                onSelected: (value) => _handleMemberAction(value, member),
                itemBuilder: (context) {
                  final canRemove = _permissionsService.canRemoveMember(
                      _space!, _members, member);
                  final canChangeRole = _permissionsService.canChangeRole(
                      _space!, _members, member.role);

                  return [
                    // Change role option
                    if (!member.isPending && canChangeRole)
                      const PopupMenuItem(
                        value: 'change_role',
                        child: ListTile(
                          leading: Icon(Icons.admin_panel_settings),
                          title: Text('Change Role'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    if (!member.isPending && !canChangeRole)
                      PopupMenuItem(
                        enabled: false,
                        value: 'change_role_disabled',
                        child: Tooltip(
                          message: _permissionsService
                              .getPermissionErrorMessage('change_role'),
                          child: const ListTile(
                            leading: Icon(Icons.admin_panel_settings,
                                color: Colors.grey),
                            title: Text('Change Role',
                                style: TextStyle(color: Colors.grey)),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ),

                    // Cancel invite or remove member
                    if (member.isPending && canRemove)
                      const PopupMenuItem(
                        value: 'cancel_invite',
                        child: ListTile(
                          leading: Icon(Icons.cancel, color: Colors.orange),
                          title: Text('Cancel Invite'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      )
                    else if (!member.isPending && canRemove)
                      const PopupMenuItem(
                        value: 'remove',
                        child: ListTile(
                          leading: Icon(Icons.remove_circle, color: Colors.red),
                          title: Text('Remove Member'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      )
                    else if (!member.isPending && !canRemove)
                      PopupMenuItem(
                        enabled: false,
                        value: 'remove_disabled',
                        child: Tooltip(
                          message: _permissionsService
                              .getPermissionErrorMessage('remove_member'),
                          child: const ListTile(
                            leading:
                                Icon(Icons.remove_circle, color: Colors.grey),
                            title: Text('Remove Member',
                                style: TextStyle(color: Colors.grey)),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ),
                  ];
                },
              )
            : null,
      ),
    );
  }

  void _handleMemberAction(String action, SpaceMemberModel member) {
    switch (action) {
      case 'change_role':
        _showChangeRoleDialog(member);
        break;
      case 'remove':
        _showRemoveMemberDialog(member);
        break;
      case 'cancel_invite':
        _showCancelInviteDialog(member);
        break;
    }
  }

  void _showInviteDialog(BuildContext context) {
    // Navigate to the invite management page
    context.go('/spaces/${widget.spaceId}/invites');
  }

  void _showChangeRoleDialog(SpaceMemberModel member) {
    showRoleSelectionDialog(
      context: context,
      member: member,
      onRoleSelected: (newRole) async {
        await _updateMemberRole(member, newRole);
      },
    );
  }

  void _showRemoveMemberDialog(SpaceMemberModel member) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.person_remove, color: Colors.red),
                  const SizedBox(width: 8),
                  const Text(
                    'Remove Member',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  Text(
                    'Are you sure you want to remove ${member.user.displayName}?',
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  // Actions
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () async {
                            Navigator.of(context).pop();
                            await _removeMember(member);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Remove'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCancelInviteDialog(SpaceMemberModel member) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.cancel, color: Colors.orange),
                  const SizedBox(width: 8),
                  const Text(
                    'Cancel Invite',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  Text(
                    'Are you sure you want to cancel the invite for ${member.user.displayName}?',
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  // Actions
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () async {
                            Navigator.of(context).pop();
                            await _cancelInvite(member);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Cancel Invite'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _updateMemberRole(
      SpaceMemberModel member, SpaceMemberRole newRole) async {
    try {
      final updateRoleUseCase =
          DependencyInjection.getIt<UpdateMemberRoleUseCase>();
      await updateRoleUseCase.execute(widget.spaceId, member.userId, newRole);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Role updated successfully')),
        );
      }

      _loadMembers(); // Refresh the list
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to update role: $e')),
        );
      }
    }
  }

  Future<void> _removeMember(SpaceMemberModel member) async {
    try {
      final removeMemberUseCase =
          DependencyInjection.getIt<RemoveMemberUseCase>();
      await removeMemberUseCase.execute(widget.spaceId, member.userId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Member removed successfully')),
        );
      }

      _loadMembers(); // Refresh the list
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to remove member: $e')),
        );
      }
    }
  }

  Future<void> _cancelInvite(SpaceMemberModel member) async {
    try {
      final apiClient = DependencyInjection.getIt<ApiClient>();
      // Extract invite ID from member ID (format: "invite-{inviteId}")
      final inviteId = member.id.replaceFirst('invite-', '');
      await apiClient.cancelUserInvite(widget.spaceId, inviteId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Invitation cancelled')),
        );
      }

      _loadMembers(); // Refresh the list
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to cancel invite: $e')),
        );
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
