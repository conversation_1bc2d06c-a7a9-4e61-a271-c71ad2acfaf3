import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/constants/currency_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../../core/network/api_client.dart';
import '../../../../shared/widgets/permission_button.dart';
import '../../domain/services/space_permissions_service.dart';
import '../../domain/usecases/get_space_members_usecase.dart';
import '../../domain/usecases/update_member_role_usecase.dart';
import '../../domain/usecases/remove_member_usecase.dart';
import '../../data/models/space_member_model.dart';
import '../../data/models/space_models.dart';
import '../../data/models/space_category_helper.dart';
import '../../data/models/space_colors.dart';
import '../widgets/color_picker_widget.dart';
import '../widgets/currency_picker_widget.dart';
import '../widgets/role_selection_dialog.dart';
import '../bloc/space_detail_bloc.dart';
import '../bloc/spaces_bloc.dart';

class SpaceSettingsPage extends StatefulWidget {
  final String spaceId;

  const SpaceSettingsPage({
    super.key,
    required this.spaceId,
  });

  @override
  State<SpaceSettingsPage> createState() => _SpaceSettingsPageState();
}

class _SpaceSettingsPageState extends State<SpaceSettingsPage> {
  late final SpacePermissionsService _permissionsService;
  late final GetSpaceMembersUseCase _getMembersUseCase;
  List<SpaceMemberModel> _members = [];
  bool _membersLoaded = false;

  @override
  void initState() {
    super.initState();
    _permissionsService = DependencyInjection.getIt<SpacePermissionsService>();
    _getMembersUseCase = DependencyInjection.getIt<GetSpaceMembersUseCase>();
    _loadMembers();
  }

  Future<void> _loadMembers() async {
    try {
      final members = await _getMembersUseCase.execute(widget.spaceId);
      if (mounted) {
        setState(() {
          _members = members;
          _membersLoaded = true;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _membersLoaded = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => DependencyInjection.getIt<SpaceDetailBloc>()
            ..add(SpaceDetailLoadRequested(spaceId: widget.spaceId)),
        ),
        BlocProvider.value(
          value: DependencyInjection.getIt<SpacesBloc>(),
        ),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<SpacesBloc, SpacesState>(
            listener: (context, state) {
              if (state is SpaceUpdated) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Space updated successfully!'),
                    backgroundColor: Colors.green,
                  ),
                );
                // Refresh the space detail
                context.read<SpaceDetailBloc>().add(
                      SpaceDetailLoadRequested(spaceId: widget.spaceId),
                    );
              } else if (state is SpaceLeft) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Successfully left space!'),
                    backgroundColor: Colors.green,
                  ),
                );
              } else if (state is SpaceDeleted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Space deleted successfully!'),
                    backgroundColor: Colors.green,
                  ),
                );
              } else if (state is SpacesError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Error: ${state.message}'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
        ],
        child: Scaffold(
          appBar: AppBar(
            title: const Text('Space Settings'),
          ),
          body: BlocBuilder<SpaceDetailBloc, SpaceDetailState>(
            builder: (context, state) {
              if (state is SpaceDetailLoading) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              } else if (state is SpaceDetailError) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: AppColors.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading space',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        state.message,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: () {
                          context.read<SpaceDetailBloc>().add(
                                SpaceDetailLoadRequested(
                                    spaceId: widget.spaceId),
                              );
                        },
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                );
              } else if (state is SpaceDetailLoaded) {
                final space = state.space;

                return RefreshIndicator(
                  onRefresh: () async {
                    context.read<SpaceDetailBloc>().add(
                          SpaceDetailRefreshRequested(spaceId: widget.spaceId),
                        );
                  },
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Space Info Section
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(
                                AppConstants.defaultPadding),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Space Information',
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleLarge
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                ),
                                const SizedBox(
                                    height: AppConstants.defaultPadding),
                                ListTile(
                                  leading: const Icon(Icons.space_dashboard,
                                      color: AppColors.primary),
                                  title: const Text('Space Name'),
                                  subtitle: Text(space.name),
                                  trailing: const Icon(Icons.edit, size: 16),
                                  onTap: () =>
                                      _showEditNameDialog(context, space.name),
                                ),
                                ListTile(
                                  leading: const Icon(Icons.description,
                                      color: AppColors.primary),
                                  title: const Text('Description'),
                                  subtitle: Text(
                                      space.description ?? 'No description'),
                                  trailing: const Icon(Icons.edit, size: 16),
                                  onTap: () => _showEditDescriptionDialog(
                                      context, space.description),
                                ),
                                ListTile(
                                  leading: Icon(
                                      space.isPrivate
                                          ? Icons.lock
                                          : Icons.public,
                                      color: AppColors.primary),
                                  title: const Text('Privacy'),
                                  subtitle: Text(
                                      space.isPrivate ? 'Private' : 'Public'),
                                  trailing: const Icon(Icons.edit, size: 16),
                                  onTap: () => _showPrivacyDialog(
                                      context, space.isPrivate),
                                ),
                                ListTile(
                                  leading: ColorDisplayWidget(
                                    color:
                                        SpaceCategoryHelper.getEffectiveColor(
                                            space.category, space.color),
                                    showBorder: false,
                                  ),
                                  title: const Text('Color'),
                                  subtitle: Text(space.color != null
                                      ? 'Custom (${space.color})'
                                      : 'Default (${SpaceCategoryHelper.getDisplayName(space.category)})'),
                                  trailing: const Icon(Icons.edit, size: 16),
                                  onTap: () =>
                                      _showColorDialog(context, space.color),
                                ),
                                ListTile(
                                  leading: const Icon(Icons.attach_money,
                                      color: AppColors.primary),
                                  title: const Text('Default Currency'),
                                  subtitle: Text(_getCurrencyDisplayText(
                                      space.currency ?? 'USD')),
                                  trailing: const Icon(Icons.edit, size: 16),
                                  onTap: () => _showCurrencyDialog(
                                      context, space.currency ?? 'USD'),
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: AppConstants.defaultPadding),

                        // Members Section
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(
                                AppConstants.defaultPadding),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Members',
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleLarge
                                          ?.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                    ),
                                    Text(
                                      '${space.memberCount} members',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.copyWith(
                                            color: Colors.grey[600],
                                          ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),

                                // Invite Members - only for non-personal spaces
                                if (!space.isPersonal)
                                  ListTile(
                                    leading: const Icon(Icons.person_add_alt_1,
                                        color: AppColors.primary, size: 20),
                                    title: const Text('Invite Members',
                                        style: TextStyle(fontSize: 14)),
                                    subtitle: const Text(
                                        'Send invitations to join this space',
                                        style: TextStyle(fontSize: 12)),
                                    trailing: const Icon(
                                        Icons.arrow_forward_ios,
                                        size: 14),
                                    dense: true,
                                    contentPadding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 4),
                                    onTap: () => _showInviteDialog(context),
                                  ),

                                // Members List
                                if (_membersLoaded) ...[
                                  const SizedBox(height: 8),
                                  ..._buildMembersList(),
                                ] else
                                  const Padding(
                                    padding: EdgeInsets.all(
                                        AppConstants.defaultPadding),
                                    child: Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  ),

                                // Transfer Ownership - only for owners
                                if (_membersLoaded &&
                                    _permissionsService.isSpaceOwner(space))
                                  ListTile(
                                    leading: const Icon(
                                        Icons.admin_panel_settings,
                                        color: Colors.orange),
                                    title: const Text('Transfer Ownership'),
                                    subtitle: const Text(
                                        'Transfer ownership to another member'),
                                    trailing: const Icon(
                                        Icons.arrow_forward_ios,
                                        size: 16),
                                    onTap: () => _showTransferOwnershipDialog(
                                        context, space),
                                  ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: AppConstants.defaultPadding),

                        // Danger Zone
                        Card(
                          color: AppColors.error.withValues(alpha: 0.1),
                          child: Padding(
                            padding: const EdgeInsets.all(
                                AppConstants.defaultPadding),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Danger Zone',
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleLarge
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.error,
                                      ),
                                ),
                                const SizedBox(
                                    height: AppConstants.defaultPadding),
                                if (!_membersLoaded)
                                  const ListTile(
                                    leading: SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                          strokeWidth: 2),
                                    ),
                                    title: Text('Loading permissions...'),
                                  )
                                else ...[
                                  // Leave Space - available to all members except owners
                                  if (!_permissionsService.isSpaceOwner(space))
                                    ListTile(
                                      leading: const Icon(Icons.exit_to_app,
                                          color: AppColors.error),
                                      title: const Text('Leave Space'),
                                      subtitle: const Text(
                                          'You will no longer have access to this space'),
                                      onTap: () =>
                                          _showLeaveSpaceDialog(context),
                                    ),

                                  // Delete Space - only available to owners
                                  PermissionListTile(
                                    leading: const Icon(Icons.delete_forever),
                                    title: const Text('Delete Space'),
                                    subtitle: const Text(
                                        'Permanently delete this space and all its content'),
                                    hasPermission: _permissionsService
                                        .canDeleteSpace(space, _members),
                                    permissionMessage: _permissionsService
                                        .getPermissionErrorMessage(
                                            'delete_space'),
                                    isDestructive: true,
                                    onTap: () =>
                                        _showDeleteSpaceDialog(context),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }

              return const Center(
                child: CircularProgressIndicator(),
              );
            },
          ),
        ),
      ),
    );
  }

  void _showEditNameDialog(BuildContext context, String currentName) {
    final controller = TextEditingController(text: currentName);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.5,
          minChildSize: 0.3,
          maxChildSize: 0.7,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                // Drag handle
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                // Header
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                  child: Row(
                    children: [
                      const Icon(Icons.edit, color: Colors.blue),
                      const SizedBox(width: 12),
                      const Text(
                        'Edit Space Name',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                        iconSize: 20,
                      ),
                    ],
                  ),
                ),
                // Content
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        TextField(
                          controller: controller,
                          decoration: const InputDecoration(
                            labelText: 'Space Name',
                            hintText: 'Enter new space name',
                            border: OutlineInputBorder(),
                          ),
                          autofocus: true,
                        ),
                      ],
                    ),
                  ),
                ),
                // Actions
                Container(
                  padding: const EdgeInsets.all(20),
                  child: SafeArea(
                    child: Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text('Cancel'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              final newName = controller.text.trim();
                              if (newName.isNotEmpty &&
                                  newName != currentName) {
                                Navigator.of(context).pop();
                                context.read<SpacesBloc>().add(
                                      SpaceUpdateRequested(
                                        spaceId: widget.spaceId,
                                        name: newName,
                                      ),
                                    );
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Updating space name...'),
                                  ),
                                );
                              } else {
                                Navigator.of(context).pop();
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text('Save'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  void _showEditDescriptionDialog(
      BuildContext context, String? currentDescription) {
    final controller = TextEditingController(text: currentDescription ?? '');

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Drag handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                child: Row(
                  children: [
                    const Icon(Icons.edit, color: Colors.blue),
                    const SizedBox(width: 8),
                    const Text(
                      'Edit Description',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      iconSize: 20,
                    ),
                  ],
                ),
              ),
              // Content
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    TextField(
                      controller: controller,
                      decoration: const InputDecoration(
                        labelText: 'Description',
                        hintText: 'Enter space description',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 24),
                    // Actions
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(color: Colors.grey[300]!),
                              ),
                            ),
                            child: const Text('Cancel'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              final newDescription = controller.text.trim();
                              Navigator.of(context).pop();
                              context.read<SpacesBloc>().add(
                                    SpaceUpdateRequested(
                                      spaceId: widget.spaceId,
                                      description: newDescription.isEmpty
                                          ? null
                                          : newDescription,
                                    ),
                                  );
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content:
                                      Text('Updating space description...'),
                                ),
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text('Save'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showPrivacyDialog(BuildContext context, bool isPrivate) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.privacy_tip, color: Colors.blue),
                  const SizedBox(width: 8),
                  const Text(
                    'Change Privacy Setting',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  const Text('Choose the privacy setting for this space:'),
                  const SizedBox(height: 16),
                  RadioListTile<bool>(
                    title: const Text('Private'),
                    subtitle:
                        const Text('Only invited members can see this space'),
                    value: true,
                    groupValue: isPrivate,
                    onChanged: (value) {
                      Navigator.of(context).pop();
                      if (value != null && value != isPrivate) {
                        context.read<SpacesBloc>().add(
                              SpaceUpdateRequested(
                                spaceId: widget.spaceId,
                                isPrivate: value,
                              ),
                            );
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'Updating space to ${value ? 'Private' : 'Public'}...'),
                          ),
                        );
                      }
                    },
                  ),
                  RadioListTile<bool>(
                    title: const Text('Public'),
                    subtitle:
                        const Text('Anyone can discover and join this space'),
                    value: false,
                    groupValue: isPrivate,
                    onChanged: (value) {
                      Navigator.of(context).pop();
                      if (value != null && value != isPrivate) {
                        context.read<SpacesBloc>().add(
                              SpaceUpdateRequested(
                                spaceId: widget.spaceId,
                                isPrivate: value,
                              ),
                            );
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'Updating space to ${value ? 'Private' : 'Public'}...'),
                          ),
                        );
                      }
                    },
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showColorDialog(BuildContext context, String? currentColor) {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final spacesBloc = context.read<SpacesBloc>();

    ColorPickerBottomSheet.show(
      context,
      selectedColor:
          currentColor != null ? SpaceColors.hexToColor(currentColor) : null,
    ).then((selectedColor) {
      if (selectedColor != null && mounted) {
        final colorHex = SpaceColors.colorToHex(selectedColor);
        if (colorHex != currentColor) {
          spacesBloc.add(
            SpaceUpdateRequested(
              spaceId: widget.spaceId,
              color: colorHex,
            ),
          );
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Updating space color...'),
            ),
          );
        }
      }
    });
  }

  void _showInviteDialog(BuildContext context) {
    // Navigate to the invite management page
    context.push('/spaces/${widget.spaceId}/invites');
  }

  void _showLeaveSpaceDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.exit_to_app, color: Colors.red),
                  const SizedBox(width: 8),
                  const Text(
                    'Leave Space',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  const Text(
                    'Are you sure you want to leave this space? You will no longer have access to its content.',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  // Actions
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            context.read<SpacesBloc>().add(
                                  SpaceLeaveRequested(spaceId: widget.spaceId),
                                );
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Leaving space...'),
                              ),
                            );
                            // Navigate back to home page (spaces are integrated into home)
                            context.go('/home');
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.error,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Leave'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteSpaceDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.delete_forever, color: Colors.red),
                  const SizedBox(width: 8),
                  const Text(
                    'Delete Space',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  const Text(
                    'Are you sure you want to delete this space? This action cannot be undone and all content will be permanently lost.',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  // Actions
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            context.read<SpacesBloc>().add(
                                  SpaceDeleteRequested(spaceId: widget.spaceId),
                                );
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Deleting space...'),
                              ),
                            );
                            // Navigate back to home page
                            context.go('/home');
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.error,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Delete'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showTransferOwnershipDialog(BuildContext context, space) {
    // Filter members to show only admins and regular members (not pending or current owner)
    final eligibleMembers = _members
        .where((member) =>
            member.role != SpaceMemberRole.owner &&
            member.role != SpaceMemberRole.pending &&
            member.userId != _permissionsService.currentUserId)
        .toList();

    if (eligibleMembers.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
              'No eligible members to transfer ownership to. Add more members first.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Drag handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                child: Row(
                  children: [
                    const Icon(Icons.transfer_within_a_station,
                        color: Colors.orange),
                    const SizedBox(width: 8),
                    const Text(
                      'Transfer Ownership',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      iconSize: 20,
                    ),
                  ],
                ),
              ),
              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Select a member to transfer ownership to:',
                        style: TextStyle(fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.orange.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border:
                              Border.all(color: Colors.orange.withOpacity(0.3)),
                        ),
                        child: const Row(
                          children: [
                            Icon(Icons.warning, color: Colors.orange, size: 20),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Warning: This action cannot be undone. You will become a regular member.',
                                style: TextStyle(
                                    color: Colors.orange, fontSize: 12),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      Expanded(
                        child: ListView.builder(
                          controller: scrollController,
                          itemCount: eligibleMembers.length,
                          itemBuilder: (context, index) {
                            final member = eligibleMembers[index];
                            return ListTile(
                              leading: CircleAvatar(
                                backgroundColor: AppColors.primary,
                                backgroundImage: member.user.avatarUrl != null
                                    ? NetworkImage(member.user.avatarUrl!)
                                    : null,
                                child: member.user.avatarUrl == null
                                    ? Text(
                                        member.user.displayName.isNotEmpty
                                            ? member.user.displayName[0]
                                                .toUpperCase()
                                            : 'U',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      )
                                    : null,
                              ),
                              title: Text(member.user.displayName),
                              subtitle: Text(member.role.displayName),
                              onTap: () {
                                Navigator.of(context).pop();
                                _showTransferConfirmationDialog(
                                    context, member, space);
                              },
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showTransferConfirmationDialog(
      BuildContext context, SpaceMemberModel newOwner, space) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.warning, color: Colors.orange),
                  const SizedBox(width: 8),
                  const Text(
                    'Confirm Ownership Transfer',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Are you sure you want to transfer ownership of "${space.name}" to ${newOwner.user.displayName}?',
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'After this transfer:',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  Text(
                      '• ${newOwner.user.displayName} will become the space owner'),
                  const Text('• You will become a regular member'),
                  const Text('• This action cannot be undone'),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border:
                          Border.all(color: Colors.red.withValues(alpha: 0.3)),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.warning, color: Colors.red, size: 20),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Make sure you trust this person with full control of the space.',
                            style: TextStyle(
                              color: Colors.red,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  // Actions
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            _transferOwnership(context, newOwner.userId, space);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Transfer Ownership'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildMembersList() {
    if (_members.isEmpty) {
      return [
        const Padding(
          padding: EdgeInsets.all(AppConstants.defaultPadding),
          child: Center(
            child: Column(
              children: [
                Icon(
                  Icons.people_outline,
                  size: 48,
                  color: Colors.grey,
                ),
                SizedBox(height: 8),
                Text(
                  'No members',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ),
      ];
    }

    return _members.map((member) => _buildMemberCard(member)).toList();
  }

  Widget _buildMemberCard(SpaceMemberModel member) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary,
          backgroundImage: member.user.avatarUrl != null
              ? NetworkImage(member.user.avatarUrl!)
              : null,
          child: member.user.avatarUrl == null
              ? Text(
                  member.user.displayName.isNotEmpty
                      ? member.user.displayName[0].toUpperCase()
                      : 'U',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                )
              : null,
        ),
        title: Text(member.user.displayName),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(member.role.displayName),
            if (member.isPending && member.invitedAt != null)
              Text(
                'Invited: ${_formatDate(member.invitedAt!)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.orange[600],
                    ),
              )
            else if (member.joinedAt != null)
              Text(
                'Joined: ${_formatDate(member.joinedAt!)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            if (member.isPending && member.invitedBy != null)
              Text(
                'Invited by: ${member.invitedBy}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
          ],
        ),
        trailing: BlocBuilder<SpaceDetailBloc, SpaceDetailState>(
          builder: (context, state) {
            if (state is SpaceDetailLoaded) {
              return _permissionsService.canManageMembers(state.space, _members)
                  ? PopupMenuButton<String>(
                      onSelected: (value) => _handleMemberAction(value, member),
                      itemBuilder: (context) =>
                          _buildMemberMenuItems(member, state.space),
                    )
                  : const SizedBox.shrink();
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  List<PopupMenuEntry<String>> _buildMemberMenuItems(
      SpaceMemberModel member, SpaceModel space) {
    final canRemove =
        _permissionsService.canRemoveMember(space, _members, member);
    final canChangeRole =
        _permissionsService.canChangeRole(space, _members, member.role);

    return [
      // Change role option
      if (!member.isPending && canChangeRole)
        const PopupMenuItem(
          value: 'change_role',
          child: ListTile(
            leading: Icon(Icons.admin_panel_settings),
            title: Text('Change Role'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
      if (!member.isPending && !canChangeRole)
        PopupMenuItem(
          enabled: false,
          value: 'change_role_disabled',
          child: Tooltip(
            message:
                _permissionsService.getPermissionErrorMessage('change_role'),
            child: const ListTile(
              leading: Icon(Icons.admin_panel_settings, color: Colors.grey),
              title: Text('Change Role', style: TextStyle(color: Colors.grey)),
              contentPadding: EdgeInsets.zero,
            ),
          ),
        ),

      // Cancel invite or remove member
      if (member.isPending && canRemove)
        const PopupMenuItem(
          value: 'cancel_invite',
          child: ListTile(
            leading: Icon(Icons.cancel, color: Colors.orange),
            title: Text('Cancel Invite'),
            contentPadding: EdgeInsets.zero,
          ),
        )
      else if (!member.isPending && canRemove)
        const PopupMenuItem(
          value: 'remove',
          child: ListTile(
            leading: Icon(Icons.remove_circle, color: Colors.red),
            title: Text('Remove Member'),
            contentPadding: EdgeInsets.zero,
          ),
        )
      else if (!member.isPending && !canRemove)
        PopupMenuItem(
          enabled: false,
          value: 'remove_disabled',
          child: Tooltip(
            message:
                _permissionsService.getPermissionErrorMessage('remove_member'),
            child: const ListTile(
              leading: Icon(Icons.remove_circle, color: Colors.grey),
              title:
                  Text('Remove Member', style: TextStyle(color: Colors.grey)),
              contentPadding: EdgeInsets.zero,
            ),
          ),
        ),
    ];
  }

  void _handleMemberAction(String action, SpaceMemberModel member) {
    switch (action) {
      case 'change_role':
        _showChangeRoleDialog(member);
        break;
      case 'remove':
        _showRemoveMemberDialog(member);
        break;
      case 'cancel_invite':
        _showCancelInviteDialog(member);
        break;
    }
  }

  void _showChangeRoleDialog(SpaceMemberModel member) {
    showRoleSelectionDialog(
      context: context,
      member: member,
      onRoleSelected: (newRole) async {
        await _updateMemberRole(member, newRole);
      },
    );
  }

  void _showRemoveMemberDialog(SpaceMemberModel member) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.person_remove, color: Colors.red),
                  const SizedBox(width: 8),
                  const Text(
                    'Remove Member',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  Text(
                    'Are you sure you want to remove ${member.user.displayName}?',
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  // Actions
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () async {
                            Navigator.of(context).pop();
                            await _removeMember(member);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Remove'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCancelInviteDialog(SpaceMemberModel member) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.cancel, color: Colors.orange),
                  const SizedBox(width: 8),
                  const Text(
                    'Cancel Invite',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  Text(
                    'Are you sure you want to cancel the invite for ${member.user.displayName}?',
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  // Actions
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () async {
                            Navigator.of(context).pop();
                            await _cancelInvite(member);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Cancel Invite'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _updateMemberRole(
      SpaceMemberModel member, SpaceMemberRole newRole) async {
    try {
      final updateRoleUseCase =
          DependencyInjection.getIt<UpdateMemberRoleUseCase>();
      await updateRoleUseCase.execute(widget.spaceId, member.userId, newRole);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Role updated successfully')),
        );
      }

      _loadMembers(); // Refresh the list
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to update role: $e')),
        );
      }
    }
  }

  Future<void> _removeMember(SpaceMemberModel member) async {
    try {
      final removeMemberUseCase =
          DependencyInjection.getIt<RemoveMemberUseCase>();
      await removeMemberUseCase.execute(widget.spaceId, member.userId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Member removed successfully')),
        );
      }

      _loadMembers(); // Refresh the list
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to remove member: $e')),
        );
      }
    }
  }

  Future<void> _cancelInvite(SpaceMemberModel member) async {
    try {
      final apiClient = DependencyInjection.getIt<ApiClient>();
      // Extract invite ID from member ID (format: "invite-{inviteId}")
      final inviteId = member.id.replaceFirst('invite-', '');
      await apiClient.cancelUserInvite(widget.spaceId, inviteId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Invite cancelled successfully')),
        );
      }

      _loadMembers(); // Refresh the list
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to cancel invite: $e')),
        );
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
  }

  Future<void> _transferOwnership(
      BuildContext context, String newOwnerId, space) async {
    // Save references before async operations
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      // Show loading
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Transferring ownership...'),
          duration: Duration(seconds: 2),
        ),
      );

      // Call the transfer ownership API
      final apiClient = DependencyInjection.getIt<ApiClient>();
      await apiClient.transferOwnership(widget.spaceId, newOwnerId);

      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Ownership transferred successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh the page data
        _loadMembers();

        // Refresh space detail by triggering a rebuild
        setState(() {
          // This will cause the BlocProvider to reload the space detail
        });
      }
    } catch (e) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Failed to transfer ownership: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getCurrencyDisplayText(String currency) {
    final currencyInfo = CurrencyConstants.getCurrencyInfo(currency);
    if (currencyInfo != null) {
      return '${currencyInfo.flag} ${currencyInfo.name} (${currencyInfo.code})';
    }
    return currency;
  }

  void _showCurrencyDialog(BuildContext context, String currentCurrency) {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final spacesBloc = context.read<SpacesBloc>();

    CurrencyPickerBottomSheet.show(
      context,
      selectedCurrency: currentCurrency,
    ).then((selectedCurrency) {
      if (selectedCurrency != null &&
          selectedCurrency != currentCurrency &&
          mounted) {
        spacesBloc.add(
          SpaceUpdateRequested(
            spaceId: widget.spaceId,
            currency: selectedCurrency,
          ),
        );
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Updating space currency to $selectedCurrency...'),
          ),
        );
      }
    });
  }
}
