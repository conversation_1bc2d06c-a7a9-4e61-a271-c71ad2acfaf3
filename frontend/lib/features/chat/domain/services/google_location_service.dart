import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';
import '../../../../core/config/google_api_config.dart';
import '../../../../core/utils/api_usage_monitor.dart';
import '../../../../core/cache/location_cache_manager.dart';
import '../models/google_location_models.dart';
import 'location_service.dart';

/// 位置權限結果枚舉
enum LocationPermissionResult {
  granted,
  denied,
  permanentlyDenied,
  serviceDisabled,
  error,
}

/// Google Location API 服務
/// 專門使用 Google Maps API 提供完整的位置服務功能
class GoogleLocationService {
  static bool _isInitialized = false;

  // 使用統一的緩存管理器
  static final LocationCacheManager _cacheManager =
      LocationCacheManager.instance;

  /// 檢查 API Key 是否已配置
  static bool get isApiKeyConfigured => GoogleApiConfig.isConfigured;

  /// 檢查服務是否已初始化
  static bool get isInitialized => _isInitialized;

  /// 初始化 Google Location Service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    developer.log('🚀 初始化 Google Location Service...');

    try {
      if (!isApiKeyConfigured) {
        developer.log('⚠️ Google Maps API Key not configured');
        throw const GoogleLocationException(
            'Google Maps API Key not configured');
      }

      // 測試位置權限
      final permissionResult = await requestLocationPermission();
      developer.log('📍 位置權限狀態: $permissionResult');

      _isInitialized = true;
      developer.log('✅ Google Location Service 初始化完成');
    } catch (e) {
      developer.log('❌ Google Location Service 初始化失敗: $e');
      rethrow;
    }
  }

  /// 請求位置權限
  static Future<LocationPermissionResult> requestLocationPermission() async {
    developer.log('🔍 檢查位置權限...');

    // 檢查位置服務是否啟用
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    developer.log('🛰️ 位置服務啟用: $serviceEnabled');

    if (!serviceEnabled) {
      developer.log('❌ 位置服務未啟用');
      return LocationPermissionResult.serviceDisabled;
    }

    // 檢查權限狀態
    LocationPermission permission = await Geolocator.checkPermission();
    developer.log('📱 當前權限狀態: $permission');

    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      developer.log('📱 請求權限結果: $permission');
    }

    switch (permission) {
      case LocationPermission.always:
      case LocationPermission.whileInUse:
        return LocationPermissionResult.granted;
      case LocationPermission.denied:
        return LocationPermissionResult.denied;
      case LocationPermission.deniedForever:
        return LocationPermissionResult.permanentlyDenied;
      case LocationPermission.unableToDetermine:
        return LocationPermissionResult.denied;
    }
  }

  /// 獲取當前位置
  static Future<LocationData?> getCurrentLocation() async {
    try {
      developer.log('📍 獲取當前位置...');

      // 檢查權限
      final permissionResult = await requestLocationPermission();
      if (permissionResult != LocationPermissionResult.granted) {
        developer.log('❌ 位置權限被拒絕');
        return null;
      }

      // 獲取位置
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 10),
        ),
      );

      developer.log('✅ 當前位置: ${position.latitude}, ${position.longitude}');

      // 使用 Google API 進行反向地理編碼
      final googleLocation =
          await reverseGeocode(position.latitude, position.longitude);

      if (googleLocation != null) {
        developer.log('🏠 Google 反向地理編碼成功: ${googleLocation.formattedAddress}');
        return googleLocation.toLocationData();
      } else {
        // 如果反向地理編碼失敗，返回基本位置信息
        return LocationData(
          latitude: position.latitude,
          longitude: position.longitude,
          name: 'Current Location',
          address:
              '${position.latitude.toStringAsFixed(6)}, ${position.longitude.toStringAsFixed(6)}',
          type: LocationType.currentLocation,
        );
      }
    } catch (e) {
      developer.log('❌ 獲取當前位置失敗: $e');
      return null;
    }
  }

  /// 搜索地址 - 統一接口（帶智能緩存）
  static Future<List<LocationData>> searchAddresses(String query) async {
    try {
      developer.log('🔍 Google API 地址搜索: "$query"');

      // 檢查緩存
      final cachedResults = _cacheManager.getSearchResults(query);
      if (cachedResults != null) {
        ApiUsageMonitor.recordCacheHit('Places API');
        return cachedResults;
      }

      // 首先嘗試使用 Places Autocomplete 獲取更好的結果
      final autocompleteResults = await autocompleteSearch(query);
      if (autocompleteResults.isNotEmpty) {
        // 獲取前幾個結果的詳細信息
        final locationResults = <LocationData>[];
        for (int i = 0; i < autocompleteResults.length && i < 5; i++) {
          final prediction = autocompleteResults[i];
          final placeDetails = await getPlaceDetails(prediction.placeId);
          if (placeDetails != null) {
            locationResults.add(placeDetails.toLocationData());
          }
        }

        if (locationResults.isNotEmpty) {
          developer.log(
              '✅ Google Places Autocomplete 找到 ${locationResults.length} 個結果');
          return locationResults;
        }
      }

      // 如果 Autocomplete 沒有結果，回退到 Geocoding
      final googleResults = await geocodeAddress(query);
      final locationResults =
          googleResults.map((google) => google.toLocationData()).toList();

      developer.log('✅ Google Geocoding 找到 ${locationResults.length} 個結果');

      // 緩存結果
      _cacheManager.cacheSearchResults(query, locationResults);
      ApiUsageMonitor.recordApiCall('Places API');

      return locationResults;
    } catch (e) {
      developer.log('❌ Google API 搜索失敗: $e');
      return [];
    }
  }

  /// 獲取地點自動完成建議
  static Future<List<LocationData>> getAutocompleteSuggestions(
      String input) async {
    try {
      developer.log('🔍 Google Places Autocomplete: "$input"');

      final predictions = await autocompleteSearch(input);
      final suggestions = <LocationData>[];

      for (final prediction in predictions.take(5)) {
        suggestions.add(LocationData(
          latitude: 0, // Will be filled when place details are fetched
          longitude: 0,
          name: prediction.description,
          address: prediction.description,
          placeId: prediction.placeId,
          type: LocationType.place,
        ));
      }

      developer.log('✅ 找到 ${suggestions.length} 個自動完成建議');
      return suggestions;
    } catch (e) {
      developer.log('❌ 自動完成建議失敗: $e');
      return [];
    }
  }

  /// 地址轉座標（地理編碼）
  /// 使用 Google Geocoding API 進行高精度地址解析
  static Future<List<GoogleLocationData>> geocodeAddress(String address) async {
    if (!isApiKeyConfigured) {
      developer.log(
          '⚠️ Google Maps API Key not configured, falling back to basic geocoding');
      throw const GoogleLocationException('Google Maps API Key not configured');
    }

    developer.log('🔍 Google Geocoding: Searching for "$address"');

    try {
      final String encodedAddress = Uri.encodeComponent(address);
      final String url =
          '${GoogleApiConfig.geocodingBaseUrl}?address=$encodedAddress&key=${GoogleApiConfig.apiKey}&language=${GoogleApiConfig.language}&region=${GoogleApiConfig.region}';

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      ).timeout(GoogleApiConfig.requestTimeout);

      developer.log('🌐 Geocoding API 響應狀態: ${response.statusCode}');
      developer.log('🌐 Geocoding API 響應內容: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return _parseGeocodingResponse(data, address);
      } else {
        developer.log('❌ Google Geocoding HTTP Error: ${response.statusCode}');
        developer.log('❌ 響應內容: ${response.body}');
        throw GoogleLocationException(
            'HTTP Error: ${response.statusCode} - ${response.body}');
      }
    } on TimeoutException {
      developer.log('⏰ Google Geocoding timeout');
      throw const GoogleLocationException('Request timeout');
    } catch (e) {
      developer.log('❌ Google Geocoding error: $e');
      throw GoogleLocationException('Geocoding failed: $e');
    }
  }

  /// 座標轉地址（反向地理編碼）
  static Future<GoogleLocationData?> reverseGeocode(
      double latitude, double longitude) async {
    if (!isApiKeyConfigured) {
      throw const GoogleLocationException('Google Maps API Key not configured');
    }

    developer.log('🔄 Google Reverse Geocoding: $latitude, $longitude');

    try {
      final String url =
          '${GoogleApiConfig.geocodingBaseUrl}?latlng=$latitude,$longitude&key=${GoogleApiConfig.apiKey}&language=${GoogleApiConfig.language}&region=${GoogleApiConfig.region}';

      final response = await http
          .get(Uri.parse(url))
          .timeout(GoogleApiConfig.requestTimeout);

      developer.log('🌐 Reverse Geocoding API 響應狀態: ${response.statusCode}');
      developer.log('🌐 Reverse Geocoding API 響應內容: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final results = _parseGeocodingResponse(data, 'Reverse Geocoding');

        if (results.isNotEmpty) {
          developer
              .log('✅ Reverse geocoded: ${results.first.formattedAddress}');
          return results.first;
        } else {
          developer.log('⚠️ No reverse geocoding results found');
          return null;
        }
      } else {
        developer.log('❌ Reverse geocoding HTTP Error: ${response.statusCode}');
        developer.log('❌ 響應內容: ${response.body}');
        return null;
      }
    } catch (e) {
      developer.log('❌ Reverse geocoding error: $e');
      return null;
    }
  }

  /// 地點自動完成搜索
  static Future<List<GooglePlacePrediction>> autocompleteSearch(
      String input) async {
    if (!isApiKeyConfigured) {
      throw const GoogleLocationException('Google Maps API Key not configured');
    }

    if (input.trim().length < 2) {
      return [];
    }

    developer.log('🔍 Google Places Autocomplete: "$input"');

    try {
      final String encodedInput = Uri.encodeComponent(input);
      final String url =
          '${GoogleApiConfig.placesBaseUrl}/autocomplete/json?input=$encodedInput&key=${GoogleApiConfig.apiKey}&language=${GoogleApiConfig.language}&region=${GoogleApiConfig.region}&types=establishment|geocode';

      final response = await http
          .get(Uri.parse(url))
          .timeout(GoogleApiConfig.requestTimeout);

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return _parseAutocompleteResponse(data);
      } else {
        developer
            .log('❌ Google Autocomplete HTTP Error: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      developer.log('❌ Google Autocomplete error: $e');
      return [];
    }
  }

  /// 根據 Place ID 獲取詳細信息
  static Future<GoogleLocationData?> getPlaceDetails(String placeId) async {
    if (!isApiKeyConfigured) {
      throw const GoogleLocationException('Google Maps API Key not configured');
    }

    developer.log('📍 Google Place Details: $placeId');

    try {
      final String url =
          '${GoogleApiConfig.placesBaseUrl}/details/json?place_id=$placeId&key=${GoogleApiConfig.apiKey}&language=${GoogleApiConfig.language}&fields=geometry,formatted_address,name,address_components,place_id';

      final response = await http
          .get(Uri.parse(url))
          .timeout(GoogleApiConfig.requestTimeout);

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);

        if (data['status'] == 'OK' && data['result'] != null) {
          return GoogleLocationData.fromPlaceDetails(data['result']);
        }
      }

      developer.log('❌ Place details failed');
      return null;
    } catch (e) {
      developer.log('❌ Place details error: $e');
      return null;
    }
  }

  /// 測試 Google API 連接
  static Future<bool> testApiConnection() async {
    if (!isApiKeyConfigured) {
      developer.log('⚠️ Google Maps API Key not configured');
      return false;
    }

    developer.log('🌐 === Google API 連接測試 ===');
    developer.log('🔑 API Key: ${GoogleApiConfig.apiKey.substring(0, 10)}...');
    developer.log('🌍 Base URL: ${GoogleApiConfig.geocodingBaseUrl}');

    try {
      // 測試一個簡單的地址
      final results = await geocodeAddress('Hong Kong');

      if (results.isNotEmpty) {
        developer.log('✅ Google API 連接正常');
        developer.log('📍 測試結果: ${results.first.formattedAddress}');
        return true;
      } else {
        developer.log('❌ Google API 無響應');
        return false;
      }
    } catch (e) {
      developer.log('❌ Google API 連接失敗: $e');
      return false;
    } finally {
      developer.log('🌐 === Google API 連接測試結束 ===');
    }
  }

  /// 診斷 API 問題
  static Future<void> diagnoseApiIssues() async {
    developer.log('🔍 === Google API 診斷開始 ===');

    // 1. 檢查配置
    developer.log('📋 配置檢查:');
    developer.log('   API Key 已配置: $isApiKeyConfigured');
    developer
        .log('   API Key 前綴: ${GoogleApiConfig.apiKey.substring(0, 10)}...');
    developer.log('   語言設置: ${GoogleApiConfig.language}');
    developer.log('   地區設置: ${GoogleApiConfig.region}');

    // 2. 測試簡單請求
    developer.log('🧪 測試簡單的 Geocoding 請求...');
    try {
      const testUrl =
          '${GoogleApiConfig.geocodingBaseUrl}?address=Hong Kong&key=${GoogleApiConfig.apiKey}';
      developer.log('🌐 測試 URL: $testUrl');

      final response = await http.get(Uri.parse(testUrl));
      developer.log('📊 響應狀態: ${response.statusCode}');
      developer.log('📄 響應內容: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        developer.log('✅ API 狀態: ${data['status']}');
        if (data['status'] != 'OK') {
          developer.log('❌ API 錯誤: ${data['error_message'] ?? data['status']}');
        }
      }
    } catch (e) {
      developer.log('❌ 診斷測試失敗: $e');
    }

    developer.log('🔍 === Google API 診斷結束 ===');
  }

  /// 解析地理編碼響應
  static List<GoogleLocationData> _parseGeocodingResponse(
      Map<String, dynamic> data, String query) {
    if (data['status'] != 'OK') {
      final errorMessage = data['error_message'] ?? data['status'];
      developer.log('❌ Google API Error: $errorMessage');
      throw GoogleLocationException('API Error: $errorMessage');
    }

    final List<dynamic> results = data['results'] ?? [];
    final List<GoogleLocationData> locations = [];

    for (int i = 0; i < results.length && i < GoogleApiConfig.maxResults; i++) {
      try {
        final location = GoogleLocationData.fromGeocodingResult(results[i]);
        locations.add(location);
        developer.log(
            '✅ Parsed location: ${location.name ?? location.formattedAddress}');
      } catch (e) {
        developer.log('⚠️ Failed to parse location $i: $e');
      }
    }

    developer.log('📍 Found ${locations.length} locations for "$query"');
    return locations;
  }

  /// 解析自動完成響應
  static List<GooglePlacePrediction> _parseAutocompleteResponse(
      Map<String, dynamic> data) {
    if (data['status'] != 'OK') {
      developer.log('❌ Google Autocomplete Error: ${data['status']}');
      return [];
    }

    final List<dynamic> predictions = data['predictions'] ?? [];
    final List<GooglePlacePrediction> results = [];

    for (final prediction in predictions) {
      try {
        results.add(GooglePlacePrediction.fromJson(prediction));
      } catch (e) {
        developer.log('⚠️ Failed to parse prediction: $e');
      }
    }

    developer.log('📍 Found ${results.length} autocomplete suggestions');
    return results;
  }
}

/// Google Location 異常類
class GoogleLocationException implements Exception {
  final String message;

  const GoogleLocationException(this.message);

  @override
  String toString() => 'GoogleLocationException: $message';
}
