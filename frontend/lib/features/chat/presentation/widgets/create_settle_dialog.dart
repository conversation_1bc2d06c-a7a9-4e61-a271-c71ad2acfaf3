import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../auth/domain/services/auth_service.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../spaces/presentation/widgets/currency_picker_widget.dart';
import '../../../spaces/domain/usecases/get_space_members_usecase.dart';
import '../../../spaces/domain/usecases/get_space_detail_usecase.dart';

class CreateSettleRequest {
  final String fromUserId;
  final String fromUserName;
  final String toUserId;
  final String toUserName;
  final double amount;
  final String currency;
  final String description;

  CreateSettleRequest({
    required this.fromUserId,
    required this.fromUserName,
    required this.toUserId,
    required this.toUserName,
    required this.amount,
    required this.currency,
    required this.description,
  });
}

class CreateSettleDialog extends StatefulWidget {
  final String spaceId;
  final Function(CreateSettleRequest) onCreateSettle;

  const CreateSettleDialog({
    super.key,
    required this.spaceId,
    required this.onCreateSettle,
  });

  @override
  State<CreateSettleDialog> createState() => _CreateSettleDialogState();
}

class _CreateSettleDialogState extends State<CreateSettleDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();

  String? _selectedToUserId;
  String? _selectedToUserName;
  String _currency = 'USD'; // Will be updated with space currency
  List<Map<String, dynamic>> _spaceMembers = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSpaceMembers();
  }

  Future<void> _loadSpaceMembers() async {
    try {
      final getSpaceMembersUseCase =
          DependencyInjection.getIt<GetSpaceMembersUseCase>();
      final getSpaceDetailUseCase =
          DependencyInjection.getIt<GetSpaceDetailUseCase>();
      final authService = DependencyInjection.getIt<AuthService>();
      final currentUserId = authService.currentUser?.id ?? '';

      // Load space details and members in parallel
      final results = await Future.wait([
        getSpaceDetailUseCase.execute(widget.spaceId),
        getSpaceMembersUseCase.execute(widget.spaceId),
      ]);

      final spaceDetail = results[0] as SpaceDetailResult;
      final members = results[1] as List;

      // Convert to Map format and filter out current user
      final otherMembers = members
          .where((member) => member.userId != currentUserId)
          .map((member) => {
                'userId': member.userId,
                'displayName': member.user.displayName,
              })
          .toList();

      setState(() {
        _currency = spaceDetail.space.currency ??
            'USD'; // Use space currency as default
        _spaceMembers = otherMembers;
        _isLoading = false;

        // Auto-select first member if only one available
        if (otherMembers.length == 1) {
          _selectedToUserId = otherMembers.first['userId'];
          _selectedToUserName = otherMembers.first['displayName'];
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load space data: $e')),
        );
      }
    }
  }

  void _showCurrencyPicker() {
    CurrencyPickerBottomSheet.show(
      context,
      selectedCurrency: _currency,
    ).then((selectedCurrency) {
      if (selectedCurrency != null && mounted) {
        setState(() => _currency = selectedCurrency);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              // Drag handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 16, 16, 16),
                child: Row(
                  children: [
                    const Icon(
                      Icons.account_balance_wallet,
                      color: AppColors.primary,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Record Payment',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      iconSize: 20,
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Column(
                    children: [
                      if (_isLoading)
                        const Center(child: CircularProgressIndicator())
                      else
                        _buildForm(),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // To User Selection
          Text(
            'Pay to',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: _selectedToUserId,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            hint: const Text('Select member'),
            items: _spaceMembers.map((member) {
              return DropdownMenuItem<String>(
                value: member['userId'],
                child: Text(member['displayName'] ?? 'Unknown'),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedToUserId = value;
                _selectedToUserName = _spaceMembers
                    .firstWhere((m) => m['userId'] == value)['displayName'];
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please select a member';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Amount Field
          Text(
            'Amount',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _amountController,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                        RegExp(r'^\d*\.?\d{0,2}')),
                  ],
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    hintText: '0.00',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter an amount';
                    }
                    final amount = double.tryParse(value);
                    if (amount == null || amount <= 0) {
                      return 'Please enter a valid amount';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 12),
              GestureDetector(
                onTap: _showCurrencyPicker,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _currency,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 4),
                      const Icon(Icons.arrow_drop_down, size: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Description Field
          Text(
            'Description (Optional)',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _descriptionController,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              hintText: 'Payment description',
            ),
            maxLines: 2,
          ),

          const SizedBox(height: 24),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: _handleSubmit,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text('Record Payment'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _handleSubmit() {
    if (!_formKey.currentState!.validate()) return;

    final authService = DependencyInjection.getIt<AuthService>();
    final currentUser = authService.currentUser;

    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('User not authenticated')),
      );
      return;
    }

    final amount = double.parse(_amountController.text);
    final description = _descriptionController.text.trim();

    final settleRequest = CreateSettleRequest(
      fromUserId: currentUser.id,
      fromUserName: currentUser.displayName,
      toUserId: _selectedToUserId!,
      toUserName: _selectedToUserName!,
      amount: amount,
      currency: _currency,
      description: description,
    );

    Navigator.pop(context);
    widget.onCreateSettle(settleRequest);
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
}
