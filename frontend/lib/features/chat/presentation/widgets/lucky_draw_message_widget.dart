import 'package:flutter/material.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../auth/domain/services/auth_service.dart';
import '../../../spaces/presentation/widgets/space_theme_provider.dart';
import '../../data/models/message_model.dart';
import '../../data/models/lucky_draw_model.dart';
import '../../domain/services/lucky_draw_service.dart';

class LuckyDrawMessageWidget extends StatefulWidget {
  final MessageModel message;
  final SpaceThemeProvider spaceTheme;
  final Function(String messageId, LuckyDrawData luckyDrawData)? onJoin;
  final Function(String messageId, LuckyDrawData luckyDrawData)? onDraw;

  const LuckyDrawMessageWidget({
    super.key,
    required this.message,
    required this.spaceTheme,
    this.onJoin,
    this.onDraw,
  });

  @override
  State<LuckyDrawMessageWidget> createState() => _LuckyDrawMessageWidgetState();
}

class _LuckyDrawMessageWidgetState extends State<LuckyDrawMessageWidget>
    with TickerProviderStateMixin {
  late LuckyDrawData luckyDrawData;
  bool isJoining = false;
  bool isDrawing = false;
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _initializeLuckyDrawData();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 4.0, // More rotations for better effect
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.elasticOut),
    );

    _colorAnimation = ColorTween(
      begin: AppColors.primary,
      end: Colors.amber,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(LuckyDrawMessageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.message.metadata != widget.message.metadata) {
      _initializeLuckyDrawData();
    }
  }

  void _initializeLuckyDrawData() {
    try {
      if (widget.message.metadata != null) {
        luckyDrawData = LuckyDrawData.fromJson(widget.message.metadata!);
      } else {
        // Fallback lucky draw data with default prize
        luckyDrawData = LuckyDrawData(
          title: widget.message.content,
          prizes: [
            LuckyDrawPrize(
              id: 'default_prize',
              name: 'Default Prize',
              description: 'Default prize for this lucky draw',
              quantity: 1,
              remainingQuantity: 1,
            ),
          ],
          creatorId: widget.message.senderId,
          createdAt: DateTime.now(),
        );
      }
    } catch (e) {
      // Fallback lucky draw data with default prize
      luckyDrawData = LuckyDrawData(
        title: widget.message.content,
        prizes: [
          LuckyDrawPrize(
            id: 'default_prize',
            name: 'Default Prize',
            description: 'Default prize for this lucky draw',
            quantity: 1,
            remainingQuantity: 1,
          ),
        ],
        creatorId: widget.message.senderId,
        createdAt: DateTime.now(),
      );
    }
  }

  void _joinLuckyDraw() async {
    if (!luckyDrawData.canJoin || isJoining) return;

    final authService = DependencyInjection.getIt<AuthService>();
    final currentUser = authService.currentUser;
    if (currentUser == null) return;

    if (!luckyDrawData.canUserJoin(currentUser.id)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('You have already joined this lucky draw'),
        ),
      );
      return;
    }

    setState(() {
      isJoining = true;
    });

    try {
      final luckyDrawService = DependencyInjection.getIt<LuckyDrawService>();
      final updatedLuckyDraw = await luckyDrawService.joinLuckyDraw(
        widget.message.id,
      );

      if (mounted) {
        setState(() {
          luckyDrawData = updatedLuckyDraw;
          isJoining = false;
        });

        // Notify parent component about the join
        widget.onJoin?.call(widget.message.id, updatedLuckyDraw);

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Successfully joined the lucky draw!'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isJoining = false;
        });
        String errorMessage = 'Failed to join lucky draw';
        if (e is LuckyDrawException) {
          errorMessage = e.message;
        } else {
          errorMessage = 'Failed to join lucky draw: $e';
        }
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _performDraw() async {
    if (!luckyDrawData.canDraw || isDrawing) return;

    final authService = DependencyInjection.getIt<AuthService>();
    final currentUser = authService.currentUser;
    if (currentUser == null || currentUser.id != luckyDrawData.creatorId) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Only the creator can perform the draw')),
      );
      return;
    }

    setState(() {
      isDrawing = true;
    });

    // Start animations
    _animationController.forward();
    _pulseController.repeat(reverse: true);

    // Wait for animation to complete
    await Future.delayed(const Duration(milliseconds: 2000));

    try {
      final luckyDrawService = DependencyInjection.getIt<LuckyDrawService>();

      final drawResult = await luckyDrawService.performLuckyDraw(
        widget.message.id,
      );

      final winners = drawResult['winners'] as List<dynamic>;
      final drawnLuckyDraw = drawResult['luckyDrawData'] as LuckyDrawData;

      if (mounted) {
        setState(() {
          luckyDrawData = drawnLuckyDraw;
          isDrawing = false;
        });

        // Reset animations
        _animationController.reset();
        _pulseController.stop();
        _pulseController.reset();

        // Notify parent component about the draw
        widget.onDraw?.call(widget.message.id, drawnLuckyDraw);

        // Show winners
        if (winners.isNotEmpty) {
          final winnersList =
              winners.map((w) => LuckyDrawWinner.fromJson(w)).toList();
          _showWinnersDialog(winnersList);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isDrawing = false;
        });
        _animationController.reset();
        _pulseController.stop();
        _pulseController.reset();
        String errorMessage = 'Failed to perform draw';
        if (e is LuckyDrawException) {
          errorMessage = e.message;
        } else {
          errorMessage = 'Failed to perform draw: $e';
        }
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _showDrawHistory() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.8,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                // Drag handle
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                // Header
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                  child: Row(
                    children: [
                      const Icon(Icons.history, color: Colors.blue, size: 24),
                      const SizedBox(width: 8),
                      const Text(
                        'Draw History',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 18),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                        iconSize: 20,
                      ),
                    ],
                  ),
                ),
                // Content
                Expanded(
                  child: luckyDrawData.drawHistory.isEmpty
                      ? const Center(child: Text('No draw history available'))
                      : ListView.builder(
                          controller: scrollController,
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          itemCount: luckyDrawData.drawHistory.length,
                          itemBuilder: (context, index) {
                            final history = luckyDrawData.drawHistory[index];
                            return Card(
                              margin: const EdgeInsets.symmetric(vertical: 4),
                              child: Padding(
                                padding: const EdgeInsets.all(12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.access_time,
                                          size: 16,
                                          color: Colors.grey[600],
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          _formatDateTime(history.drawnAt),
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey[600],
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    if (history.winners.isNotEmpty) ...[
                                      const Text(
                                        'Winners:',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 14,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      ...history.winners.map(
                                        (winner) => Padding(
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 2,
                                          ),
                                          child: Row(
                                            children: [
                                              const Icon(
                                                Icons.emoji_events,
                                                color: Colors.amber,
                                                size: 16,
                                              ),
                                              const SizedBox(width: 8),
                                              Expanded(
                                                child: Text(
                                                  '${winner.userName} won ${winner.prizeName}',
                                                  style: const TextStyle(
                                                      fontSize: 13),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ] else
                                      const Text(
                                        'No winners in this draw',
                                        style: TextStyle(
                                          color: Colors.grey,
                                          fontSize: 13,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                ),
                // Footer
                Container(
                  padding: const EdgeInsets.all(20),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('Close'),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  String _getPermissionMessage() {
    if (luckyDrawData.isExpired) {
      return 'This lucky draw has expired';
    }

    if (!luckyDrawData.isActive) {
      return 'This lucky draw is no longer active';
    }

    if (luckyDrawData.isInviteOnly) {
      final authService = DependencyInjection.getIt<AuthService>();
      final currentUser = authService.currentUser;
      if (currentUser != null &&
          !luckyDrawData.invitedUserIds.contains(currentUser.id)) {
        return 'This is an invite-only lucky draw and you are not invited';
      }
    }

    if (luckyDrawData.maxParticipants > 0 &&
        luckyDrawData.participants.length >= luckyDrawData.maxParticipants) {
      return 'Maximum number of participants (${luckyDrawData.maxParticipants}) reached';
    }

    final authService = DependencyInjection.getIt<AuthService>();
    final currentUser = authService.currentUser;
    if (currentUser != null && !luckyDrawData.allowMultipleEntries) {
      final hasAlreadyJoined = luckyDrawData.participants.any(
        (p) => p.userId == currentUser.id,
      );
      if (hasAlreadyJoined) {
        return 'You have already joined this lucky draw';
      }
    }

    return 'Unable to join this lucky draw';
  }

  void _showWinnersDialog(List<LuckyDrawWinner> winners) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      enableDrag: false,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.emoji_events, color: Colors.amber, size: 28),
                  const SizedBox(width: 8),
                  const Text(
                    'Lucky Draw Results',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                  ),
                ],
              ),
            ),
            // Content
            Flexible(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        '🎉 Congratulations to the winners! 🎉',
                        style: TextStyle(
                            fontWeight: FontWeight.w600, fontSize: 16),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ConstrainedBox(
                      constraints: const BoxConstraints(maxHeight: 300),
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: winners.length,
                        itemBuilder: (context, index) {
                          final winner = winners[index];
                          return Container(
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.amber.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: Colors.amber.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.emoji_events,
                                  color: Colors.amber,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        winner.userName,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 14,
                                        ),
                                      ),
                                      Text(
                                        'won ${winner.prizeName}',
                                        style: TextStyle(
                                          color: Colors.grey[600],
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 24),
                    // Close Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text('Close'),
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUser = authService.currentUser;
    final isCreator = currentUser?.id == luckyDrawData.creatorId;
    // Check if user has joined or is automatically included (invite-only)
    final hasJoined = currentUser != null &&
        (luckyDrawData.isUserParticipating(currentUser.id) ||
            (luckyDrawData.isInviteOnly &&
                (luckyDrawData.invitedUserIds.contains(currentUser.id) ||
                    currentUser.id == luckyDrawData.creatorId)));

    return Container(
      constraints: const BoxConstraints(maxWidth: 320),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.border,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              AnimatedBuilder(
                animation: Listenable.merge([
                  _rotationAnimation,
                  _pulseAnimation,
                  _colorAnimation,
                ]),
                builder: (context, child) {
                  return Transform.scale(
                    scale: isDrawing ? _pulseAnimation.value : 1.0,
                    child: Transform.rotate(
                      angle: _rotationAnimation.value * 3.14159,
                      child: Icon(
                        Icons.card_giftcard,
                        color: isDrawing
                            ? _colorAnimation.value
                            : AppColors.primary,
                        size: 16,
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(width: 6),
              const Text(
                'Lucky Draw',
                style: TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
              ),
              const SizedBox(width: 8),
              const Spacer(),
              if (luckyDrawData.drawHistory.isNotEmpty)
                GestureDetector(
                  onTap: _showDrawHistory,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.surfaceVariant,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppColors.border,
                      ),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.history,
                          size: 12,
                          color: AppColors.primary,
                        ),
                        SizedBox(width: 2),
                        Text(
                          'History',
                          style: TextStyle(
                            fontSize: 10,
                            color: AppColors.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              const SizedBox(width: 8),
              if (luckyDrawData.isExpired)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Text(
                    'EXPIRED',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 9,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 6),

          // Title
          Text(
            luckyDrawData.title,
            style: const TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
          ),

          if (luckyDrawData.description != null) ...[
            const SizedBox(height: 4),
            Text(
              luckyDrawData.description!,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],

          const SizedBox(height: 8),

          // Prizes
          if (luckyDrawData.prizes.isNotEmpty) ...[
            const Text(
              'Prizes:',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 4),
            ...luckyDrawData.prizes.map(
              (prize) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  children: [
                    Icon(
                      Icons.emoji_events,
                      size: 14,
                      color: prize.isAvailable ? Colors.amber : Colors.grey,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        '${prize.name} (${prize.remainingQuantity}/${prize.quantity})',
                        style: TextStyle(
                          fontSize: 11,
                          color:
                              prize.isAvailable ? Colors.black87 : Colors.grey,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 8),
          ],

          // Statistics
          Row(
            children: [
              Flexible(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.people, size: 12, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Text(
                        '${luckyDrawData.totalParticipants} participants',
                        style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.history, size: 12, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Text(
                        '${luckyDrawData.totalDraws} draws',
                        style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              if (luckyDrawData.maxParticipants > 0) ...[
                const SizedBox(width: 8),
                Flexible(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.group, size: 12, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Flexible(
                        child: Text(
                          'Max: ${luckyDrawData.maxParticipants}',
                          style:
                              TextStyle(fontSize: 11, color: Colors.grey[600]),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),

          // Draw Status Info (for creator when expiry date is set)
          if (isCreator && luckyDrawData.expiresAt != null) ...[
            const SizedBox(height: 6),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: luckyDrawData.isExpired
                    ? Colors.red.withValues(alpha: 0.1)
                    : luckyDrawData.autoDrawOnExpiry
                        ? Colors.orange.withValues(alpha: 0.1)
                        : Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: luckyDrawData.isExpired
                      ? Colors.red.withValues(alpha: 0.3)
                      : luckyDrawData.autoDrawOnExpiry
                          ? Colors.orange.withValues(alpha: 0.3)
                          : Colors.blue.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    luckyDrawData.isExpired
                        ? Icons.schedule
                        : luckyDrawData.autoDrawOnExpiry
                            ? Icons.auto_mode
                            : Icons.schedule,
                    size: 12,
                    color: luckyDrawData.isExpired
                        ? Colors.red
                        : luckyDrawData.autoDrawOnExpiry
                            ? Colors.orange
                            : Colors.blue,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      luckyDrawData.isExpired
                          ? 'Expired - ${luckyDrawData.autoDrawOnExpiry ? "Auto draw completed" : "Ready to draw"}'
                          : luckyDrawData.autoDrawOnExpiry
                              ? 'Auto draw on ${_formatDate(luckyDrawData.expiresAt!)}'
                              : 'Manual draw - Expires ${_formatDate(luckyDrawData.expiresAt!)}',
                      style: TextStyle(
                        fontSize: 10,
                        color: luckyDrawData.isExpired
                            ? Colors.red
                            : luckyDrawData.autoDrawOnExpiry
                                ? Colors.orange[700]
                                : Colors.blue[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 8),

          // Permission Status
          if (!luckyDrawData.canJoin && !hasJoined)
            Container(
              padding: const EdgeInsets.all(8),
              margin: const EdgeInsets.only(bottom: 8),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: Colors.orange,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getPermissionMessage(),
                      style: const TextStyle(
                        fontSize: 11,
                        color: Colors.orange,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Action Buttons
          Row(
            children: [
              // Join Button (only show for non-invite-only mode)
              if (luckyDrawData.canJoin &&
                  !hasJoined &&
                  !luckyDrawData.isInviteOnly)
                Expanded(
                  child: ElevatedButton(
                    onPressed: isJoining ? null : _joinLuckyDraw,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 6),
                      minimumSize: const Size(0, 32),
                    ),
                    child: isJoining
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : const Text(
                            'Join Lucky Draw',
                            style: TextStyle(fontSize: 12),
                          ),
                  ),
                ),

              // Already Joined Indicator (including creator)
              if (hasJoined)
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.green),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          luckyDrawData.isInviteOnly
                              ? Icons.mail
                              : Icons.check_circle,
                          color: Colors.green,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          luckyDrawData.isInviteOnly ? 'Invited' : 'Joined',
                          style: const TextStyle(
                            color: Colors.green,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // Draw Button (Creator only)
              if (isCreator && luckyDrawData.shouldShowDrawButton) ...[
                if (hasJoined || (!luckyDrawData.canJoin && !hasJoined))
                  const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: (isDrawing || !luckyDrawData.canDrawNow)
                        ? null
                        : _performDraw,
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          luckyDrawData.canDrawNow ? Colors.amber : Colors.grey,
                      foregroundColor: luckyDrawData.canDrawNow
                          ? Colors.black87
                          : Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 6),
                      minimumSize: const Size(0, 32),
                    ),
                    child: isDrawing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.black87,
                              ),
                            ),
                          )
                        : Text(
                            luckyDrawData.drawButtonText,
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                  ),
                ),
              ],

              // Cannot Join/Draw Messages
              if (!luckyDrawData.canJoin && !hasJoined && !isCreator)
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 6),
                    child: Text(
                      luckyDrawData.isExpired
                          ? 'Lucky draw has expired'
                          : luckyDrawData.isParticipantLimitReached
                              ? 'Participant limit reached'
                              : luckyDrawData.isInviteOnly &&
                                      currentUser != null &&
                                      !luckyDrawData.invitedUserIds.contains(
                                        currentUser.id,
                                      )
                                  ? 'You are not invited to this lucky draw'
                                  : 'Lucky draw is closed',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[600],
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),

          // Recent Winners
          if (luckyDrawData.drawHistory.isNotEmpty) ...[
            const SizedBox(height: 8),
            const Text(
              'Recent Winners:',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 4),
            ...luckyDrawData.drawHistory.last.winners.take(3).map(
                  (winner) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 1),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.emoji_events,
                          size: 12,
                          color: Colors.amber,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            '${winner.userName} won ${winner.prizeName}',
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            if (luckyDrawData.drawHistory.last.winners.length > 3)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 1),
                child: Text(
                  '... and ${luckyDrawData.drawHistory.last.winners.length - 3} more',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
          ],
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
