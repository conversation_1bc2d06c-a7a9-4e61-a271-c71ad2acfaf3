import 'package:flutter/material.dart';
import '../../data/models/todo_model.dart';
import 'todo_kanban_card.dart';

class TodoKanbanBoard extends StatefulWidget {
  final List<TodoData> todos;
  final Function(TodoData, TodoStatus) onStatusChanged;
  final Function(TodoData)? onEditTodo;
  final Function(TodoData)? onDeleteTodo;
  final VoidCallback? onRefresh;
  final VoidCallback? onFilterSort;
  final bool hasActiveFilters;

  const TodoKanbanBoard({
    super.key,
    required this.todos,
    required this.onStatusChanged,
    this.onEditTodo,
    this.onDeleteTodo,
    this.onRefresh,
    this.onFilterSort,
    this.hasActiveFilters = false,
  });

  @override
  State<TodoKanbanBoard> createState() => _TodoKanbanBoardState();
}

class _TodoKanbanBoardState extends State<TodoKanbanBoard> {
  // Track expanded state of cards
  final Set<String> _expandedCards = <String>{};

  void _toggleCardExpanded(String todoId) {
    setState(() {
      if (_expandedCards.contains(todoId)) {
        _expandedCards.remove(todoId);
      } else {
        _expandedCards.add(todoId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final pendingTodos = widget.todos
        .where((todo) => todo.status == TodoStatus.pending)
        .toList();
    final inProgressTodos = widget.todos
        .where((todo) => todo.status == TodoStatus.inProgress)
        .toList();
    final completedTodos = widget.todos
        .where((todo) => todo.status == TodoStatus.completed)
        .toList();

    return RefreshIndicator(
      onRefresh: () async {
        widget.onRefresh?.call();
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Container(
          height: MediaQuery.of(context).size.height -
              200, // Adjust based on app bar height
          padding: const EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Pending Column
              Expanded(
                child: _buildColumn(
                  title: 'Pending',
                  todos: pendingTodos,
                  status: TodoStatus.pending,
                  color: const Color(0xFFFF9800), // Orange
                ),
              ),
              const SizedBox(width: 8),
              // In Progress Column
              Expanded(
                child: _buildColumn(
                  title: 'In Progress',
                  todos: inProgressTodos,
                  status: TodoStatus.inProgress,
                  color: const Color(0xFF2196F3), // Blue
                ),
              ),
              const SizedBox(width: 8),
              // Completed Column
              Expanded(
                child: _buildColumn(
                  title: 'Completed',
                  todos: completedTodos,
                  status: TodoStatus.completed,
                  color: const Color(0xFF4CAF50), // Green
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildColumn({
    required String title,
    required List<TodoData> todos,
    required TodoStatus status,
    required Color color,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Column Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              border: Border(
                bottom: BorderSide(
                  color: color.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _getStatusIcon(status),
                  color: color,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).textTheme.titleMedium?.color,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: widget.onFilterSort,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: widget.hasActiveFilters
                          ? Theme.of(context)
                              .primaryColor
                              .withValues(alpha: 0.2)
                          : color.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: widget.hasActiveFilters
                          ? Border.all(
                              color: Theme.of(context).primaryColor,
                              width: 1,
                            )
                          : null,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '${todos.length}',
                          style: TextStyle(
                            color: widget.hasActiveFilters
                                ? Theme.of(context).primaryColor
                                : color,
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (widget.onFilterSort != null) ...[
                          const SizedBox(width: 2),
                          Icon(
                            Icons.tune,
                            size: 12,
                            color: widget.hasActiveFilters
                                ? Theme.of(context).primaryColor
                                : color,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Drag Target Area
          Expanded(
            child: DragTarget<TodoData>(
              onAcceptWithDetails: (details) {
                final todo = details.data;
                if (todo.status != status) {
                  widget.onStatusChanged(todo, status);
                }
              },
              onWillAcceptWithDetails: (details) =>
                  details.data.status != status,
              builder: (context, candidateData, rejectedData) {
                final isHighlighted = candidateData.isNotEmpty;
                return Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: isHighlighted
                        ? color.withValues(alpha: 0.1)
                        : Colors.transparent,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  child: todos.isEmpty
                      ? _buildEmptyState(status)
                      : ListView.builder(
                          padding: const EdgeInsets.all(8),
                          itemCount: todos.length,
                          itemBuilder: (context, index) {
                            final todo = todos[index];
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 6),
                              child: Draggable<TodoData>(
                                data: todo,
                                feedback: Material(
                                  elevation: 12,
                                  borderRadius: BorderRadius.circular(8),
                                  child: Transform.scale(
                                    scale: 1.05,
                                    child: SizedBox(
                                      width: 250,
                                      child: TodoKanbanCard(
                                        todo: todo,
                                        isDragging: true,
                                        isExpanded:
                                            _expandedCards.contains(todo.id),
                                      ),
                                    ),
                                  ),
                                ),
                                childWhenDragging: Transform.scale(
                                  scale: 0.95,
                                  child: Opacity(
                                    opacity: 0.3,
                                    child: TodoKanbanCard(
                                      todo: todo,
                                      isExpanded:
                                          _expandedCards.contains(todo.id),
                                    ),
                                  ),
                                ),
                                child: TodoKanbanCard(
                                  todo: todo,
                                  isExpanded: _expandedCards.contains(todo.id),
                                  onToggleExpanded: () =>
                                      _toggleCardExpanded(todo.id),
                                  onLongPress: () =>
                                      _showTodoMenu(context, todo),
                                ),
                              ),
                            );
                          },
                        ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(TodoStatus status) {
    String message;
    IconData icon;

    switch (status) {
      case TodoStatus.pending:
        message = 'No pending tasks';
        icon = Icons.inbox_outlined;
        break;
      case TodoStatus.inProgress:
        message = 'No tasks in progress';
        icon = Icons.work_outline;
        break;
      case TodoStatus.completed:
        message = 'No completed tasks';
        icon = Icons.check_circle_outline;
        break;
      case TodoStatus.cancelled:
        message = 'No cancelled tasks';
        icon = Icons.cancel_outlined;
        break;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getStatusIcon(TodoStatus status) {
    switch (status) {
      case TodoStatus.pending:
        return Icons.radio_button_unchecked;
      case TodoStatus.inProgress:
        return Icons.play_circle_outline;
      case TodoStatus.completed:
        return Icons.check_circle;
      case TodoStatus.cancelled:
        return Icons.cancel;
    }
  }

  void _showTodoMenu(BuildContext context, TodoData todo) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),

            // Todo title
            Text(
              todo.title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 24),

            // Menu options
            ListTile(
              leading: const Icon(Icons.edit, color: Colors.blue),
              title: const Text('Edit TODO'),
              onTap: () {
                Navigator.pop(context);
                widget.onEditTodo?.call(todo);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete TODO'),
              onTap: () {
                Navigator.pop(context);
                _showDeleteConfirmation(context, todo);
              },
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, TodoData todo) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.delete, color: Colors.red),
                  const SizedBox(width: 8),
                  const Text(
                    'Delete TODO',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Text(
                'Are you sure you want to delete "${todo.title}"?',
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ),
            // Actions
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  top: BorderSide(color: Colors.grey[200]!),
                ),
              ),
              child: SafeArea(
                child: Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('Cancel'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          widget.onDeleteTodo?.call(todo);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Delete'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
