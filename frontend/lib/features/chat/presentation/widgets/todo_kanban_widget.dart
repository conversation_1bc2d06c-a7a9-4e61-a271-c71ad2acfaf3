import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../data/datasources/todo_api_service.dart';
import '../../data/models/todo_model.dart';
import 'create_todo_dialog.dart';
import 'edit_todo_dialog.dart';
import 'todo_kanban_board.dart';
import 'todo_filter_dialog.dart';
import 'todo_sort_dialog.dart';
import 'dart:async';

class TodoKanbanWidget extends StatefulWidget {
  final String spaceId;
  final String spaceName;

  const TodoKanbanWidget({
    super.key,
    required this.spaceId,
    required this.spaceName,
  });

  @override
  State<TodoKanbanWidget> createState() => _TodoKanbanWidgetState();
}

class _TodoKanbanWidgetState extends State<TodoKanbanWidget> {
  late TodoApiService _todoApiService;

  List<TodoData> _todos = [];
  List<TodoData> _filteredTodos = [];
  bool _isLoading = false;
  String? _error;
  TodoFilterOptions? _currentFilters;
  TodoSortOptions _currentSort = TodoSortOptions(
    sortBy: TodoSortBy.createdAt,
    sortOrder: TodoSortOrder.desc,
  );
  List<String> _availableTags = [];

  @override
  void initState() {
    super.initState();
    _todoApiService = GetIt.instance<TodoApiService>();
    _loadTodos();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _loadTodos({bool refresh = false}) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await _todoApiService.getTodos(
        widget.spaceId,
        tags: _currentFilters?.tags,
        dueBefore: _currentFilters?.dueBefore?.toIso8601String(),
        dueAfter: _currentFilters?.dueAfter?.toIso8601String(),
        sortBy: _currentSort.sortByString,
        sortOrder: _currentSort.sortOrderString,
      );

      if (response.success && response.data != null) {
        setState(() {
          _todos = response.data!.todos;
          _applyLocalFilters();
          _extractAvailableTags();
        });
      } else {
        setState(() {
          _error = 'Failed to load TODOs';
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading TODOs: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createTodo(CreateTodoRequest todoRequest) async {
    try {
      final response = await _todoApiService.createTodo(
        widget.spaceId,
        todoRequest,
      );

      if (response.success && response.data != null) {
        setState(() {
          _todos.insert(0, response.data!);
          // Apply filters to update the filtered list
          _applyLocalFilters();
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('TODO created successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating TODO: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _applyLocalFilters() {
    _filteredTodos = _todos.where((todo) {
      // Apply status filter
      if (_currentFilters?.statuses != null &&
          _currentFilters!.statuses!.isNotEmpty &&
          !_currentFilters!.statuses!.contains(todo.status)) {
        return false;
      }

      // Apply location filter
      if (_currentFilters?.hasLocation != null) {
        final hasLocation = todo.location != null && todo.location!.isNotEmpty;
        if (_currentFilters!.hasLocation! != hasLocation) {
          return false;
        }
      }

      // Apply due date filter
      if (_currentFilters?.hasDueDate != null) {
        final hasDueDate = todo.dueDate != null;
        if (_currentFilters!.hasDueDate! != hasDueDate) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  void _extractAvailableTags() {
    final tagSet = <String>{};
    for (final todo in _todos) {
      for (final tag in todo.tags) {
        tagSet.add(tag.name);
      }
    }
    _availableTags = tagSet.toList()..sort();
  }

  void _showCreateTodoDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreateTodoDialog(
        spaceId: widget.spaceId,
        onCreateTodo: _createTodo,
      ),
    );
  }

  Widget _buildIntegratedFilterSortSheet() {
    return Material(
      color: Colors.transparent,
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.9,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                _buildDragHandle(),
                _buildSheetHeader(),
                Expanded(
                  child: _buildSheetContent(scrollController),
                ),
                _buildSheetActions(),
              ],
            );
          },
        ),
      ),
    );
  }

  void _showFilterSortMenu() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildIntegratedFilterSortSheet(),
    );
  }

  Widget _buildDragHandle() {
    return Container(
      width: 40,
      height: 4,
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildSheetHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          const Text(
            'Filter & Sort',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          if (_currentFilters?.hasActiveFilters == true)
            TextButton(
              onPressed: () {
                setState(() {
                  _currentFilters = null;
                });
                _loadTodos(refresh: true);
                Navigator.pop(context);
              },
              child: const Text('Clear All'),
            ),
        ],
      ),
    );
  }

  Widget _buildSheetContent(ScrollController scrollController) {
    return SingleChildScrollView(
      controller: scrollController,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSortSection(),
          const SizedBox(height: 24),
          _buildFilterSection(),
        ],
      ),
    );
  }

  Widget _buildSheetActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                _loadTodos(refresh: true);
                Navigator.pop(context);
              },
              child: const Text('Apply'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSortSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Sort By',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...TodoSortBy.values.map((sortBy) {
          return RadioListTile<TodoSortBy>(
            title: Text(_getSortByDisplayName(sortBy)),
            value: sortBy,
            groupValue: _currentSort.sortBy,
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _currentSort = _currentSort.copyWith(sortBy: value);
                });
              }
            },
            contentPadding: EdgeInsets.zero,
          );
        }),
        const SizedBox(height: 16),
        const Text(
          'Sort Order',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        RadioListTile<TodoSortOrder>(
          title: const Text('Ascending (A-Z, Oldest first)'),
          value: TodoSortOrder.asc,
          groupValue: _currentSort.sortOrder,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _currentSort = _currentSort.copyWith(sortOrder: value);
              });
            }
          },
          contentPadding: EdgeInsets.zero,
        ),
        RadioListTile<TodoSortOrder>(
          title: const Text('Descending (Z-A, Newest first)'),
          value: TodoSortOrder.desc,
          groupValue: _currentSort.sortOrder,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _currentSort = _currentSort.copyWith(sortOrder: value);
              });
            }
          },
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  String _getSortByDisplayName(TodoSortBy sortBy) {
    switch (sortBy) {
      case TodoSortBy.createdAt:
        return 'Created Date';
      case TodoSortBy.dueDate:
        return 'Due Date';
      case TodoSortBy.title:
        return 'Title';
      case TodoSortBy.status:
        return 'Status';
      case TodoSortBy.priority:
        return 'Priority';
    }
  }

  Widget _buildFilterSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Filter',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),

        // Tags Filter
        if (_availableTags.isNotEmpty) ...[
          const Text(
            'Tags',
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: _availableTags.map((tag) {
              final isSelected = _currentFilters?.tags?.contains(tag) ?? false;
              return FilterChip(
                label: Text(tag),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    _currentFilters ??= TodoFilterOptions();
                    final currentTags =
                        List<String>.from(_currentFilters!.tags ?? []);
                    if (selected) {
                      currentTags.add(tag);
                    } else {
                      currentTags.remove(tag);
                    }
                    _currentFilters = _currentFilters!.copyWith(
                      tags: currentTags.isEmpty ? null : currentTags,
                    );
                  });
                },
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
        ],

        // Status Filter
        const Text(
          'Status',
          style: TextStyle(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: TodoStatus.values.map((status) {
            final isSelected =
                _currentFilters?.statuses?.contains(status) ?? false;
            return FilterChip(
              label: Text(_getStatusDisplayName(status)),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _currentFilters ??= TodoFilterOptions();
                  final currentStatuses =
                      List<TodoStatus>.from(_currentFilters!.statuses ?? []);
                  if (selected) {
                    currentStatuses.add(status);
                  } else {
                    currentStatuses.remove(status);
                  }
                  _currentFilters = _currentFilters!.copyWith(
                    statuses: currentStatuses.isEmpty ? null : currentStatuses,
                  );
                });
              },
            );
          }).toList(),
        ),
        const SizedBox(height: 16),

        // Additional Filters
        const Text(
          'Additional Filters',
          style: TextStyle(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        CheckboxListTile(
          title: const Text('Has Location'),
          value: _currentFilters?.hasLocation,
          tristate: true,
          onChanged: (value) {
            setState(() {
              _currentFilters ??= TodoFilterOptions();
              _currentFilters = _currentFilters!.copyWith(hasLocation: value);
            });
          },
          contentPadding: EdgeInsets.zero,
        ),
        CheckboxListTile(
          title: const Text('Has Due Date'),
          value: _currentFilters?.hasDueDate,
          tristate: true,
          onChanged: (value) {
            setState(() {
              _currentFilters ??= TodoFilterOptions();
              _currentFilters = _currentFilters!.copyWith(hasDueDate: value);
            });
          },
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  String _getStatusDisplayName(TodoStatus status) {
    switch (status) {
      case TodoStatus.pending:
        return 'Pending';
      case TodoStatus.inProgress:
        return 'In Progress';
      case TodoStatus.completed:
        return 'Completed';
      case TodoStatus.cancelled:
        return 'Cancelled';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadTodos(refresh: true),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_isLoading && _todos.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_todos.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.checklist,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No TODOs found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first TODO to get started',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _showCreateTodoDialog,
              icon: const Icon(Icons.add),
              label: const Text('Create TODO'),
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        TodoKanbanBoard(
          todos: _filteredTodos.isNotEmpty ||
                  _currentFilters?.hasActiveFilters == true
              ? _filteredTodos
              : _todos,
          onStatusChanged: _updateTodoStatus,
          onEditTodo: _showEditTodoDialog,
          onDeleteTodo: _deleteTodo,
          onRefresh: () => _loadTodos(refresh: true),
          onFilterSort: _showFilterSortMenu,
          hasActiveFilters: _currentFilters?.hasActiveFilters == true,
        ),
        Positioned(
          bottom: 16,
          right: 16,
          child: FloatingActionButton(
            onPressed: _showCreateTodoDialog,
            child: const Icon(Icons.add),
          ),
        ),
      ],
    );
  }

  Future<void> _updateTodoStatus(TodoData todo, TodoStatus newStatus) async {
    // Optimistically update UI
    setState(() {
      final index = _todos.indexWhere((t) => t.id == todo.id);
      if (index != -1) {
        _todos[index] = todo.copyWith(
          status: newStatus,
          completedAt:
              newStatus == TodoStatus.completed ? DateTime.now() : null,
        );
        // Apply filters to update the filtered list
        _applyLocalFilters();
      }
    });

    try {
      // Use updateTodo API with status change
      final updateRequest = UpdateTodoRequest(status: newStatus);
      final response = await _todoApiService.updateTodo(
        widget.spaceId,
        todo.id,
        updateRequest,
      );

      if (!response.success) {
        // Revert the optimistic update
        setState(() {
          final index = _todos.indexWhere((t) => t.id == todo.id);
          if (index != -1) {
            _todos[index] = todo; // Revert to original
            // Apply filters to update the filtered list
            _applyLocalFilters();
          }
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Failed to update TODO status: ${response.message ?? 'Unknown error'}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content:
                  Text('TODO moved to ${_getStatusDisplayName(newStatus)}'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 1),
            ),
          );
        }
      }
    } catch (e) {
      // Revert the optimistic update
      setState(() {
        final index = _todos.indexWhere((t) => t.id == todo.id);
        if (index != -1) {
          _todos[index] = todo; // Revert to original
          // Apply filters to update the filtered list
          _applyLocalFilters();
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating TODO: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showEditTodoDialog(TodoData todo) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => EditTodoDialog(
        spaceId: widget.spaceId,
        todo: todo,
        onUpdateTodo: (UpdateTodoRequest updateRequest) =>
            _updateTodo(todo, updateRequest),
      ),
    );
  }

  Future<void> _updateTodo(
      TodoData todo, UpdateTodoRequest updateRequest) async {
    try {
      final response = await _todoApiService.updateTodo(
        widget.spaceId,
        todo.id,
        updateRequest,
      );

      if (response.success && response.data != null) {
        setState(() {
          final index = _todos.indexWhere((t) => t.id == todo.id);
          if (index != -1) {
            _todos[index] = response.data!;
            // Apply filters to update the filtered list
            _applyLocalFilters();
          }
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('TODO updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Failed to update TODO: ${response.message ?? 'Unknown error'}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating TODO: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteTodo(TodoData todo) async {
    try {
      final response =
          await _todoApiService.deleteTodo(widget.spaceId, todo.id);

      if (response.success) {
        setState(() {
          _todos.removeWhere((t) => t.id == todo.id);
          // Apply filters to update the filtered list
          _applyLocalFilters();
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('TODO deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Failed to delete TODO: ${response.message ?? 'Unknown error'}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting TODO: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
