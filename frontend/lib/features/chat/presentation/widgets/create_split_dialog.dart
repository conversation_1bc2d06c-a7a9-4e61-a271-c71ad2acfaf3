import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../spaces/data/models/space_member_model.dart';
import '../../../spaces/domain/usecases/get_space_members_usecase.dart';
import '../../../spaces/domain/usecases/get_space_detail_usecase.dart';
import '../../../spaces/presentation/widgets/currency_picker_widget.dart';
import '../../data/models/split_model.dart';

class CreateSplitDialog extends StatefulWidget {
  final String spaceId;
  final Function(CreateSplitRequest) onCreateSplit;

  const CreateSplitDialog({
    super.key,
    required this.spaceId,
    required this.onCreateSplit,
  });

  @override
  State<CreateSplitDialog> createState() => _CreateSplitDialogState();
}

class _CreateSplitDialogState extends State<CreateSplitDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _currency = 'USD'; // Will be updated with space currency
  String _category = 'Food';
  SplitType _splitType = SplitType.equal;
  String? _paidByUserId;
  DateTime _selectedDate = DateTime.now();
  List<SpaceMemberModel> _spaceMembers = [];
  final List<String> _selectedParticipants = [];
  final Map<String, double> _customAmounts = {};
  bool _isLoading = false;

  final Map<String, Map<String, dynamic>> _categories = {
    'Flights': {'icon': '✈️', 'color': 0xFF2196F3},
    'Lodging': {'icon': '🏨', 'color': 0xFF4CAF50},
    'Car rental': {'icon': '🚗', 'color': 0xFFFF9800},
    'Transit': {'icon': '🚌', 'color': 0xFF9C27B0},
    'Food': {'icon': '🍽️', 'color': 0xFFE91E63},
    'Drinks': {'icon': '🥤', 'color': 0xFF00BCD4},
    'Sightseeing': {'icon': '🏛️', 'color': 0xFF795548},
    'Activities': {'icon': '🎯', 'color': 0xFF607D8B},
    'Shopping': {'icon': '🛍️', 'color': 0xFFFF5722},
    'Gas': {'icon': '⛽', 'color': 0xFF3F51B5},
    'Groceries': {'icon': '🛒', 'color': 0xFF8BC34A},
    'Other': {'icon': '📋', 'color': 0xFF9E9E9E},
  };

  @override
  void initState() {
    super.initState();
    _loadSpaceData();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadSpaceData() async {
    setState(() => _isLoading = true);
    try {
      // Load space details to get currency
      final getSpaceDetailUseCase =
          DependencyInjection.getIt<GetSpaceDetailUseCase>();
      final space = await getSpaceDetailUseCase.execute(widget.spaceId);

      // Load space members
      final getMembersUseCase =
          DependencyInjection.getIt<GetSpaceMembersUseCase>();
      final members = await getMembersUseCase.execute(widget.spaceId);

      setState(() {
        _currency =
            space.space.currency ?? 'USD'; // Use space currency as default
        _spaceMembers = members;
        if (members.isNotEmpty) {
          _paidByUserId = members.first.userId; // Default to first member
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load space data: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showCurrencyPicker() {
    CurrencyPickerBottomSheet.show(
      context,
      selectedCurrency: _currency,
    ).then((selectedCurrency) {
      if (selectedCurrency != null && mounted) {
        setState(() => _currency = selectedCurrency);
      }
    });
  }

  void _createSplit() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_paidByUserId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select who paid')),
      );
      return;
    }

    // Only validate participants if not no-split
    if (_splitType != SplitType.noSplit && _selectedParticipants.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select participants')),
      );
      return;
    }

    final totalAmount = double.tryParse(_amountController.text) ?? 0.0;
    if (totalAmount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a valid amount')),
      );
      return;
    }

    // Validate custom amounts if custom split type
    if (_splitType == SplitType.custom) {
      final customTotal =
          _customAmounts.values.fold(0.0, (sum, amount) => sum + amount);
      if ((customTotal - totalAmount).abs() > 0.01) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Custom amounts must equal total amount')),
        );
        return;
      }
    }

    // For no-split, only the payer is a participant
    final participants = _splitType == SplitType.noSplit
        ? [_paidByUserId!]
        : _selectedParticipants;

    final splitRequest = CreateSplitRequest(
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      category: _category,
      totalAmount: totalAmount,
      currency: _currency,
      paidByUserId: _paidByUserId!,
      splitType: _splitType,
      participantUserIds: participants,
      customAmounts: _splitType == SplitType.custom ? _customAmounts : null,
      date: _selectedDate,
    );

    try {
      widget.onCreateSplit(splitRequest);
      Navigator.pop(context);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error creating split: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.7,
        maxChildSize: 0.95,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              // Drag handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              _buildHeader(),

              // Content
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _buildContent(scrollController),
              ),

              // Footer
              _buildFooter(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
      child: Row(
        children: [
          const Icon(
            Icons.receipt_long,
            color: AppColors.primary,
            size: 24,
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'Add Expense',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close),
            iconSize: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildContent(ScrollController scrollController) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          controller: scrollController,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title input with category icon (moved to top)
              _buildTitleInput(),
              const SizedBox(height: 8),

              // Amount input
              _buildAmountInput(),
              const SizedBox(height: 8),

              // Date selection
              _buildDateSelection(),
              const SizedBox(height: 8),

              // Paid by selection
              _buildPaidBySelection(),
              const SizedBox(height: 8),

              // Split type selection
              _buildSplitTypeSelection(),
              const SizedBox(height: 8),

              // Participants and amount calculation area
              _buildParticipantsAndCalculation(),

              const SizedBox(height: 8),

              // Description input
              _buildDescriptionInput(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAmountInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            // Currency selector
            GestureDetector(
              onTap: _showCurrencyPicker,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    bottomLeft: Radius.circular(8),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _currency,
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(width: 4),
                    const Icon(Icons.arrow_drop_down, size: 20),
                  ],
                ),
              ),
            ),

            // Amount input
            Expanded(
              child: TextFormField(
                controller: _amountController,
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                ],
                onChanged: (value) {
                  setState(
                      () {}); // Trigger rebuild to update amount calculation
                },
                decoration: InputDecoration(
                  hintText: '0',
                  hintStyle:
                      TextStyle(color: Colors.grey.shade400, fontSize: 16),
                  border: OutlineInputBorder(
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  focusedBorder: const OutlineInputBorder(
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                    borderSide: BorderSide(color: AppColors.primary),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                style: const TextStyle(fontSize: 16),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an amount';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'Please enter a valid amount';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNoSplitCalculation(double totalAmount) {
    final paidByUser = _spaceMembers.firstWhere(
      (member) => member.userId == _paidByUserId,
      orElse: () => const SpaceMemberModel(
        id: '',
        userId: '',
        role: SpaceMemberRole.member,
        user: SpaceMemberUserModel(
          id: '',
          displayName: 'Unknown',
          email: '',
        ),
      ),
    );

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.person, color: Colors.blue.shade600, size: 16),
          const SizedBox(width: 8),
          Text(
            '${paidByUser.user.displayName} pays $_currency${totalAmount.toStringAsFixed(2)}',
            style: TextStyle(
              color: Colors.blue.shade700,
              fontWeight: FontWeight.w500,
              fontSize: 13,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildParticipantsAndCalculation() {
    final totalAmount = double.tryParse(_amountController.text) ?? 0.0;

    if (_splitType == SplitType.noSplit) {
      // For no split, only show the calculation without participants
      return totalAmount > 0
          ? _buildNoSplitCalculation(totalAmount)
          : const SizedBox.shrink();
    }

    // For other split types, show participants and calculation
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Participants section
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Participants',
                  style: TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
                ),
                const SizedBox(height: 8),
                ..._spaceMembers.map((member) {
                  final isSelected =
                      _selectedParticipants.contains(member.userId);
                  return CheckboxListTile(
                    value: isSelected,
                    onChanged: (value) {
                      setState(() {
                        if (value == true) {
                          _selectedParticipants.add(member.userId);
                        } else {
                          _selectedParticipants.remove(member.userId);
                          _customAmounts.remove(member.userId);
                        }
                      });
                    },
                    title: Row(
                      children: [
                        CircleAvatar(
                          radius: 16,
                          backgroundColor: AppColors.primary,
                          child: Text(
                            member.user.displayName[0].toUpperCase(),
                            style: const TextStyle(
                                color: Colors.white, fontSize: 12),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(child: Text(member.user.displayName)),
                        // Show amount for custom split
                        if (_splitType == SplitType.custom && isSelected) ...[
                          SizedBox(
                            width: 80,
                            child: TextFormField(
                              keyboardType:
                                  const TextInputType.numberWithOptions(
                                      decimal: true),
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(
                                    RegExp(r'^\d*\.?\d{0,2}')),
                              ],
                              decoration: InputDecoration(
                                hintText: '0.00',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                isDense: true,
                              ),
                              style: const TextStyle(fontSize: 12),
                              onChanged: (value) {
                                setState(() {
                                  _customAmounts[member.userId] =
                                      double.tryParse(value) ?? 0.0;
                                });
                              },
                              initialValue:
                                  _customAmounts[member.userId]?.toString() ??
                                      '',
                            ),
                          ),
                        ],
                      ],
                    ),
                    activeColor: AppColors.primary,
                    dense: true,
                    contentPadding: EdgeInsets.zero,
                  );
                }),
              ],
            ),
          ),

          // Calculation section
          if (totalAmount > 0 && _selectedParticipants.isNotEmpty) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(12),
              child: _buildSplitCalculationSummary(totalAmount),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSplitCalculationSummary(double totalAmount) {
    switch (_splitType) {
      case SplitType.equal:
        return _buildEqualSplitSummary(totalAmount);
      case SplitType.custom:
        return _buildCustomSplitSummary(totalAmount);
      case SplitType.percentage:
        return _buildPercentageSplitSummary(totalAmount);
      case SplitType.noSplit:
        return const SizedBox.shrink();
    }
  }

  Widget _buildEqualSplitSummary(double totalAmount) {
    final perPerson = totalAmount / _selectedParticipants.length;
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          Icon(Icons.calculate, color: Colors.green.shade600, size: 16),
          const SizedBox(width: 8),
          Text(
            '${_selectedParticipants.length} people × $_currency${perPerson.toStringAsFixed(2)} each',
            style: TextStyle(
              color: Colors.green.shade700,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomSplitSummary(double totalAmount) {
    final allocatedAmount =
        _customAmounts.values.fold(0.0, (sum, amount) => sum + amount);
    final remaining = totalAmount - allocatedAmount;
    final isComplete =
        remaining.abs() < 0.01; // Allow small rounding differences

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: isComplete ? Colors.green.shade50 : Colors.orange.shade50,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isComplete ? Icons.check_circle : Icons.warning,
                color:
                    isComplete ? Colors.green.shade600 : Colors.orange.shade600,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Allocated: $_currency${allocatedAmount.toStringAsFixed(2)}',
                style: TextStyle(
                  color: isComplete
                      ? Colors.green.shade700
                      : Colors.orange.shade700,
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          if (!isComplete) ...[
            const SizedBox(height: 4),
            Text(
              'Remaining: $_currency${remaining.toStringAsFixed(2)}',
              style: TextStyle(
                color: Colors.orange.shade700,
                fontSize: 11,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPercentageSplitSummary(double totalAmount) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          Icon(Icons.percent, color: Colors.purple.shade600, size: 16),
          const SizedBox(width: 8),
          Text(
            'Total: $_currency${totalAmount.toStringAsFixed(2)}',
            style: TextStyle(
              color: Colors.purple.shade700,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleInput() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: GestureDetector(
          onTap: _showCategoryPicker,
          child: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              _categories[_category]?['icon'] ?? '📋',
              style: const TextStyle(fontSize: 24),
            ),
          ),
        ),
        title: TextFormField(
          controller: _titleController,
          decoration: const InputDecoration(
            hintText: 'Select item',
            border: InputBorder.none,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter a title';
            }
            return null;
          },
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      ),
    );
  }

  Widget _buildPaidBySelection() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        title: const Text('Paid by',
            style: TextStyle(fontWeight: FontWeight.w500)),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_paidByUserId != null) ...[
              CircleAvatar(
                radius: 12,
                backgroundColor: AppColors.primary,
                child: Text(
                  _spaceMembers
                      .firstWhere((m) => m.userId == _paidByUserId)
                      .user
                      .displayName[0]
                      .toUpperCase(),
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                _spaceMembers
                    .firstWhere((m) => m.userId == _paidByUserId)
                    .user
                    .displayName,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(width: 8),
            ],
            const Icon(Icons.arrow_drop_down),
          ],
        ),
        onTap: () => _showPaidBySelector(),
      ),
    );
  }

  Widget _buildSplitTypeSelection() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        title:
            const Text('Split', style: TextStyle(fontWeight: FontWeight.w500)),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _getSplitTypeText(),
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(width: 8),
            const Icon(Icons.arrow_drop_down),
          ],
        ),
        onTap: () => _showSplitTypeSelector(),
      ),
    );
  }

  Widget _buildDateSelection() {
    return GestureDetector(
      onTap: _showDatePicker,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            const Icon(
              Icons.calendar_today,
              color: AppColors.primary,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Date',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    _formatDate(_selectedDate),
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_drop_down,
              color: Colors.grey.shade600,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionInput() {
    return TextFormField(
      controller: _descriptionController,
      decoration: InputDecoration(
        labelText: 'Description (Optional)',
        hintText: 'Add a note...',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      ),
      maxLines: 1,
      maxLength: 100,
    );
  }

  void _showDatePicker() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: AppColors.primary,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final selectedDay = DateTime(date.year, date.month, date.day);

    if (selectedDay == today) {
      return 'Today';
    } else if (selectedDay == yesterday) {
      return 'Yesterday';
    } else {
      // Format as "Jan 19, 2025"
      const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec'
      ];
      return '${months[date.month - 1]} ${date.day}, ${date.year}';
    }
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _createSplit,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'Save',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ),
    );
  }

  void _showPaidBySelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Who paid?',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ..._spaceMembers.map((member) {
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppColors.primary,
                  child: Text(
                    member.user.displayName[0].toUpperCase(),
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                title: Text(member.user.displayName),
                trailing: _paidByUserId == member.userId
                    ? const Icon(Icons.check, color: AppColors.primary)
                    : null,
                onTap: () {
                  setState(() => _paidByUserId = member.userId);
                  Navigator.pop(context);
                },
              );
            }),
          ],
        ),
      ),
    );
  }

  void _showCategoryPicker() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Select Category',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 4,
                  childAspectRatio: 1.0,
                  crossAxisSpacing: 6,
                  mainAxisSpacing: 6,
                ),
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final categoryName = _categories.keys.elementAt(index);
                  final categoryData = _categories[categoryName]!;
                  final isSelected = _category == categoryName;

                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _category = categoryName;
                      });
                      Navigator.pop(context);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Color(categoryData['color'])
                                .withValues(alpha: 0.2)
                            : Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(8),
                        border: isSelected
                            ? Border.all(
                                color: Color(categoryData['color']), width: 2)
                            : Border.all(color: Colors.grey.shade300),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            categoryData['icon'],
                            style: const TextStyle(fontSize: 24),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            categoryName,
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                              color: isSelected
                                  ? Color(categoryData['color'])
                                  : Colors.black87,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSplitTypeSelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'How to split?',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              title: const Text('No split'),
              subtitle: const Text('Record expense without splitting'),
              trailing: _splitType == SplitType.noSplit
                  ? const Icon(Icons.check, color: AppColors.primary)
                  : null,
              onTap: () {
                setState(() => _splitType = SplitType.noSplit);
                Navigator.pop(context);
              },
            ),
            ListTile(
              title: const Text('Split equally'),
              subtitle: const Text('Everyone pays the same amount'),
              trailing: _splitType == SplitType.equal
                  ? const Icon(Icons.check, color: AppColors.primary)
                  : null,
              onTap: () {
                setState(() => _splitType = SplitType.equal);
                Navigator.pop(context);
              },
            ),
            ListTile(
              title: const Text('Custom amounts'),
              subtitle: const Text('Set different amounts for each person'),
              trailing: _splitType == SplitType.custom
                  ? const Icon(Icons.check, color: AppColors.primary)
                  : null,
              onTap: () {
                setState(() => _splitType = SplitType.custom);
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  String _getSplitTypeText() {
    switch (_splitType) {
      case SplitType.noSplit:
        return 'No split';
      case SplitType.equal:
        return 'Split equally';
      case SplitType.custom:
        return 'Custom amounts';
      case SplitType.percentage:
        return 'By percentage';
    }
  }
}
