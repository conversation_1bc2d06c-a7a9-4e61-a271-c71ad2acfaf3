import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../data/models/todo_model.dart';

class TodoListItemWidget extends StatelessWidget {
  final TodoData todo;
  final VoidCallback? onTap;
  final Function(TodoStatus)? onStatusChanged;

  const TodoListItemWidget({
    super.key,
    required this.todo,
    this.onTap,
    this.onStatusChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 8),
              _buildTitle(),
              if (todo.details != null && todo.details!.isNotEmpty) ...[
                const SizedBox(height: 4),
                _buildDetails(),
              ],
              const SizedBox(height: 8),
              _buildMetadata(),
              if (todo.participants.isNotEmpty) ...[
                const SizedBox(height: 8),
                _buildParticipants(),
              ],
              // Note: Tags are now managed through the unified Tag system
              // Tag display will be implemented when TodoTag relations are loaded
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        _buildStatusIcon(),
        const SizedBox(width: 8),
        _buildStatusChip(),
        const Spacer(),
        if (todo.dueDate != null) _buildDueDate(),
      ],
    );
  }

  Widget _buildStatusIcon() {
    IconData iconData;
    Color iconColor;

    switch (todo.status) {
      case TodoStatus.pending:
        iconData = Icons.radio_button_unchecked;
        iconColor = Colors.orange;
        break;
      case TodoStatus.inProgress:
        iconData = Icons.play_circle_outline;
        iconColor = Colors.blue;
        break;
      case TodoStatus.completed:
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
      case TodoStatus.cancelled:
        iconData = Icons.cancel;
        iconColor = Colors.grey;
        break;
    }

    return GestureDetector(
      onTap: () {
        if (onStatusChanged != null && todo.status != TodoStatus.completed) {
          _showStatusMenu();
        }
      },
      child: Icon(
        iconData,
        color: iconColor,
        size: 24,
      ),
    );
  }

  void _showStatusMenu() {
    // This would need context, so we'll implement it differently
    // For now, just toggle between pending and completed
    if (onStatusChanged != null) {
      final newStatus = todo.status == TodoStatus.pending
          ? TodoStatus.completed
          : TodoStatus.pending;
      onStatusChanged!(newStatus);
    }
  }

  Widget _buildStatusChip() {
    Color backgroundColor;
    Color textColor;
    String statusText;

    switch (todo.status) {
      case TodoStatus.pending:
        backgroundColor = Colors.orange.withValues(alpha: 0.1);
        textColor = Colors.orange;
        statusText = 'Pending';
        break;
      case TodoStatus.inProgress:
        backgroundColor = Colors.blue.withValues(alpha: 0.1);
        textColor = Colors.blue;
        statusText = 'In Progress';
        break;
      case TodoStatus.completed:
        backgroundColor = Colors.green.withValues(alpha: 0.1);
        textColor = Colors.green;
        statusText = 'Completed';
        break;
      case TodoStatus.cancelled:
        backgroundColor = Colors.grey.withValues(alpha: 0.1);
        textColor = Colors.grey;
        statusText = 'Cancelled';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildDueDate() {
    final now = DateTime.now();
    final dueDate = todo.dueDate!;
    final isOverdue =
        dueDate.isBefore(now) && todo.status != TodoStatus.completed;
    final isToday = dueDate.year == now.year &&
        dueDate.month == now.month &&
        dueDate.day == now.day;

    Color textColor = AppColors.textSecondary;
    if (isOverdue) {
      textColor = Colors.red;
    } else if (isToday) {
      textColor = Colors.orange;
    }

    // Format date and add time if available
    String displayText = _formatDueDate(dueDate);
    if (todo.dueTime != null && todo.dueTime!.isNotEmpty) {
      displayText += ' ${todo.dueTime}';
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.schedule,
          size: 14,
          color: textColor,
        ),
        const SizedBox(width: 4),
        Text(
          displayText,
          style: TextStyle(
            fontSize: 12,
            color: textColor,
            fontWeight:
                isToday || isOverdue ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  String _formatDueDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    if (dateOnly == today) {
      return 'Today';
    } else if (dateOnly == tomorrow) {
      return 'Tomorrow';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Widget _buildTitle() {
    return Text(
      todo.title,
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: todo.status == TodoStatus.completed
            ? AppColors.textSecondary
            : AppColors.textPrimary,
        decoration: todo.status == TodoStatus.completed
            ? TextDecoration.lineThrough
            : null,
      ),
    );
  }

  Widget _buildDetails() {
    return Text(
      todo.details!,
      style: TextStyle(
        fontSize: 14,
        color: AppColors.textSecondary,
        decoration: todo.status == TodoStatus.completed
            ? TextDecoration.lineThrough
            : null,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildMetadata() {
    return Row(
      children: [
        const Icon(
          Icons.person,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          'Created ${_formatCreatedDate(todo.createdAt)}',
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
        if (todo.location != null) ...[
          const SizedBox(width: 16),
          const Icon(
            Icons.location_on,
            size: 14,
            color: AppColors.textSecondary,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              todo.location!,
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.textSecondary,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ],
    );
  }

  String _formatCreatedDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'today';
    } else if (difference.inDays == 1) {
      return 'yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Widget _buildParticipants() {
    return Row(
      children: [
        const Icon(
          Icons.group,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            '${todo.participants.length} participant${todo.participants.length != 1 ? 's' : ''}',
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
        ),
      ],
    );
  }
}
