import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../spaces/presentation/widgets/space_theme_provider.dart';
import '../../../togo/data/models/togo_model.dart';
import '../../data/models/message_model.dart';

class ToGoMessageWidget extends StatefulWidget {
  final MessageModel message;
  final SpaceThemeProvider spaceTheme;

  const ToGoMessageWidget({
    super.key,
    required this.message,
    required this.spaceTheme,
  });

  @override
  State<ToGoMessageWidget> createState() => _ToGoMessageWidgetState();
}

class _ToGoMessageWidgetState extends State<ToGoMessageWidget> {
  late ToGoData togoData;
  bool _isDeleted = false;

  @override
  void initState() {
    super.initState();
    _parseToGoData();
    // Check if message is deleted
    if (widget.message.isDeleted) {
      _isDeleted = true;
    }
  }

  @override
  void didUpdateWidget(ToGoMessageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.message.metadata != widget.message.metadata) {
      _parseToGoData();
    }
  }

  void _parseToGoData() {
    try {
      final metadata = widget.message.metadata;
      if (metadata != null) {
        togoData = ToGoData(
          id: 'temp-${widget.message.id}', // Temporary ID from message
          spaceId: widget.message.spaceId,
          creatorId: metadata['creatorId'] ?? widget.message.senderId,
          name: metadata['name'] ?? widget.message.content,
          description: metadata['description'],
          address: metadata['address'],
          latitude: metadata['latitude']?.toDouble(),
          longitude: metadata['longitude']?.toDouble(),
          originalLink: metadata['originalLink'],
          tags: const [], // Tags not available in message metadata
          createdAt: widget.message.createdAt,
        );
      } else {
        throw Exception('No metadata');
      }
    } catch (e) {
      // Fallback data if parsing fails
      togoData = ToGoData(
        id: 'temp-${widget.message.id}',
        spaceId: widget.message.spaceId,
        creatorId: widget.message.senderId,
        name: widget.message.content.isNotEmpty
            ? widget.message.content.replaceFirst('Shared a place: ', '')
            : 'Unknown Place',
        tags: const [], // No tags in fallback
        createdAt: widget.message.createdAt,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // If ToGo is deleted, show a simplified deleted state
    if (_isDeleted) {
      return Container(
        constraints: const BoxConstraints(maxWidth: 320),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.place,
                  color: Colors.grey[400],
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  'TOGO',
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Deleted',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              togoData.name,
              style: TextStyle(
                color: Colors.grey[500],
                fontWeight: FontWeight.w600,
                fontSize: 15,
                decoration: TextDecoration.lineThrough,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'This place has been deleted',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[400],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      constraints: const BoxConstraints(maxWidth: 320),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.border,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 8),
          _buildPlaceName(),
          if (togoData.description != null &&
              togoData.description!.isNotEmpty) ...[
            const SizedBox(height: 6),
            _buildDescription(),
          ],
          if (togoData.address != null && togoData.address!.isNotEmpty) ...[
            const SizedBox(height: 6),
            _buildAddress(),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return const Row(
      children: [
        Icon(
          Icons.place,
          color: AppColors.primary,
          size: 16,
        ),
        SizedBox(width: 6),
        Text(
          'ToGo',
          style: TextStyle(
            color: AppColors.primary,
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
        ),
        Spacer(),
      ],
    );
  }

  Widget _buildPlaceName() {
    return Text(
      togoData.name,
      style: const TextStyle(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.w600,
        fontSize: 15,
      ),
    );
  }

  Widget _buildDescription() {
    return Text(
      togoData.description!,
      style: const TextStyle(
        fontSize: 13,
        color: AppColors.textSecondary,
      ),
    );
  }

  Widget _buildAddress() {
    return GestureDetector(
      onTap: () => _openInGoogleMaps(),
      child: Row(
        children: [
          const Icon(
            Icons.location_on,
            size: 14,
            color: AppColors.primary,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              togoData.address!,
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.primary,
              ),
            ),
          ),
          const Icon(
            Icons.open_in_new,
            size: 12,
            color: AppColors.primary,
          ),
        ],
      ),
    );
  }

  Future<void> _openInGoogleMaps() async {
    try {
      // 檢查是否有原始連結
      final originalLink = widget.message.metadata?['originalLink'] as String?;

      String urlToOpen;
      if (originalLink != null && originalLink.isNotEmpty) {
        // 使用原始的 Google Maps 連結
        urlToOpen = originalLink;
        debugPrint('🔗 使用原始連結: $originalLink');
      } else {
        // 降級：使用地址構建搜索 URL
        final encodedAddress = Uri.encodeComponent(togoData.address!);
        urlToOpen =
            'https://www.google.com/maps/search/?api=1&query=$encodedAddress';
        debugPrint('🔍 使用地址搜索: $urlToOpen');
      }

      final uri = Uri.parse(urlToOpen);

      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        debugPrint('❌ 無法打開 Google Maps: $urlToOpen');
      }
    } catch (e) {
      debugPrint('❌ 打開 Google Maps 時發生錯誤: $e');
    }
  }
}
