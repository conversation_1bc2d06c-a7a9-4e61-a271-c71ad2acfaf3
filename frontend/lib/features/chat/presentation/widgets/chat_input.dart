import 'dart:async';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/network/api_client.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../data/models/message_model.dart';
import '../../data/models/poll_model.dart';
import '../../data/models/dice_model.dart';
import '../../data/models/lucky_draw_model.dart';
import '../../data/models/split_model.dart';
import '../../data/models/todo_model.dart';
import '../../data/datasources/todo_api_service.dart';

import 'google_location_picker.dart';
import 'create_poll_dialog.dart';
import 'create_todo_dialog.dart';
import 'create_dice_dialog.dart';
import 'create_lucky_draw_dialog.dart';
import 'create_split_dialog.dart';
import 'create_settle_dialog.dart';
import 'create_event_dialog.dart';
import '../../data/models/event_model.dart';

class ChatInput extends StatefulWidget {
  final Function(String, {String? replyToId}) onSendMessage;
  final Function(String, String,
      {String? replyToId, Map<String, dynamic>? metadata})? onSendFileMessage;
  final VoidCallback onTypingStarted;
  final VoidCallback onTypingStopped;
  final bool isConnected;
  final String spaceId;
  final MessageModel? replyToMessage;
  final VoidCallback? onCancelReply;

  const ChatInput({
    super.key,
    required this.onSendMessage,
    this.onSendFileMessage,
    required this.onTypingStarted,
    required this.onTypingStopped,
    required this.isConnected,
    required this.spaceId,
    this.replyToMessage,
    this.onCancelReply,
  });

  @override
  State<ChatInput> createState() => _ChatInputState();
}

class _ChatInputState extends State<ChatInput> {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  Timer? _typingTimer;
  bool _isTyping = false;
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _textController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    _typingTimer?.cancel();
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _textController.text.trim().isNotEmpty;
    if (_hasText != hasText) {
      setState(() {
        _hasText = hasText;
      });
    }

    if (hasText && !_isTyping) {
      _isTyping = true;
      widget.onTypingStarted();
    }

    // Cancel previous timer
    _typingTimer?.cancel();

    // Stop typing after 1 second of inactivity
    _typingTimer = Timer(const Duration(seconds: 1), () {
      if (_isTyping) {
        _isTyping = false;
        widget.onTypingStopped();
      }
    });
  }

  void _sendMessage() {
    final text = _textController.text.trim();
    if (text.isEmpty || !widget.isConnected) return;

    widget.onSendMessage(text, replyToId: widget.replyToMessage?.id);
    _textController.clear();

    // Stop typing indicator
    if (_isTyping) {
      _isTyping = false;
      widget.onTypingStopped();
    }
    _typingTimer?.cancel();
  }

  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Share Content',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 20),
            // First row of options
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildAttachmentOption(
                  icon: Icons.photo_camera,
                  label: 'Camera',
                  onTap: () {
                    Navigator.pop(context);
                    _handleCameraAttachment();
                  },
                ),
                _buildAttachmentOption(
                  icon: Icons.photo_library,
                  label: 'Gallery',
                  onTap: () {
                    Navigator.pop(context);
                    _handleGalleryAttachment();
                  },
                ),
                _buildAttachmentOption(
                  icon: Icons.insert_drive_file,
                  label: 'Document',
                  onTap: () {
                    Navigator.pop(context);
                    _handleDocumentAttachment();
                  },
                ),
                _buildAttachmentOption(
                  icon: Icons.location_on,
                  label: 'Location',
                  onTap: () {
                    Navigator.pop(context);
                    _handleLocationAttachment();
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Second row with poll, dice, and lucky draw options
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildAttachmentOption(
                  icon: Icons.poll,
                  label: 'Poll',
                  onTap: () {
                    Navigator.pop(context);
                    _handlePollAttachment();
                  },
                ),
                _buildAttachmentOption(
                  icon: Icons.casino,
                  label: 'Dice',
                  onTap: () {
                    Navigator.pop(context);
                    _handleDiceAttachment();
                  },
                ),
                _buildAttachmentOption(
                  icon: Icons.card_giftcard,
                  label: 'Lucky Draw',
                  onTap: () {
                    Navigator.pop(context);
                    _handleLuckyDrawAttachment();
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Third row with split, settle, todo, and togo options
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildAttachmentOption(
                  icon: Icons.receipt_long,
                  label: 'Split Bill',
                  onTap: () {
                    Navigator.pop(context);
                    _handleSplitAttachment();
                  },
                ),
                _buildAttachmentOption(
                  icon: Icons.account_balance_wallet,
                  label: 'Settle Up',
                  onTap: () {
                    Navigator.pop(context);
                    _handleSettleAttachment();
                  },
                ),
                _buildAttachmentOption(
                  icon: Icons.task_alt,
                  label: 'TODO',
                  onTap: () {
                    Navigator.pop(context);
                    _handleTodoAttachment();
                  },
                ),
                _buildAttachmentOption(
                  icon: Icons.event,
                  label: 'Event',
                  onTap: () {
                    Navigator.pop(context);
                    _handleEventAttachment();
                  },
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: 28,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
    );
  }

  void _handleCameraAttachment() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        await _uploadAndSendFile(image);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to take photo: $e')),
        );
      }
    }
  }

  void _handleGalleryAttachment() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        await _uploadAndSendFile(image);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to select image: $e')),
        );
      }
    }
  }

  void _handleDocumentAttachment() async {
    try {
      final FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = result.files.single;
        final xFile = XFile(file.path!);
        await _uploadAndSendFile(xFile);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to select file: $e')),
        );
      }
    }
  }

  void _handleLocationAttachment() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: false, // 禁用拖拽，避免與地圖手勢衝突
      isDismissible: true, // 仍然允許點擊外部關閉
      builder: (context) => GoogleLocationPicker(
        onLocationSelected: (locationData) {
          _sendLocationMessage(locationData);
          Navigator.pop(context); // Close the picker
        },
      ),
    );
  }

  void _handlePollAttachment() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreatePollDialog(
        onCreatePoll: (pollRequest) {
          _sendPollMessage(pollRequest);
        },
      ),
    );
  }

  void _sendPollMessage(CreatePollRequest pollRequest) {
    if (widget.onSendFileMessage != null) {
      // Convert CreatePollRequest to PollData format
      final pollOptions = pollRequest.options.asMap().entries.map((entry) {
        return {
          'id': 'option_${entry.key}',
          'text': entry.value,
          'voterIds': <String>[],
          'voterNames': <String>[],
        };
      }).toList();

      final pollData = {
        'question': pollRequest.question,
        'options': pollOptions,
        'allowMultipleVotes': pollRequest.allowMultipleVotes,
        'isAnonymous': pollRequest.isAnonymous,
        'expiresAt': pollRequest.expiresAt?.toIso8601String(),
        'isClosed': false,
        'creatorId': '', // Will be set by backend
      };

      widget.onSendFileMessage!(
        pollRequest.question, // Use question as content
        'poll', // fileName to indicate it's a poll
        metadata: pollData, // Full poll data in metadata
      );
    }
  }

  void _handleDiceAttachment() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => const CreateDiceDialog(),
    ).then((diceData) {
      if (diceData != null && diceData is DiceData) {
        _sendDiceMessage(diceData);
      }
    });
  }

  void _sendDiceMessage(DiceData diceData) {
    if (widget.onSendFileMessage != null) {
      final content = diceData.question.isNotEmpty
          ? diceData.question
          : 'Dice Roll: ${diceData.options.join(', ')}';

      widget.onSendFileMessage!(
        content, // Use question or options as content
        'dice', // fileName to indicate it's a dice
        metadata: diceData.toJson(), // Full dice data in metadata
      );
    }
  }

  void _handleLuckyDrawAttachment() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => CreateLuckyDrawDialog(
        spaceId: widget.spaceId,
        onCreateLuckyDraw: (luckyDrawRequest) {
          _sendLuckyDrawMessage(luckyDrawRequest);
        },
      ),
    );
  }

  void _sendLuckyDrawMessage(CreateLuckyDrawRequest luckyDrawRequest) {
    if (widget.onSendFileMessage != null) {
      // Convert CreateLuckyDrawRequest to LuckyDrawData format
      final prizes = luckyDrawRequest.prizes.asMap().entries.map((entry) {
        return {
          'id': 'prize_${entry.key}',
          'name': entry.value.name,
          'description': entry.value.description,
          'quantity': entry.value.quantity,
          'remainingQuantity': entry.value.quantity,
        };
      }).toList();

      final luckyDrawData = {
        'title': luckyDrawRequest.title,
        'description': luckyDrawRequest.description,
        'prizes': prizes,
        'participants': <Map<String, dynamic>>[],
        'drawHistory': <Map<String, dynamic>>[],
        'creatorId': '', // Will be set by backend
        'createdAt': DateTime.now().toIso8601String(),
        'expiresAt': luckyDrawRequest.expiresAt?.toIso8601String(),
        'isActive': true,
        'allowMultipleEntries': luckyDrawRequest.allowMultipleEntries,
        'maxParticipants': luckyDrawRequest.maxParticipants,
        'isInviteOnly': luckyDrawRequest.isInviteOnly,
        'invitedUserIds': luckyDrawRequest.invitedUserIds,
        'autoDrawOnExpiry': luckyDrawRequest.autoDrawOnExpiry,
      };

      widget.onSendFileMessage!(
        luckyDrawRequest.title, // Use title as content
        'luckydraw', // fileName to indicate it's a lucky draw
        metadata: luckyDrawData, // Full lucky draw data in metadata
      );
    }
  }

  void _handleSplitAttachment() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreateSplitDialog(
        spaceId: widget.spaceId,
        onCreateSplit: (splitRequest) {
          _sendSplitMessage(splitRequest);
        },
      ),
    );
  }

  void _handleSettleAttachment() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreateSettleDialog(
        spaceId: widget.spaceId,
        onCreateSettle: (settleRequest) {
          _sendSettleMessage(settleRequest);
        },
      ),
    );
  }

  void _sendSplitMessage(CreateSplitRequest splitRequest) {
    if (widget.onSendFileMessage != null) {
      // Convert CreateSplitRequest to SplitData format for metadata
      final splitData = {
        'title': splitRequest.title,
        'description': splitRequest.description,
        'category': splitRequest.category,
        'totalAmount': splitRequest.totalAmount,
        'currency': splitRequest.currency,
        'paidByUserId': splitRequest.paidByUserId,
        'paidByUserName': '', // Will be set by backend
        'splitType': splitRequest.splitType.name,
        'participants':
            <Map<String, dynamic>>[], // Will be populated by backend
        'creatorId': '', // Will be set by backend
        'createdAt': DateTime.now().toIso8601String(),
        'settledAt': null,
        'isSettled': false,
        'participantUserIds': splitRequest.participantUserIds,
        'customAmounts': splitRequest.customAmounts,
      };

      widget.onSendFileMessage!(
        splitRequest.title, // Use title as content
        'split', // fileName to indicate it's a split
        metadata: splitData, // Full split data in metadata
      );
    }
  }

  void _sendSettleMessage(CreateSettleRequest settleRequest) {
    if (widget.onSendFileMessage != null) {
      // Convert CreateSettleRequest to SettleData format for metadata
      final settleData = {
        'fromUserId': settleRequest.fromUserId,
        'fromUserName': settleRequest.fromUserName,
        'toUserId': settleRequest.toUserId,
        'toUserName': settleRequest.toUserName,
        'amount': settleRequest.amount,
        'currency': settleRequest.currency,
        'description': settleRequest.description,
      };

      final content =
          '${settleRequest.currency} ${settleRequest.amount.toStringAsFixed(2)}';

      widget.onSendFileMessage!(
        content, // Use payment description as content
        'settle', // fileName to indicate it's a settle
        metadata: settleData, // Full settle data in metadata
      );
    }
  }

  void _handleTodoAttachment() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreateTodoDialog(
        spaceId: widget.spaceId,
        onCreateTodo: (todoRequest) {
          _sendTodoMessage(todoRequest);
        },
      ),
    );
  }

  void _sendTodoMessage(CreateTodoRequest todoRequest) async {
    // Use the same API service as todo tab to ensure consistency
    try {
      final todoApiService = GetIt.instance<TodoApiService>();
      final response = await todoApiService.createTodo(
        widget.spaceId,
        todoRequest,
      );

      if (response.success && response.data != null) {
        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('TODO created successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating TODO: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _sendLocationMessage(LocationData locationData) {
    // 構建顯示文本
    final displayText = locationData.name?.isNotEmpty == true
        ? locationData.name!
        : locationData.address?.isNotEmpty == true
            ? locationData.address!
            : 'Location: ${locationData.latitude.toStringAsFixed(6)}, ${locationData.longitude.toStringAsFixed(6)}';

    // Send as location type message if callback is available
    if (widget.onSendFileMessage != null) {
      // 構建元數據 - 使用新格式但保持向後兼容
      final metadata = {
        'lat': locationData.latitude,
        'lng': locationData.longitude,
        if (locationData.address?.isNotEmpty == true)
          'addr': locationData.address,
        if (locationData.name?.isNotEmpty == true) 'name': locationData.name,
        'type': locationData.type.toString(),
        // Legacy format for backward compatibility
        'latitude': locationData.latitude,
        'longitude': locationData.longitude,
        if (locationData.address?.isNotEmpty == true)
          'address': locationData.address,
      };

      widget.onSendFileMessage!(
        displayText,
        'location',
        replyToId: widget.replyToMessage?.id,
        metadata: metadata,
      );
    } else {
      // Fallback to regular message
      widget.onSendMessage(
        displayText,
        replyToId: widget.replyToMessage?.id,
      );
    }
  }

  void _handleEventAttachment() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreateEventDialog(
        spaceId: widget.spaceId,
        onCreateEvent: (eventRequest) {
          _sendEventMessage(eventRequest);
        },
      ),
    );
  }

  void _sendEventMessage(CreateEventRequest eventRequest) async {
    if (widget.onSendFileMessage != null) {
      // Create the message content and metadata
      final messageContent = eventRequest.title;
      final metadata = eventRequest.toJson();

      // Send the message using the file message mechanism with metadata
      widget.onSendFileMessage!(
        messageContent, // Use title as content
        'event', // fileName to indicate it's an event
        metadata: metadata, // Full event data in metadata
      );

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Event created successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else {
      // Fallback to regular message if file message not supported
      widget.onSendMessage(eventRequest.title);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Event created (basic mode)'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  Future<void> _uploadAndSendFile(XFile file) async {
    if (widget.onSendFileMessage == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('File sharing not supported')),
      );
      return;
    }

    try {
      // Show uploading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 16),
              Text('Uploading file...'),
            ],
          ),
          duration: Duration(seconds: 30),
        ),
      );

      // Get API client
      final apiClient = DependencyInjection.getIt<ApiClient>();

      // Create multipart file
      final multipartFile = await MultipartFile.fromFile(
        file.path,
        filename: file.name,
      );

      // Upload file
      final response = await apiClient.uploadMedia(multipartFile);

      // Hide uploading indicator
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      }

      if (response.success && response.data != null) {
        // Send file message
        widget.onSendFileMessage!(
          response.data!.url,
          response.data!.filename,
          replyToId: widget.replyToMessage?.id,
        );
      } else {
        throw Exception('Upload failed');
      }
    } catch (e) {
      // Hide uploading indicator
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to upload file: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Reply Indicator
            if (widget.replyToMessage != null) _buildReplyIndicator(),

            // Input Row
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Attachment Button
                  IconButton(
                    onPressed:
                        widget.isConnected ? _showAttachmentOptions : null,
                    icon: const Icon(Icons.attach_file),
                    color: widget.isConnected ? AppColors.primary : Colors.grey,
                  ),

                  // Text Input
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: TextField(
                        controller: _textController,
                        focusNode: _focusNode,
                        enabled: widget.isConnected,
                        decoration: InputDecoration(
                          hintText: widget.replyToMessage != null
                              ? 'Reply to ${widget.replyToMessage!.senderName}...'
                              : widget.isConnected
                                  ? 'Type a message...'
                                  : 'Waiting for connection...',
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        maxLines: null,
                        textCapitalization: TextCapitalization.sentences,
                        onSubmitted: (_) => _sendMessage(),
                      ),
                    ),
                  ),

                  const SizedBox(width: 8),

                  // Send Button
                  Container(
                    decoration: BoxDecoration(
                      color: widget.isConnected && _hasText
                          ? AppColors.primary
                          : Colors.grey[300],
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed:
                          widget.isConnected && _hasText ? _sendMessage : null,
                      icon: const Icon(Icons.send),
                      color: widget.isConnected && _hasText
                          ? Colors.white
                          : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReplyIndicator() {
    final replyMessage = widget.replyToMessage!;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.reply,
            size: 16,
            color: AppColors.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Replying to ${replyMessage.senderName}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  replyMessage.content,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: widget.onCancelReply,
            icon: const Icon(Icons.close),
            iconSize: 16,
            color: Colors.grey,
            constraints: const BoxConstraints(
              minWidth: 32,
              minHeight: 32,
            ),
          ),
        ],
      ),
    );
  }
}
