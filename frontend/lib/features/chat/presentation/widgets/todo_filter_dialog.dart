import 'package:flutter/material.dart';
import '../../data/models/todo_model.dart';

class TodoFilterOptions {
  final List<String>? tags;
  final List<TodoStatus>? statuses;
  final String? creatorId;
  final DateTime? dueBefore;
  final DateTime? dueAfter;
  final bool? hasLocation;
  final bool? hasDueDate;

  TodoFilterOptions({
    this.tags,
    this.statuses,
    this.creatorId,
    this.dueBefore,
    this.dueAfter,
    this.hasLocation,
    this.hasDueDate,
  });

  TodoFilterOptions copyWith({
    List<String>? tags,
    List<TodoStatus>? statuses,
    String? creatorId,
    DateTime? dueBefore,
    DateTime? dueAfter,
    bool? hasLocation,
    bool? hasDueDate,
  }) {
    return TodoFilterOptions(
      tags: tags ?? this.tags,
      statuses: statuses ?? this.statuses,
      creatorId: creatorId ?? this.creatorId,
      dueBefore: dueBefore ?? this.dueBefore,
      dueAfter: dueAfter ?? this.dueAfter,
      hasLocation: hasLocation ?? this.hasLocation,
      hasDueDate: hasDueDate ?? this.hasDueDate,
    );
  }

  bool get hasActiveFilters {
    return (tags?.isNotEmpty ?? false) ||
        (statuses?.isNotEmpty ?? false) ||
        creatorId != null ||
        dueBefore != null ||
        dueAfter != null ||
        hasLocation != null ||
        hasDueDate != null;
  }
}

class TodoFilterDialog extends StatefulWidget {
  final TodoFilterOptions? initialFilters;
  final List<String> availableTags;
  final Function(TodoFilterOptions?) onFiltersChanged;

  const TodoFilterDialog({
    super.key,
    this.initialFilters,
    required this.availableTags,
    required this.onFiltersChanged,
  });

  @override
  State<TodoFilterDialog> createState() => _TodoFilterDialogState();
}

class _TodoFilterDialogState extends State<TodoFilterDialog> {
  late List<String> _selectedTags;
  late List<TodoStatus> _selectedStatuses;
  DateTime? _dueBefore;
  DateTime? _dueAfter;
  bool? _hasLocation;
  bool? _hasDueDate;

  @override
  void initState() {
    super.initState();
    _selectedTags = widget.initialFilters?.tags ?? [];
    _selectedStatuses = widget.initialFilters?.statuses ?? [];
    _dueBefore = widget.initialFilters?.dueBefore;
    _dueAfter = widget.initialFilters?.dueAfter;
    _hasLocation = widget.initialFilters?.hasLocation;
    _hasDueDate = widget.initialFilters?.hasDueDate;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              // Drag handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                child: Row(
                  children: [
                    const Icon(Icons.filter_list, color: Colors.blue),
                    const SizedBox(width: 8),
                    const Text(
                      'Filter TODOs',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      iconSize: 20,
                    ),
                  ],
                ),
              ),
              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Tags Filter
                      if (widget.availableTags.isNotEmpty) ...[
                        const Text(
                          'Tags',
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          runSpacing: 4,
                          children: widget.availableTags.map((tag) {
                            final isSelected = _selectedTags.contains(tag);
                            return FilterChip(
                              label: Text(tag),
                              selected: isSelected,
                              onSelected: (selected) {
                                setState(() {
                                  if (selected) {
                                    _selectedTags.add(tag);
                                  } else {
                                    _selectedTags.remove(tag);
                                  }
                                });
                              },
                            );
                          }).toList(),
                        ),
                        const SizedBox(height: 16),
                      ],

                      // Status Filter
                      const Text(
                        'Status',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 4,
                        children: TodoStatus.values.map((status) {
                          final isSelected = _selectedStatuses.contains(status);
                          return FilterChip(
                            label: Text(_getStatusDisplayName(status)),
                            selected: isSelected,
                            onSelected: (selected) {
                              setState(() {
                                if (selected) {
                                  _selectedStatuses.add(status);
                                } else {
                                  _selectedStatuses.remove(status);
                                }
                              });
                            },
                          );
                        }).toList(),
                      ),
                      const SizedBox(height: 16),

                      // Due Date Filter
                      const Text(
                        'Due Date',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () async {
                                final date = await showDatePicker(
                                  context: context,
                                  initialDate: _dueAfter ?? DateTime.now(),
                                  firstDate: DateTime(2020),
                                  lastDate: DateTime(2030),
                                );
                                if (date != null) {
                                  setState(() {
                                    _dueAfter = date;
                                  });
                                }
                              },
                              child: Text(
                                _dueAfter != null
                                    ? 'After: ${_dueAfter!.day}/${_dueAfter!.month}/${_dueAfter!.year}'
                                    : 'Due After',
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () async {
                                final date = await showDatePicker(
                                  context: context,
                                  initialDate: _dueBefore ?? DateTime.now(),
                                  firstDate: DateTime(2020),
                                  lastDate: DateTime(2030),
                                );
                                if (date != null) {
                                  setState(() {
                                    _dueBefore = date;
                                  });
                                }
                              },
                              child: Text(
                                _dueBefore != null
                                    ? 'Before: ${_dueBefore!.day}/${_dueBefore!.month}/${_dueBefore!.year}'
                                    : 'Due Before',
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Additional Filters
                      const Text(
                        'Additional Filters',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 8),
                      CheckboxListTile(
                        title: const Text('Has Location'),
                        value: _hasLocation,
                        tristate: true,
                        onChanged: (value) {
                          setState(() {
                            _hasLocation = value;
                          });
                        },
                        contentPadding: EdgeInsets.zero,
                      ),
                      CheckboxListTile(
                        title: const Text('Has Due Date'),
                        value: _hasDueDate,
                        tristate: true,
                        onChanged: (value) {
                          setState(() {
                            _hasDueDate = value;
                          });
                        },
                        contentPadding: EdgeInsets.zero,
                      ),
                    ],
                  ),
                ),
              ),
              // Actions
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  border: Border(
                    top: BorderSide(color: Colors.grey[200]!),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () {
                              // Clear all filters
                              setState(() {
                                _selectedTags.clear();
                                _selectedStatuses.clear();
                                _dueBefore = null;
                                _dueAfter = null;
                                _hasLocation = null;
                                _hasDueDate = null;
                              });
                              widget.onFiltersChanged(null);
                              Navigator.pop(context);
                            },
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(color: Colors.grey[300]!),
                              ),
                            ),
                            child: const Text('Clear All'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: TextButton(
                            onPressed: () => Navigator.pop(context),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(color: Colors.grey[300]!),
                              ),
                            ),
                            child: const Text('Cancel'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          final filters = TodoFilterOptions(
                            tags: _selectedTags.isEmpty ? null : _selectedTags,
                            statuses: _selectedStatuses.isEmpty
                                ? null
                                : _selectedStatuses,
                            dueBefore: _dueBefore,
                            dueAfter: _dueAfter,
                            hasLocation: _hasLocation,
                            hasDueDate: _hasDueDate,
                          );
                          widget.onFiltersChanged(
                              filters.hasActiveFilters ? filters : null);
                          Navigator.pop(context);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text('Apply'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  String _getStatusDisplayName(TodoStatus status) {
    switch (status) {
      case TodoStatus.pending:
        return 'Pending';
      case TodoStatus.inProgress:
        return 'In Progress';
      case TodoStatus.completed:
        return 'Completed';
      case TodoStatus.cancelled:
        return 'Cancelled';
    }
  }
}
