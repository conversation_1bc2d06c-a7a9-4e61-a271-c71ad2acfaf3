import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../../../core/config/google_api_config.dart';

/// 位置消息的地圖縮圖組件
/// 顯示一個小型的 Google Maps 縮圖，用於聊天中的位置消息
class LocationMapThumbnail extends StatefulWidget {
  final double latitude;
  final double longitude;
  final String? locationName;
  final bool isMe;
  final VoidCallback? onTap;
  final double height;
  final double? width;

  const LocationMapThumbnail({
    super.key,
    required this.latitude,
    required this.longitude,
    this.locationName,
    required this.isMe,
    this.onTap,
    this.height = 120,
    this.width,
  });

  @override
  State<LocationMapThumbnail> createState() => _LocationMapThumbnailState();
}

class _LocationMapThumbnailState extends State<LocationMapThumbnail> {
  GoogleMapController? _mapController;
  bool _isMapReady = false;

  @override
  Widget build(BuildContext context) {
    // 如果 Google API 未配置，顯示佔位符
    if (!GoogleApiConfig.isConfigured) {
      return _buildPlaceholder();
    }

    return Container(
      height: widget.height,
      width: widget.width ?? double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: widget.isMe
              ? Colors.white.withValues(alpha: 0.3)
              : Colors.grey[300]!,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Stack(
          children: [
            // Google Maps 縮圖
            GoogleMap(
              initialCameraPosition: CameraPosition(
                target: LatLng(widget.latitude, widget.longitude),
                zoom: 15.0,
              ),
              onMapCreated: (GoogleMapController controller) {
                _mapController = controller;
                setState(() => _isMapReady = true);
              },
              markers: {
                Marker(
                  markerId: const MarkerId('location'),
                  position: LatLng(widget.latitude, widget.longitude),
                  infoWindow: InfoWindow(
                    title: widget.locationName ?? 'Location',
                  ),
                ),
              },
              // 禁用所有交互，只作為縮圖顯示
              zoomControlsEnabled: false,
              zoomGesturesEnabled: false,
              scrollGesturesEnabled: false,
              rotateGesturesEnabled: false,
              tiltGesturesEnabled: false,
              compassEnabled: false,
              mapToolbarEnabled: false,
              myLocationEnabled: false,
              myLocationButtonEnabled: false,
              liteModeEnabled: true, // 啟用精簡模式以提高性能
              // 優化設置以減少 API 調用
              buildingsEnabled: false,
              trafficEnabled: false,
              indoorViewEnabled: false,
            ),

            // 點擊覆蓋層
            if (widget.onTap != null)
              Positioned.fill(
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: widget.onTap,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ),

            // 加載指示器
            if (!_isMapReady)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: widget.isMe
                        ? Colors.white.withValues(alpha: 0.1)
                        : Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              widget.isMe
                                  ? Colors.white.withValues(alpha: 0.8)
                                  : Colors.grey[600]!,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Loading map...',
                          style: TextStyle(
                            fontSize: 10,
                            color: widget.isMe
                                ? Colors.white.withValues(alpha: 0.8)
                                : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

            // 右下角的地圖圖標指示器
            if (_isMapReady)
              Positioned(
                bottom: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.6),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Icon(
                    Icons.map,
                    size: 12,
                    color: Colors.white,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      height: widget.height,
      width: widget.width ?? double.infinity,
      decoration: BoxDecoration(
        color: widget.isMe
            ? Colors.white.withValues(alpha: 0.1)
            : Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: widget.isMe
              ? Colors.white.withValues(alpha: 0.3)
              : Colors.grey[300]!,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: widget.onTap,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.map,
                size: 32,
                color: widget.isMe
                    ? Colors.white.withValues(alpha: 0.8)
                    : Colors.grey[600],
              ),
              const SizedBox(height: 4),
              Text(
                'Tap to open in Maps',
                style: TextStyle(
                  fontSize: 12,
                  color: widget.isMe
                      ? Colors.white.withValues(alpha: 0.8)
                      : Colors.grey[600],
                ),
              ),
              const SizedBox(height: 2),
              Text(
                '${widget.latitude.toStringAsFixed(6)}, ${widget.longitude.toStringAsFixed(6)}',
                style: TextStyle(
                  fontSize: 10,
                  color: widget.isMe
                      ? Colors.white.withValues(alpha: 0.6)
                      : Colors.grey[500],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
