import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/tag_selector.dart';
import '../../data/models/event_model.dart';
import '../../data/models/linkable_item_model.dart';
import 'linkable_item_selector.dart';

class CreateEventDialog extends StatefulWidget {
  final String spaceId;
  final Function(CreateEventRequest) onCreateEvent;

  const CreateEventDialog({
    super.key,
    required this.spaceId,
    required this.onCreateEvent,
  });

  @override
  State<CreateEventDialog> createState() => _CreateEventDialogState();
}

class _CreateEventDialogState extends State<CreateEventDialog> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();

  List<DateTime> _selectedDates = [];
  TimeOfDay? _selectedTime;
  TimeOfDay? _selectedEndTime;
  EventTimeMode _timeMode = EventTimeMode.specificTime;
  TimeOfDayPeriod? _selectedTimeOfDay;
  LinkableItem? _selectedLinkableItem;
  List<String> _selectedTags = [];
  final bool _isLoading = false;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  void _onLinkableItemSelected(LinkableItem? item) {
    setState(() {
      _selectedLinkableItem = item;

      if (item != null) {
        // Auto-fill title with icon prefix (matching app bar icons)
        String titlePrefix = item.type == LinkableItemType.todo ? '☑ ' : '📍 ';
        _titleController.text = titlePrefix + item.title;

        // Auto-fill location if available
        if (item.location != null && item.location!.isNotEmpty) {
          _locationController.text = item.location!;
        }
      }
    });
  }

  void _clearSelectedItem() {
    setState(() {
      _selectedLinkableItem = null;
      _titleController.clear();
      _locationController.clear();
    });
  }

  void _createEvent() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate title - always required
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content:
                Text('Please enter an event title or select a Todo/ToGo item')),
      );
      return;
    }

    // Note: Date selection is now optional - events can be created with just time

    // Validate time range if time range mode is selected
    if (_timeMode == EventTimeMode.timeRange) {
      if (_selectedTime == null || _selectedEndTime == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text(
                  'Please select both start and end times for time range')),
        );
        return;
      }
    }

    // Validate time of day if time of day mode is selected
    if (_timeMode == EventTimeMode.timeOfDay) {
      if (_selectedTimeOfDay == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select a time of day')),
        );
        return;
      }
    }

    // Format time based on selected mode
    String? eventTimeString;
    switch (_timeMode) {
      case EventTimeMode.specificTime:
        if (_selectedTime != null) {
          eventTimeString =
              '${_selectedTime!.hour.toString().padLeft(2, '0')}:${_selectedTime!.minute.toString().padLeft(2, '0')}';
        }
        break;
      case EventTimeMode.timeRange:
        if (_selectedTime != null && _selectedEndTime != null) {
          final startTime =
              '${_selectedTime!.hour.toString().padLeft(2, '0')}:${_selectedTime!.minute.toString().padLeft(2, '0')}';
          final endTime =
              '${_selectedEndTime!.hour.toString().padLeft(2, '0')}:${_selectedEndTime!.minute.toString().padLeft(2, '0')}';
          eventTimeString = '$startTime-$endTime';
        }
        break;
      case EventTimeMode.fullDay:
        eventTimeString = 'full-day';
        break;
      case EventTimeMode.timeOfDay:
        if (_selectedTimeOfDay != null) {
          eventTimeString = _selectedTimeOfDay!.name;
        }
        break;
    }

    final eventRequest = CreateEventRequest(
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      eventDates: _selectedDates,
      eventTime: eventTimeString,
      location: _locationController.text.trim().isEmpty
          ? null
          : _locationController.text.trim(),
      linkedTodoId: _selectedLinkableItem?.type == LinkableItemType.todo
          ? _selectedLinkableItem?.id
          : null,
      linkedToGoId: _selectedLinkableItem?.type == LinkableItemType.togo
          ? _selectedLinkableItem?.id
          : null,
      tags: _selectedTags.isNotEmpty ? _selectedTags : null,
    );

    widget.onCreateEvent(eventRequest);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.8,
          minChildSize: 0.5,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                _buildDragHandle(),
                _buildHeader(),
                Expanded(
                  child: _buildContent(scrollController),
                ),
                _buildActions(),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildDragHandle() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey, width: 0.2),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.event,
            color: AppColors.primary,
            size: 24,
          ),
          const SizedBox(width: 12),
          const Text(
            'Create Event',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(ScrollController scrollController) {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          controller: scrollController,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),
              _buildTitleSelection(),
              const SizedBox(height: 16),
              _buildDescriptionInput(),
              const SizedBox(height: 16),
              _buildDateTimeSelection(),
              const SizedBox(height: 16),
              _buildLocationInput(),
              const SizedBox(height: 16),
              _buildTagSelection(),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitleSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Event Title *',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),

        // Title input field - always visible
        _buildTitleInput(),
        const SizedBox(height: 16),

        // Todo/ToGo selector - always visible for optional selection
        _buildTodoToGoSelector(),
      ],
    );
  }

  Widget _buildTitleInput() {
    return TextFormField(
      controller: _titleController,
      decoration: InputDecoration(
        hintText: 'Enter event title or select from Todo/ToGo below',
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        // Add clear button when Todo/ToGo item is selected
        suffixIcon: _selectedLinkableItem != null
            ? IconButton(
                icon: const Icon(Icons.clear, size: 20),
                onPressed: _clearSelectedItem,
                tooltip: 'Clear selection',
              )
            : null,
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter an event title or select a Todo/ToGo item';
        }
        return null;
      },
      maxLength: 100,
      buildCounter: (context,
          {required currentLength, required isFocused, maxLength}) {
        return Text(
          '$currentLength/$maxLength',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        );
      },
    );
  }

  Widget _buildTodoToGoSelector() {
    return LinkableItemSelector(
      spaceId: widget.spaceId,
      selectedItem: _selectedLinkableItem,
      onItemSelected: _onLinkableItemSelected,
    );
  }

  Widget _buildDescriptionInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Description',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            hintText: 'Enter event description (optional)',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          maxLines: 3,
          maxLength: 500,
          buildCounter: (context,
              {required currentLength, required isFocused, maxLength}) {
            return Text(
              '$currentLength/$maxLength',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildDateTimeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Suggested Date section
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Suggested Date (Optional)',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
            if (_selectedDates.isNotEmpty)
              TextButton.icon(
                onPressed: _clearDates,
                icon: const Icon(Icons.clear, size: 16),
                label: const Text('Clear'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        _buildDateSelection(),

        const SizedBox(height: 16),

        // Suggested Time section
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Suggested Time (Optional)',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
            if (_hasTimeSelection())
              TextButton.icon(
                onPressed: _clearTime,
                icon: const Icon(Icons.clear, size: 16),
                label: const Text('Clear'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        _buildTimeModeSelector(),
        const SizedBox(height: 8),
        _buildTimeInputs(),
      ],
    );
  }

  Widget _buildLocationInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Location',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _locationController,
          decoration: const InputDecoration(
            hintText: 'Enter event location (optional)',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            prefixIcon: Icon(Icons.location_on),
          ),
          maxLength: 200,
          buildCounter: (context,
              {required currentLength, required isFocused, maxLength}) {
            return Text(
              '$currentLength/$maxLength',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildTagSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        TagSelector(
          spaceId: widget.spaceId,
          selectedTags: _selectedTags,
          onTagsChanged: (tags) {
            setState(() {
              _selectedTags = tags;
            });
          },
          hintText: 'Add tags for this event...',
          allowCustomTags: true,
          maxTags: 10,
        ),
      ],
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey, width: 0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: _createEvent,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Create Event'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        OutlinedButton.icon(
          onPressed: _selectDates,
          icon: const Icon(Icons.calendar_today, size: 18),
          label: Text(_getDateSelectionText()),
        ),
        if (_selectedDates.isNotEmpty) ...[
          const SizedBox(height: 8),
          _buildSelectedDatesDisplay(),
        ],
      ],
    );
  }

  String _getDateSelectionText() {
    if (_selectedDates.isEmpty) {
      return 'Select Dates';
    } else if (_selectedDates.length == 1) {
      final date = _selectedDates.first;
      return '${date.day}/${date.month}/${date.year}';
    } else {
      return '${_selectedDates.length} dates selected';
    }
  }

  Widget _buildSelectedDatesDisplay() {
    // Sort dates for display
    final sortedDates = List<DateTime>.from(_selectedDates)..sort();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Selected Dates:',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 4),
        Wrap(
          spacing: 6,
          runSpacing: 4,
          children: _buildDateRangeChips(sortedDates),
        ),
      ],
    );
  }

  List<Widget> _buildDateRangeChips(List<DateTime> sortedDates) {
    if (sortedDates.isEmpty) return [];

    final dateRanges = _groupConsecutiveDates(sortedDates);
    final chips = <Widget>[];

    for (final range in dateRanges) {
      if (range.length == 1) {
        // Single date
        chips.add(_buildDateChip(range.first, isRange: false));
      } else {
        // Date range
        chips.add(_buildDateRangeChip(range.first, range.last, range));
      }
    }

    return chips;
  }

  List<List<DateTime>> _groupConsecutiveDates(List<DateTime> sortedDates) {
    if (sortedDates.isEmpty) return [];

    final groups = <List<DateTime>>[];
    List<DateTime> currentGroup = [sortedDates.first];

    for (int i = 1; i < sortedDates.length; i++) {
      final currentDate = sortedDates[i];
      final previousDate = sortedDates[i - 1];

      // Check if dates are consecutive (difference of 1 day)
      final daysDifference = currentDate.difference(previousDate).inDays;

      if (daysDifference == 1) {
        // Consecutive date, add to current group
        currentGroup.add(currentDate);
      } else {
        // Non-consecutive date, start new group
        groups.add(currentGroup);
        currentGroup = [currentDate];
      }
    }

    // Add the last group
    groups.add(currentGroup);

    return groups;
  }

  Widget _buildDateChip(DateTime date, {bool isRange = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.shade100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade300),
      ),
      child: Text(
        '${date.day}/${date.month}',
        style: const TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.w500,
          color: Colors.blue,
        ),
      ),
    );
  }

  Widget _buildDateRangeChip(
      DateTime startDate, DateTime endDate, List<DateTime> rangeDates) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.green.shade100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.shade300),
      ),
      child: Text(
        '${startDate.day}/${startDate.month}-${endDate.day}/${endDate.month}',
        style: const TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.w500,
          color: Colors.green,
        ),
      ),
    );
  }

  Widget _buildTimeModeSelector() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildTimeModeButton(
                mode: EventTimeMode.specificTime,
                label: 'Specific Time',
                icon: Icons.schedule,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTimeModeButton(
                mode: EventTimeMode.timeRange,
                label: 'Time Range',
                icon: Icons.schedule_outlined,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildTimeModeButton(
                mode: EventTimeMode.timeOfDay,
                label: 'Time of Day',
                icon: Icons.wb_sunny_outlined,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTimeModeButton(
                mode: EventTimeMode.fullDay,
                label: 'Full Day',
                icon: Icons.all_inclusive,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTimeModeButton({
    required EventTimeMode mode,
    required String label,
    required IconData icon,
  }) {
    final isSelected = _timeMode == mode;
    return GestureDetector(
      onTap: () {
        setState(() {
          _timeMode = mode;
          // Clear time selections when mode changes
          if (mode == EventTimeMode.fullDay) {
            _selectedTime = null;
            _selectedEndTime = null;
            _selectedTimeOfDay = null;
          } else if (mode == EventTimeMode.specificTime) {
            _selectedEndTime = null;
            _selectedTimeOfDay = null;
          } else if (mode == EventTimeMode.timeRange) {
            _selectedTimeOfDay = null;
          } else if (mode == EventTimeMode.timeOfDay) {
            _selectedTime = null;
            _selectedEndTime = null;
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey.shade300,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : Colors.grey.shade700,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey.shade700,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                fontSize: 11,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeInputs() {
    if (_timeMode == EventTimeMode.fullDay) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.blue.shade50,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: Colors.blue.shade200),
        ),
        child: const Row(
          children: [
            Icon(Icons.all_inclusive, color: Colors.blue, size: 16),
            SizedBox(width: 8),
            Text(
              'This is a full day event',
              style: TextStyle(
                color: Colors.blue,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ],
        ),
      );
    }

    if (_timeMode == EventTimeMode.timeOfDay) {
      return _buildTimeOfDaySelector();
    }

    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _selectStartTime,
            icon: const Icon(Icons.access_time, size: 18),
            label: Text(
              _selectedTime != null
                  ? '${_selectedTime!.hour.toString().padLeft(2, '0')}:${_selectedTime!.minute.toString().padLeft(2, '0')}'
                  : _timeMode == EventTimeMode.timeRange
                      ? 'Start Time'
                      : 'Select Time',
            ),
          ),
        ),
        if (_timeMode == EventTimeMode.timeRange) ...[
          const SizedBox(width: 8),
          const Text('-',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
          const SizedBox(width: 8),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _selectedTime != null ? _selectEndTime : null,
              icon: const Icon(Icons.access_time_filled, size: 18),
              label: Text(
                _selectedEndTime != null
                    ? '${_selectedEndTime!.hour.toString().padLeft(2, '0')}:${_selectedEndTime!.minute.toString().padLeft(2, '0')}'
                    : 'End Time',
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTimeOfDaySelector() {
    return Row(
      children: [
        Expanded(
          child: _buildTimeOfDayButton(
            period: TimeOfDayPeriod.morning,
            label: 'Morning',
            icon: Icons.wb_sunny,
          ),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: _buildTimeOfDayButton(
            period: TimeOfDayPeriod.afternoon,
            label: 'Afternoon',
            icon: Icons.wb_sunny_outlined,
          ),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: _buildTimeOfDayButton(
            period: TimeOfDayPeriod.evening,
            label: 'Evening',
            icon: Icons.nights_stay_outlined,
          ),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: _buildTimeOfDayButton(
            period: TimeOfDayPeriod.midnight,
            label: 'Midnight',
            icon: Icons.bedtime,
          ),
        ),
      ],
    );
  }

  Widget _buildTimeOfDayButton({
    required TimeOfDayPeriod period,
    required String label,
    required IconData icon,
  }) {
    final isSelected = _selectedTimeOfDay == period;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTimeOfDay = period;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primary.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? AppColors.primary : Colors.grey.shade600,
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? AppColors.primary : Colors.grey.shade600,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDates() async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _MultiDatePickerBottomSheet(
        initialDates: _selectedDates,
        onDatesSelected: (dates) {
          setState(() {
            _selectedDates = dates;
          });
        },
      ),
    );
  }

  void _clearDates() {
    setState(() {
      _selectedDates.clear();
    });
  }

  Future<void> _selectStartTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _selectedTime ?? TimeOfDay.now(),
    );
    if (time != null) {
      setState(() {
        _selectedTime = time;
        // Clear end time if it's before start time
        if (_selectedEndTime != null && _timeMode == EventTimeMode.timeRange) {
          final startMinutes = time.hour * 60 + time.minute;
          final endMinutes =
              _selectedEndTime!.hour * 60 + _selectedEndTime!.minute;
          if (endMinutes <= startMinutes) {
            _selectedEndTime = null;
          }
        }
      });
    }
  }

  Future<void> _selectEndTime() async {
    if (_selectedTime == null) return;

    final startMinutes = _selectedTime!.hour * 60 + _selectedTime!.minute;
    final initialEndTime = _selectedEndTime ??
        TimeOfDay(
          hour: (_selectedTime!.hour + 1) % 24,
          minute: _selectedTime!.minute,
        );

    final time = await showTimePicker(
      context: context,
      initialTime: initialEndTime,
    );

    if (time != null) {
      final endMinutes = time.hour * 60 + time.minute;
      if (endMinutes > startMinutes) {
        setState(() => _selectedEndTime = time);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('End time must be after start time'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  bool _hasTimeSelection() {
    return _selectedTime != null ||
        _selectedEndTime != null ||
        _selectedTimeOfDay != null ||
        _timeMode == EventTimeMode.fullDay;
  }

  void _clearTime() {
    setState(() {
      _selectedTime = null;
      _selectedEndTime = null;
      _selectedTimeOfDay = null;
      _timeMode = EventTimeMode.specificTime; // Reset to default mode
    });
  }
}

class _MultiDatePickerBottomSheet extends StatefulWidget {
  final List<DateTime> initialDates;
  final Function(List<DateTime>) onDatesSelected;

  const _MultiDatePickerBottomSheet({
    required this.initialDates,
    required this.onDatesSelected,
  });

  @override
  State<_MultiDatePickerBottomSheet> createState() =>
      _MultiDatePickerBottomSheetState();
}

class _MultiDatePickerBottomSheetState
    extends State<_MultiDatePickerBottomSheet> {
  late List<DateTime> _selectedDates;
  late List<DateTime> _originalSelectedDates; // 拖拽開始前的原始選擇
  DateTime _focusedDay = DateTime.now();
  DateTime? _rangeStart;
  DateTime? _rangeEnd;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _selectedDates = List.from(widget.initialDates);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.85,
        minChildSize: 0.6,
        maxChildSize: 0.95,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              _buildDragHandle(),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      const Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Select Dates',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Calendar
                      _buildCalendar(),

                      const SizedBox(height: 16),

                      // Selected dates display
                      if (_selectedDates.isNotEmpty) _buildSelectedDatesInfo(),

                      const SizedBox(height: 16),

                      // Action buttons
                      Row(
                        children: [
                          if (_selectedDates.isNotEmpty)
                            TextButton(
                              onPressed: () {
                                setState(() {
                                  _selectedDates.clear();
                                  _rangeStart = null;
                                  _rangeEnd = null;
                                });
                              },
                              child: const Text('Clear All'),
                            ),
                          const Spacer(),
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('Cancel'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: () {
                              widget.onDatesSelected(_selectedDates);
                              Navigator.pop(context);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primary,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Confirm'),
                          ),
                        ],
                      ),
                      SizedBox(height: MediaQuery.of(context).padding.bottom),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDragHandle() {
    return Container(
      width: 40,
      height: 4,
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildCalendar() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: _buildSimpleCalendar(),
    );
  }

  Widget _buildSimpleCalendar() {
    return Column(
      children: [
        _buildCalendarHeader(),
        _buildCalendarGrid(),
      ],
    );
  }

  Widget _buildCalendarHeader() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () {
              setState(() {
                _focusedDay = DateTime(_focusedDay.year, _focusedDay.month - 1);
              });
            },
            icon: const Icon(Icons.chevron_left),
          ),
          Text(
            '${_getMonthName(_focusedDay.month)} ${_focusedDay.year}',
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _focusedDay = DateTime(_focusedDay.year, _focusedDay.month + 1);
              });
            },
            icon: const Icon(Icons.chevron_right),
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarGrid() {
    final firstDayOfMonth = DateTime(_focusedDay.year, _focusedDay.month, 1);
    final lastDayOfMonth = DateTime(_focusedDay.year, _focusedDay.month + 1, 0);
    final firstDayWeekday = firstDayOfMonth.weekday % 7;

    final days = <Widget>[];

    // Week day headers
    const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    for (final day in weekDays) {
      days.add(
        Container(
          height: 32,
          alignment: Alignment.center,
          child: Text(
            day,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade600,
              fontSize: 12,
            ),
          ),
        ),
      );
    }

    // Empty cells for days before month starts
    for (int i = 0; i < firstDayWeekday; i++) {
      days.add(const SizedBox(height: 32));
    }

    // Days of the month
    for (int day = 1; day <= lastDayOfMonth.day; day++) {
      final date = DateTime(_focusedDay.year, _focusedDay.month, day);
      final isSelected = _selectedDates.any((d) => _isSameDay(d, date));
      final isToday = _isSameDay(date, DateTime.now());
      final isPast =
          date.isBefore(DateTime.now().subtract(const Duration(days: 1)));

      days.add(
        _buildDayCell(date, day, isSelected, isToday, isPast),
      );
    }

    return Container(
      padding: const EdgeInsets.all(8),
      child: GridView.count(
        shrinkWrap: true,
        crossAxisCount: 7,
        children: days,
      ),
    );
  }

  Widget _buildDayCell(
      DateTime date, int day, bool isSelected, bool isToday, bool isPast) {
    return Draggable<DateTime>(
      data: date,
      feedback: const SizedBox.shrink(), // 隱藏拖拉反饋
      childWhenDragging: null, // 拖拉時保持原始外觀
      onDragStarted: isPast ? null : () => _onDragStarted(date),
      onDragEnd: isPast ? null : (details) => _onDragEnd(),
      child: DragTarget<DateTime>(
        onWillAcceptWithDetails: (details) => !isPast && details.data != date,
        onAcceptWithDetails: (details) => _onDragAccept(details.data, date),
        onMove: (details) => _onDragMove(details.data, date),
        builder: (context, candidateData, rejectedData) {
          final isHighlighted = candidateData.isNotEmpty;
          final isInDragRange = _isDragging && _isDateInDragRange(date);
          return GestureDetector(
            onTap: isPast ? null : () => _onDayTapped(date),
            child: Container(
              height: 32,
              margin: const EdgeInsets.all(1),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.primary
                    : isInDragRange
                        ? AppColors.primary.withValues(alpha: 0.7)
                        : isHighlighted
                            ? AppColors.primary.withValues(alpha: 0.3)
                            : isToday
                                ? Colors.blue.shade100
                                : null,
                borderRadius: BorderRadius.circular(4),
                border: isToday && !isSelected && !isInDragRange
                    ? Border.all(color: Colors.blue)
                    : isHighlighted
                        ? Border.all(color: AppColors.primary, width: 2)
                        : null,
              ),
              alignment: Alignment.center,
              child: Text(
                day.toString(),
                style: TextStyle(
                  color: isPast
                      ? Colors.grey.shade400
                      : isSelected || isInDragRange
                          ? Colors.white
                          : isHighlighted
                              ? AppColors.primary
                              : isToday
                                  ? Colors.blue
                                  : Colors.black,
                  fontWeight:
                      isSelected || isToday || isHighlighted || isInDragRange
                          ? FontWeight.w600
                          : FontWeight.normal,
                  fontSize: 12,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSelectedDatesInfo() {
    final sortedDates = List<DateTime>.from(_selectedDates)..sort();
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected: ${_selectedDates.length} date${_selectedDates.length == 1 ? '' : 's'}',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 12,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            _formatSelectedDates(sortedDates),
            style: const TextStyle(
              fontSize: 11,
              color: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  void _onDayTapped(DateTime date) {
    if (_isDragging) return; // Ignore tap during drag

    setState(() {
      if (_selectedDates.any((d) => _isSameDay(d, date))) {
        _selectedDates.removeWhere((d) => _isSameDay(d, date));
      } else {
        _selectedDates.add(date);
      }
    });
  }

  void _onDragStarted(DateTime date) {
    setState(() {
      _isDragging = true;
      _rangeStart = date;
      _rangeEnd = null;

      // 保存拖拽開始前的原始選擇
      _originalSelectedDates = List.from(_selectedDates);
      // 開始拖拉時先選中該日期（如果還沒選中）
      if (!_selectedDates.any((d) => _isSameDay(d, date))) {
        _selectedDates.add(date);
      }
    });
  }

  void _onDragMove(DateTime startDate, DateTime currentDate) {
    if (!_isDragging || _rangeStart == null) return;

    setState(() {
      _rangeEnd = currentDate;
      // 基於原始選擇添加拖拽範圍
      final dragRange =
          startDate.isBefore(currentDate) || _isSameDay(startDate, currentDate)
              ? _getDatesInRange(startDate, currentDate)
              : _getDatesInRange(currentDate, startDate);

      // 合併原始選擇和拖拽範圍
      _selectedDates = _mergeSelectedDates(_originalSelectedDates, dragRange);
    });
  }

  void _onDragAccept(DateTime startDate, DateTime endDate) {
    setState(() {
      // 確認選擇從開始日期到結束日期的範圍
      final dragRange =
          startDate.isBefore(endDate) || _isSameDay(startDate, endDate)
              ? _getDatesInRange(startDate, endDate)
              : _getDatesInRange(endDate, startDate);

      // 合併原始選擇和拖拽範圍
      _selectedDates = _mergeSelectedDates(_originalSelectedDates, dragRange);
    });
  }

  void _onDragEnd() {
    setState(() {
      _isDragging = false;
      _rangeStart = null;
      _rangeEnd = null;
    });
  }

  bool _isDateInDragRange(DateTime date) {
    if (!_isDragging || _rangeStart == null) return false;
    if (_rangeEnd == null) return _isSameDay(date, _rangeStart!);

    final start = _rangeStart!.isBefore(_rangeEnd!) ? _rangeStart! : _rangeEnd!;
    final end = _rangeStart!.isAfter(_rangeEnd!) ? _rangeStart! : _rangeEnd!;

    return (date.isAfter(start) || _isSameDay(date, start)) &&
        (date.isBefore(end) || _isSameDay(date, end));
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  List<DateTime> _getDatesInRange(DateTime start, DateTime end) {
    final dates = <DateTime>[];
    var current = start;
    while (current.isBefore(end) || _isSameDay(current, end)) {
      dates.add(current);
      current = current.add(const Duration(days: 1));
    }
    return dates;
  }

  List<DateTime> _mergeSelectedDates(
      List<DateTime> originalDates, List<DateTime> newDates) {
    final mergedSet = <DateTime>{};

    // 添加原始選擇的日期
    for (final date in originalDates) {
      mergedSet.add(DateTime(date.year, date.month, date.day));
    }

    // 添加新的拖拽範圍日期
    for (final date in newDates) {
      mergedSet.add(DateTime(date.year, date.month, date.day));
    }

    // 轉換為列表並排序
    final mergedList = mergedSet.toList()..sort();
    return mergedList;
  }

  String _getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months[month - 1];
  }

  String _formatSelectedDates(List<DateTime> dates) {
    if (dates.isEmpty) return '';

    final dateRanges = _groupConsecutiveDatesInDialog(dates);
    final formattedRanges = <String>[];

    for (final range in dateRanges) {
      if (range.length == 1) {
        // Single date
        formattedRanges.add('${range.first.day}/${range.first.month}');
      } else if (range.length == 2) {
        // Two consecutive dates
        formattedRanges.add(
            '${range.first.day}/${range.first.month}, ${range.last.day}/${range.last.month}');
      } else {
        // Range of 3 or more dates
        formattedRanges.add(
            '${range.first.day}/${range.first.month}-${range.last.day}/${range.last.month}');
      }
    }

    return formattedRanges.join(', ');
  }

  List<List<DateTime>> _groupConsecutiveDatesInDialog(
      List<DateTime> sortedDates) {
    if (sortedDates.isEmpty) return [];

    final groups = <List<DateTime>>[];
    List<DateTime> currentGroup = [sortedDates.first];

    for (int i = 1; i < sortedDates.length; i++) {
      final currentDate = sortedDates[i];
      final previousDate = sortedDates[i - 1];

      // Check if dates are consecutive (difference of 1 day)
      final daysDifference = currentDate.difference(previousDate).inDays;

      if (daysDifference == 1) {
        // Consecutive date, add to current group
        currentGroup.add(currentDate);
      } else {
        // Non-consecutive date, start new group
        groups.add(currentGroup);
        currentGroup = [currentDate];
      }
    }

    // Add the last group
    groups.add(currentGroup);

    return groups;
  }
}
