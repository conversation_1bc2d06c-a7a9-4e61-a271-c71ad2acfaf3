import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../../core/widgets/tag_selector.dart';
import '../../../spaces/domain/usecases/get_space_members_usecase.dart';
import '../../../spaces/data/models/space_member_model.dart';
import '../../data/models/todo_model.dart';
import 'google_location_picker.dart';

class CreateTodoDialog extends StatefulWidget {
  final String spaceId;
  final Function(CreateTodoRequest) onCreateTodo;

  const CreateTodoDialog({
    super.key,
    required this.spaceId,
    required this.onCreateTodo,
  });

  @override
  State<CreateTodoDialog> createState() => _CreateTodoDialogState();
}

class _CreateTodoDialogState extends State<CreateTodoDialog> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _detailsController = TextEditingController();
  final TextEditingController _customTagController = TextEditingController();

  List<SpaceMemberModel> _spaceMembers = [];
  final List<String> _selectedParticipants = [];
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  String? _selectedLocation;
  List<String> _selectedTags = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSpaceMembers();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _detailsController.dispose();
    _customTagController.dispose();
    super.dispose();
  }

  Future<void> _loadSpaceMembers() async {
    try {
      setState(() => _isLoading = true);
      final getSpaceMembersUseCase =
          DependencyInjection.getIt<GetSpaceMembersUseCase>();
      final members = await getSpaceMembersUseCase.execute(widget.spaceId);
      setState(() {
        _spaceMembers = members;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load members: $e')),
        );
      }
    }
  }

  void _createTodo() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Format time as HH:mm string if selected
    String? dueTimeString;
    if (_selectedTime != null) {
      dueTimeString =
          '${_selectedTime!.hour.toString().padLeft(2, '0')}:${_selectedTime!.minute.toString().padLeft(2, '0')}';
    }

    final todoRequest = CreateTodoRequest(
      title: _titleController.text.trim(),
      details: _detailsController.text.trim().isEmpty
          ? null
          : _detailsController.text.trim(),
      participantUserIds: _selectedParticipants,
      dueDate: _selectedDate,
      dueTime: dueTimeString,
      location: _selectedLocation,
      tags: _selectedTags,
    );

    widget.onCreateTodo(todoRequest);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.9,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                _buildDragHandle(),
                _buildHeader(),
                Expanded(
                  child: _buildContent(scrollController),
                ),
                _buildActions(),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildDragHandle() {
    return Container(
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 12, 12, 8),
      child: Row(
        children: [
          const Icon(
            Icons.checklist,
            color: AppColors.primary,
            size: 22,
          ),
          const SizedBox(width: 8),
          Text(
            'Create TODO',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close, size: 20),
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(ScrollController scrollController) {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          controller: scrollController,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTitleInput(),
              const SizedBox(height: 12),
              _buildDetailsInput(),
              const SizedBox(height: 12),
              _buildParticipantsSelection(),
              const SizedBox(height: 12),
              _buildDateTimeSelection(),
              const SizedBox(height: 12),
              _buildLocationSelection(),
              const SizedBox(height: 12),
              _buildTagsSelection(),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitleInput() {
    return TextFormField(
      controller: _titleController,
      decoration: const InputDecoration(
        labelText: 'Title *',
        hintText: 'Enter TODO title',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter a title';
        }
        return null;
      },
      maxLength: 100,
    );
  }

  Widget _buildDetailsInput() {
    return TextFormField(
      controller: _detailsController,
      decoration: const InputDecoration(
        labelText: 'Details',
        hintText: 'Enter TODO details (optional)',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      maxLines: 3,
      maxLength: 500,
    );
  }

  Widget _buildParticipantsSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Participants',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: _spaceMembers.map((member) {
              final isSelected = _selectedParticipants.contains(member.userId);
              return CheckboxListTile(
                title: Text(member.user.displayName),
                value: isSelected,
                onChanged: (bool? value) {
                  setState(() {
                    if (value == true) {
                      _selectedParticipants.add(member.userId);
                    } else {
                      _selectedParticipants.remove(member.userId);
                    }
                  });
                },
                dense: true,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildDateTimeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Due Date & Time',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _selectDate,
                icon: const Icon(Icons.calendar_today, size: 18),
                label: Text(
                  _selectedDate != null
                      ? '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}'
                      : 'Select Date',
                ),
              ),
            ),
            if (_selectedDate != null) ...[
              const SizedBox(width: 4),
              IconButton(
                onPressed: _clearDate,
                icon: const Icon(Icons.clear, size: 18),
                tooltip: 'Clear Date',
                padding: const EdgeInsets.all(4),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),
            ],
            const SizedBox(width: 8),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _selectedDate != null ? _selectTime : null,
                icon: const Icon(Icons.access_time, size: 18),
                label: Text(
                  _selectedTime != null
                      ? '${_selectedTime!.hour.toString().padLeft(2, '0')}:${_selectedTime!.minute.toString().padLeft(2, '0')}'
                      : 'Select Time',
                ),
              ),
            ),
            if (_selectedTime != null) ...[
              const SizedBox(width: 4),
              IconButton(
                onPressed: _clearTime,
                icon: const Icon(Icons.clear, size: 18),
                tooltip: 'Clear Time',
                padding: const EdgeInsets.all(4),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildLocationSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Location',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _selectLocation,
                icon: const Icon(Icons.location_on, size: 18),
                label: Text(
                  _selectedLocation ?? 'Select Location',
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            if (_selectedLocation != null) ...[
              const SizedBox(width: 8),
              IconButton(
                onPressed: _clearLocation,
                icon: const Icon(Icons.clear, size: 18),
                tooltip: 'Clear Location',
                padding: const EdgeInsets.all(4),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildTagsSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        TagSelector(
          spaceId: widget.spaceId,
          selectedTags: _selectedTags,
          onTagsChanged: (tags) {
            setState(() {
              _selectedTags = tags;
            });
          },
          hintText: 'Add tags for this TODO...',
          allowCustomTags: true,
          maxTags: 10,
        ),
      ],
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[200]!, width: 1),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Cancel'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: _createTodo,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Create TODO'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  Future<void> _selectTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _selectedTime ?? TimeOfDay.now(),
    );
    if (time != null) {
      setState(() => _selectedTime = time);
    }
  }

  void _clearDate() {
    setState(() {
      _selectedDate = null;
      _selectedTime = null; // Clear time when date is cleared
    });
  }

  void _clearTime() {
    setState(() {
      _selectedTime = null;
    });
  }

  void _clearLocation() {
    setState(() {
      _selectedLocation = null;
    });
  }

  Future<void> _selectLocation() async {
    final result = await showModalBottomSheet<Map<String, dynamic>>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: false, // Disable drag to avoid conflicts with map gestures
      isDismissible: true, // Still allow closing by tapping outside
      builder: (context) => GoogleLocationPicker(
        onLocationSelected: (locationData) {
          Navigator.pop(context, locationData.toJson());
        },
      ),
    );

    if (result != null) {
      final address = result['address'] as String?;
      if (address != null && address.isNotEmpty) {
        setState(() => _selectedLocation = address);
      }
    }
  }
}
