import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../../core/animations/micro_interactions.dart';
import '../../../spaces/presentation/widgets/space_theme_provider.dart';
import '../bloc/chat_bloc.dart';
import '../bloc/chat_event.dart';
import '../bloc/chat_state.dart';
import '../../data/models/message_model.dart';
import 'message_bubble.dart';
import 'chat_input.dart';
import 'typing_indicator.dart';

class ChatWidget extends StatefulWidget {
  final String spaceId;

  const ChatWidget({
    super.key,
    required this.spaceId,
  });

  @override
  State<ChatWidget> createState() => _ChatWidgetState();
}

class _ChatWidgetState extends State<ChatWidget> {
  late ScrollController _scrollController;
  late ChatBloc _chatBloc;
  MessageModel? _replyToMessage;
  bool _showScrollToBottomButton = false;
  bool _isLoadingMore = false;
  bool _hasInitiallyScrolled = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _chatBloc = DependencyInjection.getIt<ChatBloc>();

    // Initialize chat for this space
    _chatBloc.add(ChatInitialized(widget.spaceId));

    // Setup scroll listener for pagination
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    // Don't dispose the ChatBloc since it's now a singleton
    // Just leave the current space
    if (_chatBloc.state is ChatLoaded) {
      _chatBloc.add(const ChatDisposed());
    }
    super.dispose();
  }

  void _onScroll() {
    // Load more messages when scrolling near the top (for older messages)
    if (_scrollController.position.pixels < 200 && !_isLoadingMore) {
      _isLoadingMore = true;
      _chatBloc.add(const ChatLoadMoreRequested());
      // Reset loading flag after a delay
      Future.delayed(const Duration(seconds: 2), () {
        _isLoadingMore = false;
      });
    }

    // Show/hide scroll to bottom button with throttling
    // Show button when not at the bottom (newest messages are at maxScrollExtent)
    final shouldShow = _scrollController.position.pixels <
        _scrollController.position.maxScrollExtent - 500;
    if (shouldShow != _showScrollToBottomButton) {
      // Throttle setState calls to reduce UI updates
      Future.microtask(() {
        if (mounted && shouldShow != _showScrollToBottomButton) {
          setState(() {
            _showScrollToBottomButton = shouldShow;
          });
        }
      });
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      // Scroll to bottom (newest messages are at the end)
      final maxExtent = _scrollController.position.maxScrollExtent;
      _scrollController
          .animateTo(
        maxExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      )
          .then((_) {
        // Double-check after animation completes
        Future.delayed(const Duration(milliseconds: 50), () {
          if (_scrollController.hasClients) {
            final newMaxExtent = _scrollController.position.maxScrollExtent;
            final currentPosition = _scrollController.position.pixels;

            // If we're not at the bottom (with tolerance), jump to the true bottom
            if (currentPosition < newMaxExtent - 5) {
              _scrollController.jumpTo(newMaxExtent);
            }
          }
        });
      });
    }
  }

  void _scrollToBottomImmediate() {
    if (!_scrollController.hasClients) return;

    // Use a more efficient approach for immediate scrolling
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        final maxExtent = _scrollController.position.maxScrollExtent;
        _scrollController.jumpTo(maxExtent);

        // Single micro-task check to ensure we're at the bottom
        // This handles cases where the list size changes after the first jump
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_scrollController.hasClients) {
            final newMaxExtent = _scrollController.position.maxScrollExtent;
            if (newMaxExtent > maxExtent) {
              _scrollController.jumpTo(newMaxExtent);
            }
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final spaceTheme = context.spaceTheme;

    // If no SpaceThemeProvider is found, show error
    if (spaceTheme == null) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              SizedBox(height: 16),
              Text(
                'SpaceThemeProvider not found',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'ChatWidget must be wrapped with SpaceThemeProvider',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return BlocProvider.value(
      value: _chatBloc,
      child: Column(
        children: [
          // Messages Area
          Expanded(
            child: Container(
              color: Colors.grey[50],
              child: BlocConsumer<ChatBloc, ChatState>(
                listener: (context, state) {
                  if (state is ChatLoaded && state.error != null) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(state.error!),
                        backgroundColor: Colors.red,
                        action: SnackBarAction(
                          label: 'Dismiss',
                          textColor: Colors.white,
                          onPressed: () {
                            _chatBloc.add(const ChatErrorCleared());
                          },
                        ),
                      ),
                    );
                  }

                  // Auto-scroll to bottom only on initial load or new messages
                  if (state is ChatLoaded) {
                    // Only scroll on initial load or when receiving new messages
                    if (!_hasInitiallyScrolled && state.messages.isNotEmpty) {
                      _hasInitiallyScrolled = true;
                      // Use immediate post-frame callback for instant scrolling
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        _scrollToBottomImmediate();
                      });
                    }
                  }
                },
                builder: (context, state) {
                  if (state is ChatLoading) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  if (state is ChatError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Failed to load chat',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  color: Colors.grey[600],
                                ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            state.message,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: Colors.grey[500],
                                ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              _chatBloc.add(ChatInitialized(widget.spaceId));
                            },
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }

                  if (state is ChatLoaded) {
                    return _buildChatContent(context, state, spaceTheme);
                  }

                  return const SizedBox.shrink();
                },
              ),
            ),
          ),

          // Input Area
          BlocBuilder<ChatBloc, ChatState>(
            builder: (context, state) {
              final isConnected =
                  state is ChatLoaded ? state.isConnected : false;

              return ChatInput(
                spaceId: widget.spaceId,
                onSendMessage: (content, {String? replyToId}) {
                  _chatBloc.add(ChatMessageSent(
                    content: content,
                    replyToId: replyToId ?? _replyToMessage?.id,
                  ));
                  // Clear reply after sending
                  if (_replyToMessage != null) {
                    setState(() {
                      _replyToMessage = null;
                    });
                  }
                  // Scroll to bottom after sending (immediate)
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (_scrollController.hasClients) {
                      _scrollToBottom();
                    }
                  });
                },
                onSendFileMessage: (fileUrl, fileName,
                    {String? replyToId, Map<String, dynamic>? metadata}) {
                  MessageType messageType;
                  Map<String, dynamic> messageMetadata;

                  // Handle location messages
                  if (fileName == 'location') {
                    messageType = MessageType.location;
                    // Use provided metadata or create default
                    messageMetadata = metadata ??
                        {
                          'type': 'location',
                          'description': fileUrl,
                        };
                  } else if (fileName == 'poll') {
                    // Handle poll messages
                    messageType = MessageType.poll;
                    messageMetadata = metadata ?? {};
                  } else if (fileName == 'dice') {
                    // Handle dice messages
                    messageType = MessageType.dice;
                    messageMetadata = metadata ?? {};
                  } else if (fileName == 'luckydraw') {
                    // Handle lucky draw messages
                    messageType = MessageType.luckyDraw;
                    messageMetadata = metadata ?? {};
                  } else if (fileName == 'split') {
                    // Handle split messages
                    messageType = MessageType.split;
                    messageMetadata = metadata ?? {};
                  } else if (fileName == 'settle') {
                    // Handle settle messages
                    messageType = MessageType.settle;
                    messageMetadata = metadata ?? {};
                  } else if (fileName == 'todo') {
                    // Handle todo messages
                    messageType = MessageType.todo;
                    messageMetadata = metadata ?? {};
                  } else if (fileName == 'togo') {
                    // Handle togo messages
                    messageType = MessageType.togo;
                    messageMetadata = metadata ?? {};
                  } else if (fileName == 'event') {
                    // Handle event messages
                    messageType = MessageType.event;
                    messageMetadata = metadata ?? {};
                  } else {
                    // Determine message type based on file extension
                    final isImage = fileName.toLowerCase().endsWith('.jpg') ||
                        fileName.toLowerCase().endsWith('.jpeg') ||
                        fileName.toLowerCase().endsWith('.png') ||
                        fileName.toLowerCase().endsWith('.webp');

                    messageType =
                        isImage ? MessageType.image : MessageType.file;
                    messageMetadata = {
                      'fileName': fileName,
                      'fileUrl': fileUrl,
                    };
                  }

                  _chatBloc.add(ChatMessageSent(
                    content: fileUrl,
                    type: messageType,
                    metadata: messageMetadata,
                    replyToId: replyToId ?? _replyToMessage?.id,
                  ));
                  // Clear reply after sending
                  if (_replyToMessage != null) {
                    setState(() {
                      _replyToMessage = null;
                    });
                  }
                  // Scroll to bottom after sending (immediate)
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (_scrollController.hasClients) {
                      _scrollToBottom();
                    }
                  });
                },
                onTypingStarted: () {
                  _chatBloc.add(const ChatTypingStarted());
                },
                onTypingStopped: () {
                  _chatBloc.add(const ChatTypingStopped());
                },
                isConnected: isConnected,
                replyToMessage: _replyToMessage,
                onCancelReply: () {
                  setState(() {
                    _replyToMessage = null;
                  });
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildChatContent(
      BuildContext context, ChatLoaded state, SpaceThemeProvider spaceTheme) {
    if (state.messages.isEmpty) {
      return _buildEmptyState(context, spaceTheme);
    }

    return Stack(
      children: [
        RefreshIndicator(
          onRefresh: () async {
            _chatBloc.add(const ChatMessagesLoadRequested(isRefresh: true));
          },
          child: Column(
            children: [
              // Connection Status - Only show when disconnected
              if (!state.isConnected)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  color: Colors.orange[100],
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 12,
                        height: 12,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.orange[700]!),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Connecting...',
                        style: TextStyle(
                          color: Colors.orange[700],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

              // Messages List
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  // No reverse - messages are now in chronological order (oldest first)
                  padding: const EdgeInsets.symmetric(
                      horizontal: 8, vertical: 4), // Reduced padding
                  itemCount:
                      state.messages.length + (state.isLoadingMore ? 1 : 0),
                  // Performance optimizations
                  cacheExtent: 500, // Reduce cache to prevent memory issues
                  addAutomaticKeepAlives: false, // Don't keep all items alive
                  addRepaintBoundaries: true, // Improve repaint performance
                  physics:
                      const ClampingScrollPhysics(), // Reduce overscroll effects
                  itemBuilder: (context, index) {
                    if (index == 0 && state.isLoadingMore) {
                      // Loading more indicator at top for older messages
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }

                    final messageIndex =
                        state.isLoadingMore ? index - 1 : index;
                    final message = state.messages[messageIndex];
                    return RepaintBoundary(
                      child: ButtonPressAnimation(
                        pressScale: 0.98,
                        onTap:
                            null, // No click callback needed, just animation effect
                        child: MessageBubble(
                          key: ValueKey(message.id),
                          message: message,
                          spaceTheme: spaceTheme,
                          onEdit: () =>
                              _showEditMessageDialog(context, message),
                          onDelete: () =>
                              _showDeleteMessageDialog(context, message),
                          onReply: () => _setReplyToMessage(message),
                        ),
                      ),
                    );
                  },
                ),
              ),

              // Typing Indicator
              if (state.typingUsers.isNotEmpty)
                TypingIndicator(
                  typingUsers: state.typingUsers.values.toList(),
                  spaceTheme: spaceTheme,
                ),
            ],
          ),
        ),

        // Scroll to bottom button
        if (_showScrollToBottomButton)
          Positioned(
            bottom: 16,
            right: 16,
            child: AnimatedOpacity(
              opacity: _showScrollToBottomButton ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 200),
              child: FloatingActionButton.small(
                onPressed: _scrollToBottom,
                backgroundColor: spaceTheme.primaryColor,
                child: const Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.white,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context, SpaceThemeProvider spaceTheme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: const BoxDecoration(
              color: AppColors.surfaceVariant,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.chat_bubble_outline,
              size: 40,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Start the conversation',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Send a message to begin chatting with your space members',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showEditMessageDialog(BuildContext context, MessageModel message) {
    final TextEditingController controller =
        TextEditingController(text: message.content);

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Drag handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                child: Row(
                  children: [
                    const Icon(Icons.edit, color: Colors.blue),
                    const SizedBox(width: 8),
                    const Text(
                      'Edit Message',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      iconSize: 20,
                    ),
                  ],
                ),
              ),
              // Content
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    TextField(
                      controller: controller,
                      maxLines: null,
                      decoration: const InputDecoration(
                        hintText: 'Enter your message...',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 24),
                    // Actions
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () => Navigator.pop(context),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(color: Colors.grey[300]!),
                              ),
                            ),
                            child: const Text('Cancel'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              if (controller.text.trim().isNotEmpty) {
                                _chatBloc.add(ChatMessageUpdated(
                                  messageId: message.id,
                                  content: controller.text.trim(),
                                ));
                                Navigator.pop(context);
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text('Save'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteMessageDialog(BuildContext context, MessageModel message) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.delete, color: Colors.red),
                  const SizedBox(width: 8),
                  const Text(
                    'Delete Message',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  const Text(
                    'Are you sure you want to delete this message? This action cannot be undone.',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  // Actions
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.pop(context),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            _chatBloc.add(ChatMessageDeleted(message.id));
                            Navigator.pop(context);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Delete'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _setReplyToMessage(MessageModel message) {
    setState(() {
      _replyToMessage = message;
    });
  }
}
