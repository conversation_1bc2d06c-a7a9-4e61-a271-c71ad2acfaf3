import 'package:flutter/material.dart';

class CalendarPickerWidget extends StatefulWidget {
  final List<DateTime> selectedDates;
  final Function(List<DateTime>) onDatesChanged;

  const CalendarPickerWidget({
    super.key,
    required this.selectedDates,
    required this.onDatesChanged,
  });

  @override
  State<CalendarPickerWidget> createState() => _CalendarPickerWidgetState();
}

class _CalendarPickerWidgetState extends State<CalendarPickerWidget> {
  late List<DateTime> _selectedDates;
  late List<DateTime> _originalSelectedDates;
  DateTime _focusedDay = DateTime.now();
  DateTime? _rangeStart;
  // DateTime? _rangeEnd;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _selectedDates = List.from(widget.selectedDates);
  }

  @override
  void didUpdateWidget(CalendarPickerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedDates != widget.selectedDates) {
      _selectedDates = List.from(widget.selectedDates);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Month navigation
        _buildMonthNavigation(),
        const SizedBox(height: 16),

        // Instructions
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: const Row(
            children: [
              Icon(Icons.info_outline, size: 16, color: Colors.blue),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Tap to select single dates, long press + drag to select date ranges',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // Calendar
        Expanded(child: _buildCalendar()),
      ],
    );
  }

  Widget _buildMonthNavigation() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          onPressed: () {
            setState(() {
              _focusedDay = DateTime(_focusedDay.year, _focusedDay.month - 1);
            });
          },
          icon: const Icon(Icons.chevron_left),
        ),
        Text(
          _getMonthYearText(_focusedDay),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        IconButton(
          onPressed: () {
            setState(() {
              _focusedDay = DateTime(_focusedDay.year, _focusedDay.month + 1);
            });
          },
          icon: const Icon(Icons.chevron_right),
        ),
      ],
    );
  }

  Widget _buildCalendar() {
    return GestureDetector(
      onPanEnd: (details) {
        // Swipe navigation
        if (details.velocity.pixelsPerSecond.dx > 300) {
          // Swipe right - previous month
          setState(() {
            _focusedDay = DateTime(_focusedDay.year, _focusedDay.month - 1);
          });
        } else if (details.velocity.pixelsPerSecond.dx < -300) {
          // Swipe left - next month
          setState(() {
            _focusedDay = DateTime(_focusedDay.year, _focusedDay.month + 1);
          });
        }
      },
      child: _buildCalendarGrid(),
    );
  }

  Widget _buildCalendarGrid() {
    final firstDayOfMonth = DateTime(_focusedDay.year, _focusedDay.month, 1);
    final lastDayOfMonth = DateTime(_focusedDay.year, _focusedDay.month + 1, 0);
    final startOfWeek =
        firstDayOfMonth.subtract(Duration(days: firstDayOfMonth.weekday - 1));

    List<Widget> days = [];

    // Week day headers
    const weekDays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    for (String day in weekDays) {
      days.add(
        Container(
          height: 40,
          alignment: Alignment.center,
          child: Text(
            day,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
        ),
      );
    }

    // Empty cells for days before the first day of the month
    for (int i = 0; i < firstDayOfMonth.weekday - 1; i++) {
      final date = startOfWeek.add(Duration(days: i));
      days.add(_buildDayCell(date, date.day, false, false, true,
          isOtherMonth: true));
    }

    // Days of the month
    for (int day = 1; day <= lastDayOfMonth.day; day++) {
      final date = DateTime(_focusedDay.year, _focusedDay.month, day);
      final isSelected = _selectedDates.any((d) => _isSameDay(d, date));
      final isToday = _isSameDay(date, DateTime.now());
      final isPast =
          date.isBefore(DateTime.now().subtract(const Duration(days: 1)));

      days.add(_buildDayCell(date, day, isSelected, isToday, isPast));
    }

    // Fill remaining cells
    final totalCells = ((days.length - 7) / 7).ceil() * 7 + 7;
    while (days.length < totalCells) {
      final date = lastDayOfMonth
          .add(Duration(days: days.length - 7 - lastDayOfMonth.day + 1));
      days.add(_buildDayCell(date, date.day, false, false, true,
          isOtherMonth: true));
    }

    return Container(
      padding: const EdgeInsets.all(8),
      child: GridView.count(
        shrinkWrap: true,
        crossAxisCount: 7,
        children: days,
      ),
    );
  }

  Widget _buildDayCell(
      DateTime date, int day, bool isSelected, bool isToday, bool isPast,
      {bool isOtherMonth = false}) {
    return GestureDetector(
      onTap: isOtherMonth || isPast ? null : () => _onDayTapped(date),
      onLongPressStart:
          isOtherMonth || isPast ? null : (_) => _onDragStarted(date),
      child: DragTarget<DateTime>(
        onMove: (details) {
          if (_isDragging && !isOtherMonth && !isPast) {
            _onDragMove(date);
          }
        },
        onAcceptWithDetails: (details) {
          if (_isDragging) {
            _onDragEnded(date);
          }
        },
        builder: (context, candidateData, rejectedData) {
          return Draggable<DateTime>(
            data: date,
            feedback: const SizedBox.shrink(),
            childWhenDragging: null,
            child: Container(
              margin: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: isSelected
                    ? Colors.blue
                    : isToday
                        ? Colors.blue.withValues(alpha: 0.3)
                        : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: isToday && !isSelected
                    ? Border.all(color: Colors.blue, width: 2)
                    : null,
              ),
              child: Center(
                child: Text(
                  '$day',
                  style: TextStyle(
                    color: isOtherMonth
                        ? Colors.grey[400]
                        : isPast
                            ? Colors.grey[500]
                            : isSelected
                                ? Colors.white
                                : isToday
                                    ? Colors.blue
                                    : Colors.black87,
                    fontWeight: isSelected || isToday
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _onDayTapped(DateTime date) {
    if (_isDragging) return;

    setState(() {
      if (_selectedDates.any((d) => _isSameDay(d, date))) {
        _selectedDates.removeWhere((d) => _isSameDay(d, date));
      } else {
        _selectedDates.add(date);
      }
    });
    widget.onDatesChanged(_selectedDates);
  }

  void _onDragStarted(DateTime date) {
    setState(() {
      _isDragging = true;
      _rangeStart = date;
      // _rangeEnd = null;
      _originalSelectedDates = List.from(_selectedDates);

      if (!_selectedDates.any((d) => _isSameDay(d, date))) {
        _selectedDates.add(date);
      }
    });
  }

  void _onDragMove(DateTime date) {
    if (!_isDragging || _rangeStart == null) return;

    setState(() {
      // _rangeEnd = date;
      _selectedDates = List.from(_originalSelectedDates);

      final start = _rangeStart!;
      final end = date;
      final startDate = start.isBefore(end) ? start : end;
      final endDate = start.isBefore(end) ? end : start;

      for (DateTime d = startDate;
          d.isBefore(endDate.add(const Duration(days: 1)));
          d = d.add(const Duration(days: 1))) {
        if (!_selectedDates.any((selected) => _isSameDay(selected, d))) {
          _selectedDates.add(d);
        }
      }
    });
  }

  void _onDragEnded(DateTime date) {
    setState(() {
      _isDragging = false;
      _rangeStart = null;
      // _rangeEnd = null;
    });
    widget.onDatesChanged(_selectedDates);
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  String _getMonthYearText(DateTime date) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }
}
