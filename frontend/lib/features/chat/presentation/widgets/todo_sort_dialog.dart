import 'package:flutter/material.dart';

enum TodoSortBy {
  createdAt,
  dueDate,
  title,
  status,
  priority,
}

enum TodoSortOrder {
  asc,
  desc,
}

class TodoSortOptions {
  final TodoSortBy sortBy;
  final TodoSortOrder sortOrder;

  TodoSortOptions({
    required this.sortBy,
    required this.sortOrder,
  });

  TodoSortOptions copyWith({
    TodoSortBy? sortBy,
    TodoSortOrder? sortOrder,
  }) {
    return TodoSortOptions(
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }

  String get sortByString {
    switch (sortBy) {
      case TodoSortBy.createdAt:
        return 'createdAt';
      case TodoSortBy.dueDate:
        return 'dueDate';
      case TodoSortBy.title:
        return 'title';
      case TodoSortBy.status:
        return 'status';
      case TodoSortBy.priority:
        return 'priority';
    }
  }

  String get sortOrderString {
    switch (sortOrder) {
      case TodoSortOrder.asc:
        return 'asc';
      case TodoSortOrder.desc:
        return 'desc';
    }
  }
}

class TodoSortDialog extends StatefulWidget {
  final TodoSortOptions? initialSort;
  final Function(TodoSortOptions) onSortChanged;

  const TodoSortDialog({
    super.key,
    this.initialSort,
    required this.onSortChanged,
  });

  @override
  State<TodoSortDialog> createState() => _TodoSortDialogState();
}

class _TodoSortDialogState extends State<TodoSortDialog> {
  late TodoSortBy _sortBy;
  late TodoSortOrder _sortOrder;

  @override
  void initState() {
    super.initState();
    _sortBy = widget.initialSort?.sortBy ?? TodoSortBy.createdAt;
    _sortOrder = widget.initialSort?.sortOrder ?? TodoSortOrder.desc;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.8,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              // Drag handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                child: Row(
                  children: [
                    const Icon(Icons.sort, color: Colors.blue),
                    const SizedBox(width: 8),
                    const Text(
                      'Sort TODOs',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      iconSize: 20,
                    ),
                  ],
                ),
              ),
              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Sort By',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 8),
                      ...TodoSortBy.values.map((sortBy) {
                        return RadioListTile<TodoSortBy>(
                          title: Text(_getSortByDisplayName(sortBy)),
                          value: sortBy,
                          groupValue: _sortBy,
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _sortBy = value;
                              });
                            }
                          },
                          contentPadding: EdgeInsets.zero,
                        );
                      }),
                      const SizedBox(height: 16),
                      const Text(
                        'Sort Order',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 8),
                      RadioListTile<TodoSortOrder>(
                        title: const Text('Ascending (A-Z, Oldest first)'),
                        value: TodoSortOrder.asc,
                        groupValue: _sortOrder,
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _sortOrder = value;
                            });
                          }
                        },
                        contentPadding: EdgeInsets.zero,
                      ),
                      RadioListTile<TodoSortOrder>(
                        title: const Text('Descending (Z-A, Newest first)'),
                        value: TodoSortOrder.desc,
                        groupValue: _sortOrder,
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _sortOrder = value;
                            });
                          }
                        },
                        contentPadding: EdgeInsets.zero,
                      ),
                    ],
                  ),
                ),
              ),
              // Actions
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  border: Border(
                    top: BorderSide(color: Colors.grey[200]!),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                            side: BorderSide(color: Colors.grey[300]!),
                          ),
                        ),
                        child: const Text('Cancel'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          final sortOptions = TodoSortOptions(
                            sortBy: _sortBy,
                            sortOrder: _sortOrder,
                          );
                          widget.onSortChanged(sortOptions);
                          Navigator.pop(context);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text('Apply'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  String _getSortByDisplayName(TodoSortBy sortBy) {
    switch (sortBy) {
      case TodoSortBy.createdAt:
        return 'Created Date';
      case TodoSortBy.dueDate:
        return 'Due Date';
      case TodoSortBy.title:
        return 'Title';
      case TodoSortBy.status:
        return 'Status';
      case TodoSortBy.priority:
        return 'Priority';
    }
  }
}
