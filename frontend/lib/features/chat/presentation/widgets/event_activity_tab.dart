import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../auth/domain/services/auth_service.dart';
import '../../data/models/event_model.dart';
import '../../data/models/message_model.dart';
import '../../data/datasources/event_api_service.dart';
import '../../domain/repositories/chat_repository.dart';
import '../dialogs/suggest_dates_dialog.dart';
import '../dialogs/confirm_date_dialog.dart';
import '../dialogs/edit_event_dialog.dart';
import '../dialogs/event_calendar_dialog.dart';
import 'create_event_dialog.dart';

class EventActivityTab extends StatefulWidget {
  final String spaceId;

  const EventActivityTab({
    super.key,
    required this.spaceId,
  });

  @override
  State<EventActivityTab> createState() => _EventActivityTabState();
}

class _EventActivityTabState extends State<EventActivityTab>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late EventApiService _eventApiService;
  List<EventModel> _events = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _eventApiService = DependencyInjection.getIt<EventApiService>();
    _loadEvents();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadEvents() async {
    setState(() => _isLoading = true);

    try {
      final eventsData = await _eventApiService.getEvents(widget.spaceId);
      final events = eventsData
          .map((eventData) => EventModel.fromJson(eventData))
          .toList();

      setState(() {
        _events = events;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading events: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: _buildTabContent(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateEventDialog,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        tabs: [
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.schedule, size: 16),
                const SizedBox(width: 4),
                Text('Upcoming (${_getUpcomingEvents().length})'),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.check_circle, size: 16),
                const SizedBox(width: 4),
                Text('Past (${_getPastEvents().length})'),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.cancel, size: 16),
                const SizedBox(width: 4),
                Text('Cancelled (${_getCancelledEvents().length})'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildEventsList(_getUpcomingEvents()),
        _buildEventsList(_getPastEvents()),
        _buildEventsList(_getCancelledEvents()),
      ],
    );
  }

  Widget _buildEventsList(List<EventModel> events) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (events.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadEvents,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: events.length,
        itemBuilder: (context, index) {
          final event = events[index];
          return _buildEventCard(event);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return RefreshIndicator(
      onRefresh: _loadEvents,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.6,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.event_busy,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No events found',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Events will appear here when created',
                  style: TextStyle(
                    color: Colors.grey[500],
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Pull down to refresh',
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEventCard(EventModel event) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: GestureDetector(
        onTap: () => _showEventParticipation(event),
        onLongPress: () => _showEventOptions(event),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.border,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildEventHeader(event),
              const SizedBox(height: 8),
              _buildEventTitle(event),
              if (event.description != null &&
                  event.description!.isNotEmpty) ...[
                const SizedBox(height: 6),
                _buildEventDescription(event),
              ],
              if (event.hasSuggestedDates) ...[
                const SizedBox(height: 6),
                _buildEventDates(event),
              ],
              if (event.eventTime != null && event.eventTime!.isNotEmpty) ...[
                const SizedBox(height: 6),
                _buildEventTime(event),
              ],
              if (event.location != null && event.location!.isNotEmpty) ...[
                const SizedBox(height: 6),
                _buildEventLocation(event),
              ],
              const SizedBox(height: 6),
              _buildEventParticipantsInfo(event),
              // Debug: Always show tags section for testing
              const SizedBox(height: 6),
              _buildEventTags(event),
              const SizedBox(height: 8),
              _buildActionButtons(event),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEventHeader(EventModel event) {
    return Row(
      children: [
        const Icon(
          Icons.event,
          color: AppColors.primary,
          size: 16,
        ),
        const SizedBox(width: 6),
        const Text(
          'Event',
          style: TextStyle(
            color: AppColors.primary,
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
        ),
        const SizedBox(width: 8),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: _getEventStatusColor(event.status).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getEventStatusText(event.status),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: _getEventStatusColor(event.status),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEventTitle(EventModel event) {
    return Text(
      event.title,
      style: const TextStyle(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.w600,
        fontSize: 15,
      ),
    );
  }

  Widget _buildEventDescription(EventModel event) {
    return Text(
      event.description!,
      style: const TextStyle(
        fontSize: 13,
        color: AppColors.textSecondary,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildEventDates(EventModel event) {
    if (!event.hasSuggestedDates) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        const Icon(
          Icons.calendar_today,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            _formatDates(event.eventDates),
            style: const TextStyle(
              fontSize: 11,
              color: AppColors.textSecondary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEventTime(EventModel event) {
    return Row(
      children: [
        const Icon(
          Icons.access_time,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          event.eventTime!,
          style: const TextStyle(
            fontSize: 11,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildEventLocation(EventModel event) {
    return Row(
      children: [
        const Icon(
          Icons.location_on,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            event.location!,
            style: const TextStyle(
              fontSize: 11,
              color: AppColors.textSecondary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildEventParticipantsInfo(EventModel event) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(
          Icons.people,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          '${event.responses.length} responses',
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
        const Spacer(),
        Text(
          'by ${event.creatorName}',
          style: const TextStyle(
            fontSize: 11,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildEventTags(EventModel event) {
    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: event.tags.map((tagName) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            tagName,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildActionButtons(EventModel event) {
    // Get current user ID
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserId = authService.currentUser?.id ?? '';

    // Check if current user has selected dates
    final hasUserSelectedDates = event.hasUserSelectedDates(currentUserId);
    final suggestButtonText = hasUserSelectedDates ? 'Edit Suggest' : 'Suggest';

    // Check if current user is the event creator and event is not confirmed
    final isCreator = event.creatorId == currentUserId;
    final isNotConfirmed = event.confirmedDate == null;
    final showConfirmButton =
        isCreator && isNotConfirmed && event.hasSuggestedDates;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextButton.icon(
              onPressed: event.hasSuggestedDates
                  ? () => _handleJoinEvent(event)
                  : null,
              icon: Icon(
                Icons.person_add,
                size: 14,
                color:
                    event.hasSuggestedDates ? AppColors.primary : Colors.grey,
              ),
              label: Text(
                'Join',
                style: TextStyle(
                  color:
                      event.hasSuggestedDates ? AppColors.primary : Colors.grey,
                  fontSize: 12,
                ),
              ),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ),
          Container(
            width: 1,
            height: 20,
            color: Colors.grey[300],
            margin: const EdgeInsets.symmetric(horizontal: 6),
          ),
          Expanded(
            child: TextButton.icon(
              onPressed: () => _handleSuggestDates(event),
              icon: Icon(
                Icons.calendar_month,
                size: 14,
                color: Colors.grey[600],
              ),
              label: Text(
                suggestButtonText,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ),
          if (showConfirmButton) ...[
            Container(
              width: 1,
              height: 20,
              color: Colors.grey[300],
              margin: const EdgeInsets.symmetric(horizontal: 6),
            ),
            Expanded(
              child: TextButton.icon(
                onPressed: () => _handleConfirmDate(event),
                icon: Icon(
                  Icons.check_circle,
                  size: 14,
                  color: Colors.green[600],
                ),
                label: Text(
                  'Confirm',
                  style: TextStyle(
                    color: Colors.green[600],
                    fontSize: 12,
                  ),
                ),
                style: TextButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getEventStatusColor(EventStatus status) {
    switch (status) {
      case EventStatus.pending:
        return Colors.orange;
      case EventStatus.confirmed:
        return Colors.green;
      case EventStatus.cancelled:
        return Colors.red;
    }
  }

  String _getEventStatusText(EventStatus status) {
    switch (status) {
      case EventStatus.pending:
        return 'Pending';
      case EventStatus.confirmed:
        return 'Confirmed';
      case EventStatus.cancelled:
        return 'Cancelled';
    }
  }

  String _formatDates(List<DateTime> dates) {
    if (dates.isEmpty) return 'No dates';

    // Sort dates to display in chronological order
    final sortedDates = List<DateTime>.from(dates)..sort();

    if (sortedDates.length == 1) {
      return _formatSingleDate(sortedDates.first);
    }

    // Use the same formatting logic as Create Event
    final dateRanges = _groupConsecutiveDates(sortedDates);
    final formattedRanges = <String>[];

    for (final range in dateRanges) {
      if (range.length == 1) {
        // Single date - use relative format if applicable
        final date = range.first;
        final dateOnly = DateTime(date.year, date.month, date.day);
        final now = DateTime.now();
        final today = DateTime(now.year, now.month, now.day);
        final tomorrow = today.add(const Duration(days: 1));

        if (dateOnly == today) {
          formattedRanges.add('Today');
        } else if (dateOnly == tomorrow) {
          formattedRanges.add('Tomorrow');
        } else {
          formattedRanges.add('${date.day}/${date.month}');
        }
      } else if (range.length == 2) {
        // Two consecutive dates
        formattedRanges.add(
            '${range.first.day}/${range.first.month}, ${range.last.day}/${range.last.month}');
      } else {
        // Range of 3 or more dates
        formattedRanges.add(
            '${range.first.day}/${range.first.month}-${range.last.day}/${range.last.month}');
      }
    }

    return formattedRanges.join(', ');
  }

  List<List<DateTime>> _groupConsecutiveDates(List<DateTime> sortedDates) {
    if (sortedDates.isEmpty) return [];

    final groups = <List<DateTime>>[];
    List<DateTime> currentGroup = [sortedDates.first];

    for (int i = 1; i < sortedDates.length; i++) {
      final currentDate = sortedDates[i];
      final previousDate = sortedDates[i - 1];

      // Check if dates are consecutive (difference of 1 day)
      final daysDifference = currentDate.difference(previousDate).inDays;

      if (daysDifference == 1) {
        // Consecutive date, add to current group
        currentGroup.add(currentDate);
      } else {
        // Non-consecutive date, start new group
        groups.add(currentGroup);
        currentGroup = [currentDate];
      }
    }

    // Add the last group
    groups.add(currentGroup);

    return groups;
  }

  String _formatSingleDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    if (dateOnly == today) {
      return 'Today';
    } else if (dateOnly == tomorrow) {
      return 'Tomorrow';
    } else {
      return DateFormat('MMM d').format(date);
    }
  }

  void _showCreateEventDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreateEventDialog(
        spaceId: widget.spaceId,
        onCreateEvent: _handleCreateEvent,
      ),
    );
  }

  Future<void> _handleCreateEvent(CreateEventRequest request) async {
    try {
      // Create event message through chat API instead of direct event API
      // This ensures both chat message and event record are created
      final chatRepository = DependencyInjection.getIt<ChatRepository>();

      final messageRequest = CreateMessageRequest(
        content: request.title,
        type: MessageType.event,
        metadata: request.toJson(),
      );

      await chatRepository.sendMessage(widget.spaceId, messageRequest);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Event created successfully!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
        // Refresh the events list
        _loadEvents();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create event: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _showEventParticipation(EventModel event) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => EventCalendarDialog(
        event: event,
      ),
    );
  }

  void _showEventOptions(EventModel event) async {
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserId = authService.currentUser?.id;
    final isCreator = currentUserId == event.creatorId;

    if (!mounted) return;

    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isCreator) ...[
              ListTile(
                leading: const Icon(Icons.edit, color: AppColors.primary),
                title: const Text('Edit Event'),
                onTap: () {
                  Navigator.pop(context);
                  _showEditEventDialog(event);
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete, color: AppColors.error),
                title: const Text('Delete Event'),
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteEventDialog(event);
                },
              ),
            ] else ...[
              const ListTile(
                leading:
                    Icon(Icons.info_outline, color: AppColors.textSecondary),
                title: Text(
                  'Only the event creator can edit or delete this event',
                  style: TextStyle(color: AppColors.textSecondary),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _handleJoinEvent(EventModel event) async {
    try {
      final joinRequest = JoinEventRequest(
        status: EventResponseStatus.pending,
        note: null,
      );

      await _eventApiService.joinEvent(
        widget.spaceId,
        event.id,
        joinRequest,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Successfully joined the event!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
        // Refresh the events list
        _loadEvents();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to join event: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _handleSuggestDates(EventModel event) async {
    // Get current user ID and their previously selected dates
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserId = authService.currentUser?.id ?? '';

    // Convert user's previously selected date strings to DateTime objects
    final userSelectedDateStrings = event.getUserSelectedDates(currentUserId);
    final initialSelectedDates = userSelectedDateStrings
        .map((dateStr) => DateTime.parse(dateStr))
        .toList();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (dialogContext) => SuggestDatesDialog(
        event: event,
        initialSelectedDates: initialSelectedDates,
        onSuggestDates: (selectedDates) async {
          try {
            final suggestRequest = SuggestEventDatesRequest(
              suggestedDates: selectedDates
                  .map((date) =>
                      '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}')
                  .toList(),
              note: null,
            );

            await _eventApiService.suggestEventDates(
              widget.spaceId,
              event.id,
              suggestRequest,
            );

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Successfully suggested alternative dates!'),
                  backgroundColor: Colors.green,
                  duration: Duration(seconds: 2),
                ),
              );
              // Refresh the events list
              _loadEvents();
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to suggest dates: $e'),
                  backgroundColor: Colors.red,
                  duration: const Duration(seconds: 3),
                ),
              );
            }
          }
        },
      ),
    );
  }

  void _showEditEventDialog(EventModel event) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => EditEventDialog(
        spaceId: widget.spaceId,
        event: event,
        onUpdateEvent: (updateRequest) {
          _handleUpdateEvent(event.id, updateRequest);
        },
      ),
    );
  }

  Future<void> _handleUpdateEvent(
      String eventId, UpdateEventRequest updateRequest) async {
    try {
      await _eventApiService.updateEvent(
        widget.spaceId,
        eventId,
        updateRequest.toJson(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Event updated successfully!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
        // Refresh the events list
        _loadEvents();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update event: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _handleConfirmDate(EventModel event) async {
    final currentContext = context;

    // Show date picker dialog for confirming event date
    showModalBottomSheet(
      context: currentContext,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (dialogContext) => ConfirmDateDialog(
        event: event,
        onConfirmDate: (confirmedDate, confirmedTime) async {
          try {
            final confirmRequest = ConfirmEventDateRequest(
              confirmedDate:
                  '${confirmedDate.year}-${confirmedDate.month.toString().padLeft(2, '0')}-${confirmedDate.day.toString().padLeft(2, '0')}',
              confirmedTime: confirmedTime,
            );

            await _eventApiService.confirmEventDate(
              widget.spaceId,
              event.id,
              confirmRequest,
            );

            if (mounted && currentContext.mounted) {
              ScaffoldMessenger.of(currentContext).showSnackBar(
                const SnackBar(
                  content: Text('Successfully confirmed event date!'),
                  backgroundColor: Colors.green,
                  duration: Duration(seconds: 2),
                ),
              );
              // Refresh the events list
              _loadEvents();
            }
          } catch (e) {
            if (mounted && currentContext.mounted) {
              ScaffoldMessenger.of(currentContext).showSnackBar(
                SnackBar(
                  content: Text('Failed to confirm date: $e'),
                  backgroundColor: Colors.red,
                  duration: const Duration(seconds: 3),
                ),
              );
            }
          }
        },
      ),
    );
  }

  void _showDeleteEventDialog(EventModel event) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const Icon(
              Icons.warning,
              color: AppColors.error,
              size: 48,
            ),
            const SizedBox(height: 16),
            const Text(
              'Delete Event',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Are you sure you want to delete "${event.title}"?',
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(color: Colors.grey[300]!),
                      ),
                    ),
                    child: const Text(
                      'Cancel',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _deleteEvent(event);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.error,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'Delete',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: MediaQuery.of(context).padding.bottom),
          ],
        ),
      ),
    );
  }

  Future<void> _deleteEvent(EventModel event) async {
    try {
      await _eventApiService.deleteEvent(widget.spaceId, event.id);
      await _loadEvents(); // Refresh the list
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Event deleted successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting event: $e')),
        );
      }
    }
  }

  List<EventModel> _getUpcomingEvents() {
    final now = DateTime.now();
    return _events.where((event) {
      if (event.status == EventStatus.cancelled) return false;

      // If event has no dates, show it in upcoming
      if (event.firstEventDate == null) return true;

      return event.firstEventDate!.isAfter(now);
    }).toList();
  }

  List<EventModel> _getPastEvents() {
    final now = DateTime.now();
    return _events.where((event) {
      if (event.status == EventStatus.cancelled) return false;

      // Only show events with dates that are in the past
      return event.firstEventDate != null &&
          event.firstEventDate!.isBefore(now);
    }).toList();
  }

  List<EventModel> _getCancelledEvents() {
    return _events
        .where((event) => event.status == EventStatus.cancelled)
        .toList();
  }
}
