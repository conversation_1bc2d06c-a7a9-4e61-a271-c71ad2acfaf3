import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../data/models/linkable_item_model.dart';
import '../../data/datasources/todo_api_service.dart';
import '../../../togo/data/services/togo_service.dart';
import '../../../../core/di/dependency_injection.dart';

class LinkableItemSelector extends StatefulWidget {
  final String spaceId;
  final Function(LinkableItem?) onItemSelected;
  final LinkableItem? selectedItem;

  const LinkableItemSelector({
    super.key,
    required this.spaceId,
    required this.onItemSelected,
    this.selectedItem,
  });

  @override
  State<LinkableItemSelector> createState() => _LinkableItemSelectorState();
}

class _LinkableItemSelectorState extends State<LinkableItemSelector> {
  List<LinkableItem> _items = [];
  bool _isLoading = false;
  String? _error;
  LinkableItemType _selectedType = LinkableItemType.todo;

  late TodoApiService _todoApiService;
  late ToGoService _togoService;

  @override
  void initState() {
    super.initState();
    _initServices();
    _loadItems();
  }

  void _initServices() {
    _todoApiService = DependencyInjection.getIt<TodoApiService>();
    _togoService = DependencyInjection.getIt<ToGoService>();
  }

  Future<void> _loadItems() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      List<LinkableItem> items = [];

      if (_selectedType == LinkableItemType.todo) {
        // Load todos
        final response = await _todoApiService.getTodos(widget.spaceId);
        if (response.success && response.data != null) {
          items = response.data!.todos
              .map((todo) => LinkableItem.fromTodo(todo.toJson()))
              .toList();
        }
      } else {
        // Load togos
        final togos = await _togoService.getToGos(widget.spaceId);
        items =
            togos.map((togo) => LinkableItem.fromToGo(togo.toJson())).toList();
      }

      setState(() {
        _items = items;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load items: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Todo OR ToGo item',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),

        // Type selector
        Row(
          children: [
            Expanded(
              child: _buildTypeButton(LinkableItemType.todo, 'Todo'),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTypeButton(LinkableItemType.togo, 'ToGo'),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Items list
        Container(
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: _buildItemsList(),
        ),
      ],
    );
  }

  Widget _buildTypeButton(LinkableItemType type, String label) {
    final isSelected = _selectedType == type;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedType = type;
        });
        _loadItems();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey.shade300,
          ),
        ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey.shade700,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildItemsList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red.shade400),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(color: Colors.red.shade600, fontSize: 12),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: _loadItems,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _selectedType == LinkableItemType.todo
                  ? Icons.checklist
                  : Icons.place,
              color: Colors.grey.shade400,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              'No ${_selectedType == LinkableItemType.todo ? 'todos' : 'places'} found',
              style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _items.length,
      itemBuilder: (context, index) {
        final item = _items[index];
        return _buildItemTile(
          item,
          item.displayTitle,
          item.displaySubtitle,
        );
      },
    );
  }

  Widget _buildItemTile(LinkableItem item, String title, String subtitle) {
    final isSelected = widget.selectedItem?.id == item.id;

    return ListTile(
      dense: true,
      leading: Radio<String>(
        value: item.id,
        groupValue: widget.selectedItem?.id,
        onChanged: (value) {
          widget.onItemSelected(item);
        },
        activeColor: AppColors.primary,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 13,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      subtitle: subtitle.isNotEmpty
          ? Text(
              subtitle,
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey.shade600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            )
          : null,
      onTap: () {
        widget.onItemSelected(item);
      },
    );
  }
}
