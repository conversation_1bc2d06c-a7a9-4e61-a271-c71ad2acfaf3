import 'package:flutter/material.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../spaces/data/models/space_member_model.dart';
import '../../../spaces/domain/usecases/get_space_members_usecase.dart';
import '../../data/models/lucky_draw_model.dart';
import 'member_selection_dialog.dart';

class CreateLuckyDrawDialog extends StatefulWidget {
  final String spaceId;
  final Function(CreateLuckyDrawRequest) onCreateLuckyDraw;

  const CreateLuckyDrawDialog({
    super.key,
    required this.spaceId,
    required this.onCreateLuckyDraw,
  });

  @override
  State<CreateLuckyDrawDialog> createState() => _CreateLuckyDrawDialogState();
}

class _CreateLuckyDrawDialogState extends State<CreateLuckyDrawDialog> {
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _maxParticipantsController = TextEditingController();

  final List<TextEditingController> _prizeNameControllers = [];
  final List<TextEditingController> _prizeDescriptionControllers = [];
  final List<TextEditingController> _prizeQuantityControllers = [];

  bool _allowMultipleEntries = false;
  bool _isInviteOnly = false;
  bool _autoDrawOnExpiry = false;
  DateTime? _expiresAt;

  List<SpaceMemberModel> _spaceMembers = [];
  List<String> _selectedMemberIds = [];
  bool _isLoadingMembers = false;

  @override
  void initState() {
    super.initState();
    // Start with one prize
    _addPrize();
    // Load space members
    _loadSpaceMembers();
  }

  Future<void> _loadSpaceMembers() async {
    setState(() {
      _isLoadingMembers = true;
    });

    try {
      final getSpaceMembersUseCase =
          DependencyInjection.getIt<GetSpaceMembersUseCase>();
      final members = await getSpaceMembersUseCase.execute(widget.spaceId);

      if (mounted) {
        setState(() {
          _spaceMembers = members;
          // Set max participants to space member count by default
          _maxParticipantsController.text = members.length.toString();
          _isLoadingMembers = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingMembers = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load space members: $e')),
        );
      }
    }
  }

  void _showMemberSelectionDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MemberSelectionDialog(
        members: _spaceMembers,
        selectedMemberIds: _selectedMemberIds,
        onSelectionChanged: (selectedIds) {
          setState(() {
            _selectedMemberIds = selectedIds;
          });
        },
      ),
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _maxParticipantsController.dispose();

    for (final controller in _prizeNameControllers) {
      controller.dispose();
    }
    for (final controller in _prizeDescriptionControllers) {
      controller.dispose();
    }
    for (final controller in _prizeQuantityControllers) {
      controller.dispose();
    }

    super.dispose();
  }

  void _addPrize() {
    setState(() {
      _prizeNameControllers.add(TextEditingController());
      _prizeDescriptionControllers.add(TextEditingController());
      _prizeQuantityControllers.add(TextEditingController(text: '1'));
    });
  }

  void _removePrize(int index) {
    if (_prizeNameControllers.length <= 1) return; // Keep at least one prize

    setState(() {
      _prizeNameControllers[index].dispose();
      _prizeDescriptionControllers[index].dispose();
      _prizeQuantityControllers[index].dispose();

      _prizeNameControllers.removeAt(index);
      _prizeDescriptionControllers.removeAt(index);
      _prizeQuantityControllers.removeAt(index);
    });
  }

  void _createLuckyDraw() {
    final title = _titleController.text.trim();
    if (title.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please enter a title for the lucky draw')),
      );
      return;
    }

    // Validate prizes
    final prizes = <CreatePrizeRequest>[];
    for (int i = 0; i < _prizeNameControllers.length; i++) {
      final prizeName = _prizeNameControllers[i].text.trim();
      if (prizeName.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Please enter a name for prize ${i + 1}')),
        );
        return;
      }

      final quantityText = _prizeQuantityControllers[i].text.trim();
      final quantity = int.tryParse(quantityText) ?? 1;
      if (quantity < 1) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Prize ${i + 1} quantity must be at least 1')),
        );
        return;
      }

      prizes.add(CreatePrizeRequest(
        name: prizeName,
        description: _prizeDescriptionControllers[i].text.trim().isEmpty
            ? null
            : _prizeDescriptionControllers[i].text.trim(),
        quantity: quantity,
      ));
    }

    final maxParticipantsText = _maxParticipantsController.text.trim();
    final maxParticipants = maxParticipantsText.isEmpty
        ? 0
        : (int.tryParse(maxParticipantsText) ?? 0);

    final request = CreateLuckyDrawRequest(
      title: title,
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      prizes: prizes,
      expiresAt: _expiresAt,
      allowMultipleEntries: _allowMultipleEntries,
      maxParticipants: maxParticipants,
      isInviteOnly: _isInviteOnly,
      invitedUserIds: _selectedMemberIds,
      autoDrawOnExpiry: _autoDrawOnExpiry,
    );

    widget.onCreateLuckyDraw(request);
    Navigator.of(context).pop();
  }

  Future<void> _selectExpiryDate() async {
    final now = DateTime.now();
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: _expiresAt ?? now.add(const Duration(days: 7)),
      firstDate: now,
      lastDate: now.add(const Duration(days: 365)),
    );

    if (selectedDate != null && mounted) {
      final selectedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(
            _expiresAt ?? now.add(const Duration(hours: 1))),
      );

      if (selectedTime != null && mounted) {
        setState(() {
          _expiresAt = DateTime(
            selectedDate.year,
            selectedDate.month,
            selectedDate.day,
            selectedTime.hour,
            selectedTime.minute,
          );
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              // Drag handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                child: Row(
                  children: [
                    const Icon(
                      Icons.casino,
                      color: AppColors.primary,
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Create Lucky Draw',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      iconSize: 20,
                    ),
                  ],
                ),
              ),
              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                    // Title
                    TextField(
                      controller: _titleController,
                      decoration: InputDecoration(
                        labelText: 'Lucky Draw Title *',
                        hintText: 'Enter the title of your lucky draw',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                        isDense: true,
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 6),
                      ),
                      maxLength: 100,
                    ),
                    const SizedBox(height: 6),

                    // Description
                    TextField(
                      controller: _descriptionController,
                      decoration: InputDecoration(
                        labelText: 'Description (Optional)',
                        hintText: 'Add a description for your lucky draw',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                        isDense: true,
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 6),
                      ),
                      maxLines: 2,
                      maxLength: 200,
                    ),
                    const SizedBox(height: 6),

                    // Prizes Section
                    Row(
                      children: [
                        const Text(
                          'Prizes',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        TextButton.icon(
                          onPressed: _prizeNameControllers.length < 10
                              ? _addPrize
                              : null,
                          icon: const Icon(Icons.add),
                          label: const Text('Add Prize'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),

                    // Prize List
                    ...List.generate(_prizeNameControllers.length, (index) {
                      return Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  'Prize ${index + 1}',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 14),
                                ),
                                const Spacer(),
                                if (_prizeNameControllers.length > 1)
                                  IconButton(
                                    onPressed: () => _removePrize(index),
                                    icon: const Icon(Icons.delete, size: 18),
                                    color: Colors.red,
                                    constraints: const BoxConstraints(),
                                    padding: const EdgeInsets.all(4),
                                  ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            TextField(
                              controller: _prizeNameControllers[index],
                              decoration: InputDecoration(
                                labelText: 'Prize Name *',
                                hintText: 'e.g., iPhone 15, Gift Card',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                isDense: true,
                                contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 6),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: TextField(
                                    controller:
                                        _prizeDescriptionControllers[index],
                                    decoration: InputDecoration(
                                      labelText: 'Description',
                                      hintText: 'Optional details',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      isDense: true,
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              horizontal: 8, vertical: 6),
                                    ),
                                    maxLines: 1,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: TextField(
                                    controller:
                                        _prizeQuantityControllers[index],
                                    decoration: InputDecoration(
                                      labelText: 'Qty',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      isDense: true,
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              horizontal: 8, vertical: 6),
                                    ),
                                    keyboardType: TextInputType.number,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      );
                    }),

                    const SizedBox(height: 8),

                    // Settings Section
                    const Text(
                      'Settings',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 6),

                    // Max Participants
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _maxParticipantsController,
                            decoration: InputDecoration(
                              labelText: 'Max Participants',
                              hintText: 'Default: ${_spaceMembers.length}',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                              isDense: true,
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 6),
                            ),
                            keyboardType: TextInputType.number,
                          ),
                        ),
                        const SizedBox(width: 6),
                        TextButton(
                          onPressed: () {
                            setState(() {
                              _maxParticipantsController.text =
                                  _spaceMembers.length.toString();
                            });
                          },
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            minimumSize: Size.zero,
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          ),
                          child: const Text('Use Space',
                              style: TextStyle(fontSize: 12)),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),

                    // Settings Row
                    Row(
                      children: [
                        Expanded(
                          child: CheckboxListTile(
                            title: const Text('Invite Only',
                                style: TextStyle(fontSize: 13)),
                            subtitle: Text(
                              _isInviteOnly
                                  ? '${_selectedMemberIds.length} selected'
                                  : 'Anyone can join',
                              style: const TextStyle(fontSize: 11),
                            ),
                            value: _isInviteOnly,
                            onChanged: (value) {
                              setState(() {
                                _isInviteOnly = value ?? false;
                                if (!_isInviteOnly) {
                                  _selectedMemberIds.clear();
                                }
                              });
                            },
                            controlAffinity: ListTileControlAffinity.leading,
                            contentPadding: EdgeInsets.zero,
                            dense: true,
                          ),
                        ),
                        Expanded(
                          child: CheckboxListTile(
                            title: const Text('Multiple Prizes',
                                style: TextStyle(fontSize: 13)),
                            subtitle: const Text(
                                'Same user can win multiple prizes',
                                style: TextStyle(fontSize: 11)),
                            value: _allowMultipleEntries,
                            onChanged: (value) {
                              setState(() {
                                _allowMultipleEntries = value ?? false;
                              });
                            },
                            controlAffinity: ListTileControlAffinity.leading,
                            contentPadding: EdgeInsets.zero,
                            dense: true,
                          ),
                        ),
                      ],
                    ),

                    // Member Selection (only show when invite-only is enabled)
                    if (_isInviteOnly) ...[
                      const SizedBox(height: 6),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                _selectedMemberIds.isEmpty
                                    ? 'No members selected'
                                    : '${_selectedMemberIds.length} members selected',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 12,
                                ),
                              ),
                            ),
                            TextButton.icon(
                              onPressed: _isLoadingMembers
                                  ? null
                                  : _showMemberSelectionDialog,
                              icon: _isLoadingMembers
                                  ? const SizedBox(
                                      width: 14,
                                      height: 14,
                                      child: CircularProgressIndicator(
                                          strokeWidth: 2),
                                    )
                                  : const Icon(Icons.people, size: 14),
                              label: Text(
                                _isLoadingMembers ? 'Loading...' : 'Select',
                                style: const TextStyle(fontSize: 12),
                              ),
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                minimumSize: Size.zero,
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 4),
                    ],

                    // Expiry Date
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Expiry Date',
                                  style: TextStyle(
                                      fontSize: 13,
                                      fontWeight: FontWeight.w500),
                                ),
                                Text(
                                  _expiresAt != null
                                      ? '${_expiresAt!.day}/${_expiresAt!.month}/${_expiresAt!.year} ${_expiresAt!.hour}:${_expiresAt!.minute.toString().padLeft(2, '0')}'
                                      : 'No expiry set',
                                  style: TextStyle(
                                    fontSize: 11,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          TextButton(
                            onPressed: _selectExpiryDate,
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            child: const Text('Set',
                                style: TextStyle(fontSize: 12)),
                          ),
                          if (_expiresAt != null)
                            IconButton(
                              onPressed: () {
                                setState(() {
                                  _expiresAt = null;
                                });
                              },
                              icon: const Icon(Icons.clear, size: 16),
                              constraints: const BoxConstraints(),
                              padding: const EdgeInsets.all(4),
                            ),
                        ],
                      ),
                    ),

                    // Auto Draw Option (only show when expiry date is set)
                    if (_expiresAt != null) ...[
                      const SizedBox(height: 6),
                      CheckboxListTile(
                        title: const Text('Auto Draw on Expiry',
                            style: TextStyle(fontSize: 13)),
                        subtitle: const Text(
                            'Automatically draw winners when expired',
                            style: TextStyle(fontSize: 11)),
                        value: _autoDrawOnExpiry,
                        onChanged: (value) {
                          setState(() {
                            _autoDrawOnExpiry = value ?? false;
                          });
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                        contentPadding: EdgeInsets.zero,
                        dense: true,
                      ),
                    ],

                    const SizedBox(height: 6),
                  ],
                ),
              ),
              // Footer
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text('Cancel'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      flex: 2,
                      child: ElevatedButton(
                        onPressed: _createLuckyDraw,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text('Create'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
