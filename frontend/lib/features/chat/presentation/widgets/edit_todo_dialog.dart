import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../../core/widgets/tag_selector.dart';
import '../../../spaces/domain/usecases/get_space_members_usecase.dart';
import '../../../spaces/data/models/space_member_model.dart';
import '../../data/models/todo_model.dart';
import '../../data/datasources/todo_api_service.dart';
import 'google_location_picker.dart';

class EditTodoDialog extends StatefulWidget {
  final String spaceId;
  final TodoData todo;
  final Function(UpdateTodoRequest) onUpdateTodo;

  const EditTodoDialog({
    super.key,
    required this.spaceId,
    required this.todo,
    required this.onUpdateTodo,
  });

  @override
  State<EditTodoDialog> createState() => _EditTodoDialogState();
}

class _EditTodoDialogState extends State<EditTodoDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _detailsController;
  final TextEditingController _customTagController = TextEditingController();

  List<SpaceMemberModel> _spaceMembers = [];
  List<String> _selectedParticipants = [];
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  String? _selectedLocation;
  List<String> _selectedTags = [];
  final bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.todo.title);
    _detailsController = TextEditingController(text: widget.todo.details ?? '');
    _selectedLocation = widget.todo.location;
    _selectedDate = widget.todo.dueDate;
    if (widget.todo.dueTime != null) {
      final timeParts = widget.todo.dueTime!.split(':');
      if (timeParts.length == 2) {
        _selectedTime = TimeOfDay(
          hour: int.parse(timeParts[0]),
          minute: int.parse(timeParts[1]),
        );
      }
    }
    _selectedTags = widget.todo.tags.map((tag) => tag.name).toList();
    _selectedParticipants =
        widget.todo.participants.map((p) => p.userId).toList();
    _loadSpaceMembers();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _detailsController.dispose();
    _customTagController.dispose();
    super.dispose();
  }

  Future<void> _loadSpaceMembers() async {
    try {
      final getSpaceMembersUseCase =
          DependencyInjection.getIt<GetSpaceMembersUseCase>();
      final members = await getSpaceMembersUseCase.execute(widget.spaceId);
      if (mounted) {
        setState(() {
          _spaceMembers = members;
        });
      }
    } catch (e) {
      // Handle error silently or show a message
    }
  }

  void _updateTodo() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Format time as HH:mm string if selected
    String? dueTimeString;
    if (_selectedTime != null) {
      dueTimeString =
          '${_selectedTime!.hour.toString().padLeft(2, '0')}:${_selectedTime!.minute.toString().padLeft(2, '0')}';
    }

    final updateRequest = UpdateTodoRequest(
      title: _titleController.text.trim(),
      details: _detailsController.text.trim().isEmpty
          ? null
          : _detailsController.text.trim(),
      participantUserIds: _selectedParticipants,
      dueDate: _selectedDate,
      dueTime: dueTimeString,
      location: _selectedLocation,
      tags: _selectedTags,
    );

    widget.onUpdateTodo(updateRequest);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.9,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                _buildDragHandle(),
                _buildHeader(),
                Expanded(
                  child: _buildContent(scrollController),
                ),
                _buildActions(),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildDragHandle() {
    return Container(
      width: 40,
      height: 4,
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          const Icon(Icons.edit, color: AppColors.primary),
          const SizedBox(width: 8),
          const Text(
            'Edit TODO',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(ScrollController scrollController) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        controller: scrollController,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Title *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.title),
              ),
              maxLines: 2,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Title is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Details
            TextFormField(
              controller: _detailsController,
              decoration: const InputDecoration(
                labelText: 'Details',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),

            // Location
            _buildLocationField(),
            const SizedBox(height: 16),

            // Participants
            _buildParticipantsSection(),
            const SizedBox(height: 16),

            // Due Date
            _buildDateTimeSection(),
            const SizedBox(height: 16),

            // Tags
            _buildTagsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading ? null : _updateTodo,
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Update'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Location',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _selectLocation,
                icon: const Icon(Icons.location_on, size: 18),
                label: Text(
                  _selectedLocation ?? 'Select Location',
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            if (_selectedLocation != null) ...[
              const SizedBox(width: 8),
              IconButton(
                onPressed: _clearLocation,
                icon: const Icon(Icons.clear, size: 18),
                tooltip: 'Clear Location',
                padding: const EdgeInsets.all(4),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildParticipantsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Participants',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: _spaceMembers.map((member) {
              final isSelected = _selectedParticipants.contains(member.userId);
              return CheckboxListTile(
                title: Text(member.user.displayName),
                value: isSelected,
                onChanged: (bool? value) {
                  setState(() {
                    if (value == true) {
                      _selectedParticipants.add(member.userId);
                    } else {
                      _selectedParticipants.remove(member.userId);
                    }
                  });
                },
                dense: true,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildDateTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Due Date & Time',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _selectDate,
                icon: const Icon(Icons.calendar_today, size: 18),
                label: Text(
                  _selectedDate != null
                      ? '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}'
                      : 'Select Date',
                ),
              ),
            ),
            if (_selectedDate != null) ...[
              const SizedBox(width: 4),
              IconButton(
                onPressed: _clearDate,
                icon: const Icon(Icons.clear, size: 18),
                tooltip: 'Clear Date',
                padding: const EdgeInsets.all(4),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),
            ],
            const SizedBox(width: 8),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _selectedDate != null ? _selectTime : null,
                icon: const Icon(Icons.access_time, size: 18),
                label: Text(
                  _selectedTime != null
                      ? '${_selectedTime!.hour.toString().padLeft(2, '0')}:${_selectedTime!.minute.toString().padLeft(2, '0')}'
                      : 'Select Time',
                ),
              ),
            ),
            if (_selectedTime != null) ...[
              const SizedBox(width: 4),
              IconButton(
                onPressed: _clearTime,
                icon: const Icon(Icons.clear, size: 18),
                tooltip: 'Clear Time',
                padding: const EdgeInsets.all(4),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildTagsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        TagSelector(
          spaceId: widget.spaceId,
          selectedTags: _selectedTags,
          onTagsChanged: (tags) {
            setState(() {
              _selectedTags = tags;
            });
          },
          hintText: 'Add tags for this TODO...',
          allowCustomTags: true,
          maxTags: 10,
        ),
      ],
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  Future<void> _selectTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _selectedTime ?? TimeOfDay.now(),
    );
    if (time != null) {
      setState(() => _selectedTime = time);
    }
  }

  void _clearDate() {
    setState(() {
      _selectedDate = null;
      _selectedTime = null; // Clear time when date is cleared
    });
  }

  void _clearTime() {
    setState(() {
      _selectedTime = null;
    });
  }

  void _clearLocation() {
    setState(() {
      _selectedLocation = null;
    });
  }

  Future<void> _selectLocation() async {
    final result = await showModalBottomSheet<Map<String, dynamic>>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: false, // Disable drag to avoid conflicts with map gestures
      isDismissible: true, // Still allow closing by tapping outside
      builder: (context) => GoogleLocationPicker(
        onLocationSelected: (locationData) {
          Navigator.pop(context, locationData.toJson());
        },
      ),
    );

    if (result != null) {
      final address = result['address'] as String?;
      if (address != null && address.isNotEmpty) {
        setState(() => _selectedLocation = address);
      }
    }
  }
}
