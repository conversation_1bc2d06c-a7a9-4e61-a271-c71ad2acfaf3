import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/repositories/chat_repository.dart';
import '../../domain/usecases/get_messages_usecase.dart';
import '../../domain/usecases/chat_connection_usecase.dart';
import '../../data/models/message_model.dart';
import '../../data/datasources/chat_websocket_service.dart';
import '../../../auth/domain/services/auth_service.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../../core/utils/error_message_sanitizer.dart';
import 'chat_event.dart';
import 'chat_state.dart';

class ChatBloc extends Bloc<ChatEvent, ChatState> {
  final ChatRepository _repository;
  final GetMessagesUseCase _getMessagesUseCase;
  final ChatConnectionUseCase _connectionUseCase;

  StreamSubscription<MessageModel>? _messageSubscription;
  StreamSubscription<MessageModel>? _messageUpdateSubscription;
  StreamSubscription<TypingEvent>? _typingSubscription;
  StreamSubscription<bool>? _connectionSubscription;
  StreamSubscription<String>? _errorSubscription;

  String? _currentSpaceId;
  Timer? _typingTimer;
  String? _lastError; // Track last error to prevent duplicates
  DateTime? _lastErrorTime; // Track when last error occurred

  ChatBloc({
    required ChatRepository repository,
    required GetMessagesUseCase getMessagesUseCase,
    required ChatConnectionUseCase connectionUseCase,
  })  : _repository = repository,
        _getMessagesUseCase = getMessagesUseCase,
        _connectionUseCase = connectionUseCase,
        super(const ChatInitial()) {
    on<ChatInitialized>(_onChatInitialized);
    on<ChatMessagesLoadRequested>(_onMessagesLoadRequested);
    on<ChatLoadMoreRequested>(_onLoadMoreRequested);
    on<ChatMessageSent>(_onMessageSent);
    on<ChatMessageReceived>(_onMessageReceived);
    on<ChatMessageUpdated>(_onMessageUpdated);
    on<ChatMessageUpdatedFromWebSocket>(_onMessageUpdatedFromWebSocket);
    on<ChatMessageDeleted>(_onMessageDeleted);
    on<ChatTypingStarted>(_onTypingStarted);
    on<ChatTypingStopped>(_onTypingStopped);
    on<ChatTypingEventReceived>(_onTypingEventReceived);
    on<ChatConnectionChanged>(_onConnectionChanged);
    on<ChatErrorOccurred>(_onErrorOccurred);
    on<ChatErrorCleared>(_onErrorCleared);
    on<ChatDisposed>(_onChatDisposed);

    _setupWebSocketListeners();
  }

  void _setupWebSocketListeners() {
    // Cancel existing subscriptions to prevent duplicates
    _messageSubscription?.cancel();
    _messageUpdateSubscription?.cancel();
    _typingSubscription?.cancel();
    _connectionSubscription?.cancel();
    _errorSubscription?.cancel();

    // Listen to incoming messages
    _messageSubscription = _repository.messageStream.listen(
      (message) => add(ChatMessageReceived(message)),
      onError: (error) => add(ChatErrorOccurred(
          ErrorMessageSanitizer.getUserFriendlyMessage(error))),
    );

    // Listen to message updates (like poll votes)
    _messageUpdateSubscription = _repository.messageUpdateStream.listen(
      (message) => add(ChatMessageUpdatedFromWebSocket(message)),
      onError: (error) => add(ChatErrorOccurred(
          ErrorMessageSanitizer.getUserFriendlyMessage(error))),
    );

    // Listen to typing events
    _typingSubscription = _repository.typingStream.listen(
      (typingEvent) => add(ChatTypingEventReceived(
        userId: typingEvent.userId,
        userName: typingEvent.userName,
        isTyping: typingEvent.isTyping,
      )),
      onError: (error) => add(ChatErrorOccurred(
          ErrorMessageSanitizer.getUserFriendlyMessage(error))),
    );

    // Listen to connection status
    _connectionSubscription = _repository.connectionStream.listen(
      (isConnected) => add(ChatConnectionChanged(isConnected)),
      onError: (error) => add(ChatErrorOccurred(
          ErrorMessageSanitizer.getUserFriendlyMessage(error))),
    );

    // Listen to errors
    _errorSubscription = _repository.errorStream.listen(
      (error) => add(ChatErrorOccurred(error)),
    );
  }

  Future<void> _onChatInitialized(
    ChatInitialized event,
    Emitter<ChatState> emit,
  ) async {
    try {
      emit(const ChatLoading());

      // Leave previous space if switching spaces
      if (_currentSpaceId != null && _currentSpaceId != event.spaceId) {
        await _connectionUseCase.leaveSpace(_currentSpaceId!);
      }

      _currentSpaceId = event.spaceId;

      // Connect to WebSocket if not connected
      if (!_repository.isConnected) {
        await _connectionUseCase.connect();
      }

      // Join the space
      await _connectionUseCase.joinSpace(event.spaceId);

      // Load initial messages
      add(const ChatMessagesLoadRequested());
    } catch (error) {
      emit(ChatError(ErrorMessageSanitizer.getUserFriendlyMessage(error),
          spaceId: event.spaceId));
    }
  }

  Future<void> _onMessagesLoadRequested(
    ChatMessagesLoadRequested event,
    Emitter<ChatState> emit,
  ) async {
    if (_currentSpaceId == null) return;

    try {
      final currentState = state;

      if (event.isRefresh && currentState is ChatLoaded) {
        emit(currentState.copyWith(isRefreshing: true, clearError: true));
      }

      final request = GetMessagesRequest(
        limit: 50,
        offset: event.isRefresh ? 0 : 0,
      );

      final messages =
          await _getMessagesUseCase.execute(_currentSpaceId!, request);

      emit(ChatLoaded(
        spaceId: _currentSpaceId!,
        messages: messages,
        hasMore: messages.length >= 50,
        isConnected: _repository.isConnected,
      ));
    } catch (error) {
      if (state is ChatLoaded) {
        final currentState = state as ChatLoaded;
        emit(currentState.copyWith(
          error: ErrorMessageSanitizer.getUserFriendlyMessage(error),
          isRefreshing: false,
        ));
      } else {
        emit(ChatError(ErrorMessageSanitizer.getUserFriendlyMessage(error),
            spaceId: _currentSpaceId));
      }
    }
  }

  Future<void> _onLoadMoreRequested(
    ChatLoadMoreRequested event,
    Emitter<ChatState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ChatLoaded ||
        currentState.isLoadingMore ||
        !currentState.hasMore ||
        _currentSpaceId == null ||
        currentState.messages.isEmpty) {
      return;
    }

    try {
      emit(currentState.copyWith(isLoadingMore: true));

      // Get the oldest message's timestamp to load messages before it
      final oldestMessage = currentState.messages.first;

      final request = GetMessagesRequest(
        limit: 50,
        before: oldestMessage.createdAt,
      );

      final newMessages =
          await _getMessagesUseCase.execute(_currentSpaceId!, request);

      // Add older messages to the beginning of the list (chronological order)
      final allMessages = [...newMessages, ...currentState.messages];

      emit(currentState.copyWith(
        messages: allMessages,
        hasMore: newMessages.length >= 50,
        isLoadingMore: false,
      ));
    } catch (error) {
      emit(currentState.copyWith(
        error: ErrorMessageSanitizer.getUserFriendlyMessage(error),
        isLoadingMore: false,
      ));
    }
  }

  Future<void> _onMessageSent(
    ChatMessageSent event,
    Emitter<ChatState> emit,
  ) async {
    if (_currentSpaceId == null) return;

    final currentState = state;
    if (currentState is! ChatLoaded) return;

    // Get current user info
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUser = authService.currentUser;

    // Create a temporary message with "sending" status
    final tempMessage = MessageModel(
      id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      spaceId: _currentSpaceId!,
      senderId: currentUser?.id ?? 'unknown',
      senderName: currentUser?.displayName ?? 'You',
      type: event.type,
      content: event.content,
      metadata: event.metadata,
      replyToId: event.replyToId,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      status: MessageStatus.sending,
    );

    // Add temporary message to UI immediately (at the end for chronological order)
    final updatedMessages = [...currentState.messages, tempMessage];
    emit(currentState.copyWith(messages: updatedMessages));

    try {
      final request = CreateMessageRequest(
        content: event.content,
        type: event.type,
        metadata: event.metadata,
        replyToId: event.replyToId,
      );

      // Send via WebSocket for real-time delivery
      await _repository.sendMessageViaWebSocket(_currentSpaceId!, request);

      // Update message status to sent
      final sentMessage = tempMessage.copyWith(status: MessageStatus.sent);
      final sentMessages = currentState.messages.map((msg) {
        return msg.id == tempMessage.id ? sentMessage : msg;
      }).toList();

      emit(currentState.copyWith(messages: sentMessages));
    } catch (error) {
      // Update message status to failed
      final failedMessage = tempMessage.copyWith(status: MessageStatus.failed);
      final failedMessages = currentState.messages.map((msg) {
        return msg.id == tempMessage.id ? failedMessage : msg;
      }).toList();

      emit(currentState.copyWith(messages: failedMessages));
      add(ChatErrorOccurred(
          ErrorMessageSanitizer.getUserFriendlyMessage(error)));
    }
  }

  void _onMessageReceived(
    ChatMessageReceived event,
    Emitter<ChatState> emit,
  ) {
    // print(
    //     '🔔 ChatBloc: Received message ${event.message.id}, type: ${event.message.type}');
    final currentState = state;
    if (currentState is ChatLoaded) {
      // Check if message already exists to avoid duplicates
      final messageExists =
          currentState.messages.any((msg) => msg.id == event.message.id);

      // print(
      //     '🔔 ChatBloc: Message exists: $messageExists, current messages count: ${currentState.messages.length}');

      if (!messageExists) {
        // Add new message to the end of the list (chronological order)
        final updatedMessages = [...currentState.messages, event.message];

        // print(
        //     '🔔 ChatBloc: Adding new message, updated count: ${updatedMessages.length}');

        emit(currentState.copyWith(
          messages: updatedMessages,
          clearError: true,
        ));
      } else {
        // print('🔔 ChatBloc: Message already exists, skipping');
      }
    } else {
      // print(
      //     '🔔 ChatBloc: Current state is not ChatLoaded: ${currentState.runtimeType}');
    }
  }

  Future<void> _onMessageUpdated(
    ChatMessageUpdated event,
    Emitter<ChatState> emit,
  ) async {
    try {
      final request = UpdateMessageRequest(content: event.content);
      final updatedMessage =
          await _repository.updateMessage(event.messageId, request);

      final currentState = state;
      if (currentState is ChatLoaded) {
        // Update the message in the local state
        final updatedMessages = currentState.messages.map((message) {
          if (message.id == event.messageId) {
            return updatedMessage;
          }
          return message;
        }).toList();

        emit(currentState.copyWith(
          messages: updatedMessages,
          clearError: true,
        ));
      }
    } catch (error) {
      add(ChatErrorOccurred(
          ErrorMessageSanitizer.getUserFriendlyMessage(error)));
    }
  }

  void _onMessageUpdatedFromWebSocket(
    ChatMessageUpdatedFromWebSocket event,
    Emitter<ChatState> emit,
  ) {
    final currentState = state;
    if (currentState is ChatLoaded) {
      // Update the message in the local state
      final updatedMessages = currentState.messages.map((message) {
        if (message.id == event.message.id) {
          return event.message;
        }
        return message;
      }).toList();

      emit(currentState.copyWith(
        messages: updatedMessages,
        clearError: true,
      ));
    }
  }

  Future<void> _onMessageDeleted(
    ChatMessageDeleted event,
    Emitter<ChatState> emit,
  ) async {
    try {
      await _repository.deleteMessage(event.messageId);

      final currentState = state;
      if (currentState is ChatLoaded) {
        // Mark the message as deleted instead of removing it
        final updatedMessages = currentState.messages.map((message) {
          if (message.id == event.messageId) {
            return message.copyWith(isDeleted: true);
          }
          return message;
        }).toList();

        emit(currentState.copyWith(
          messages: updatedMessages,
          clearError: true,
        ));
      }
    } catch (error) {
      add(ChatErrorOccurred(
          ErrorMessageSanitizer.getUserFriendlyMessage(error)));
    }
  }

  void _onTypingStarted(
    ChatTypingStarted event,
    Emitter<ChatState> emit,
  ) {
    if (_currentSpaceId == null) return;

    _connectionUseCase.startTyping(_currentSpaceId!);

    // Cancel previous timer
    _typingTimer?.cancel();

    // Stop typing after 3 seconds of inactivity
    _typingTimer = Timer(const Duration(seconds: 3), () {
      add(const ChatTypingStopped());
    });
  }

  void _onTypingStopped(
    ChatTypingStopped event,
    Emitter<ChatState> emit,
  ) {
    if (_currentSpaceId == null) return;

    _connectionUseCase.stopTyping(_currentSpaceId!);
    _typingTimer?.cancel();
  }

  void _onTypingEventReceived(
    ChatTypingEventReceived event,
    Emitter<ChatState> emit,
  ) {
    final currentState = state;
    if (currentState is ChatLoaded) {
      final updatedTypingUsers =
          Map<String, String>.from(currentState.typingUsers);

      if (event.isTyping) {
        updatedTypingUsers[event.userId] = event.userName;
      } else {
        updatedTypingUsers.remove(event.userId);
      }

      emit(currentState.copyWith(typingUsers: updatedTypingUsers));
    }
  }

  void _onConnectionChanged(
    ChatConnectionChanged event,
    Emitter<ChatState> emit,
  ) {
    final currentState = state;
    if (currentState is ChatLoaded) {
      emit(currentState.copyWith(isConnected: event.isConnected));
    }
  }

  void _onErrorOccurred(
    ChatErrorOccurred event,
    Emitter<ChatState> emit,
  ) {
    final now = DateTime.now();

    // Prevent duplicate error notifications within 5 seconds
    if (_lastError == event.error &&
        _lastErrorTime != null &&
        now.difference(_lastErrorTime!).inSeconds < 5) {
      return;
    }

    _lastError = event.error;
    _lastErrorTime = now;

    final currentState = state;
    if (currentState is ChatLoaded) {
      emit(currentState.copyWith(error: event.error));
    } else {
      emit(ChatError(event.error, spaceId: _currentSpaceId));
    }
  }

  void _onErrorCleared(
    ChatErrorCleared event,
    Emitter<ChatState> emit,
  ) {
    _lastError = null; // Reset last error when clearing
    _lastErrorTime = null; // Reset last error time when clearing
    final currentState = state;
    if (currentState is ChatLoaded) {
      emit(currentState.copyWith(clearError: true));
    }
  }

  Future<void> _onChatDisposed(
    ChatDisposed event,
    Emitter<ChatState> emit,
  ) async {
    if (_currentSpaceId != null) {
      await _connectionUseCase.leaveSpace(_currentSpaceId!);
    }

    _typingTimer?.cancel();
    _currentSpaceId = null;
  }

  @override
  Future<void> close() async {
    await _messageSubscription?.cancel();
    await _messageUpdateSubscription?.cancel();
    await _typingSubscription?.cancel();
    await _connectionSubscription?.cancel();
    await _errorSubscription?.cancel();
    _typingTimer?.cancel();

    if (_currentSpaceId != null) {
      await _connectionUseCase.leaveSpace(_currentSpaceId!);
    }

    return super.close();
  }
}
