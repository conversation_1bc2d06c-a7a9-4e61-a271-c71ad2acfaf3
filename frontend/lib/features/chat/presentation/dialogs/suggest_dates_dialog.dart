import 'package:flutter/material.dart';
import '../../data/models/event_model.dart';
import '../../../../core/theme/app_colors.dart';

class SuggestDatesDialog extends StatefulWidget {
  final EventModel event;
  final Function(List<DateTime> selectedDates) onSuggestDates;
  final List<DateTime>? initialSelectedDates;

  const SuggestDatesDialog({
    super.key,
    required this.event,
    required this.onSuggestDates,
    this.initialSelectedDates,
  });

  @override
  State<SuggestDatesDialog> createState() => _SuggestDatesDialogState();
}

class _SuggestDatesDialogState extends State<SuggestDatesDialog> {
  List<DateTime> _selectedDates = [];
  bool _isLoading = false;
  late List<DateTime> _originalSelectedDates; // 拖拽開始前的原始選擇
  DateTime _focusedDay = DateTime.now();
  DateTime? _rangeStart;
  DateTime? _rangeEnd;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    // Initialize with previously selected dates if provided
    if (widget.initialSelectedDates != null) {
      _selectedDates = List.from(widget.initialSelectedDates!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.85,
        minChildSize: 0.6,
        maxChildSize: 0.95,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              _buildDragHandle(),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Row(
                        children: [
                          const Icon(Icons.event_available, color: Colors.blue),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Suggest Alternative Dates',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // Event title
                      Text(
                        'For: ${widget.event.title}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                      const SizedBox(height: 16),

                      // Calendar
                      _buildCalendar(),

                      const SizedBox(height: 16),

                      // Selected dates display
                      if (_selectedDates.isNotEmpty) _buildSelectedDatesInfo(),

                      const SizedBox(height: 16),

                      // Action buttons
                      Row(
                        children: [
                          if (_selectedDates.isNotEmpty)
                            TextButton(
                              onPressed: () {
                                setState(() {
                                  _selectedDates.clear();
                                  _rangeStart = null;
                                  _rangeEnd = null;
                                });
                              },
                              child: const Text('Clear All'),
                            ),
                          const Spacer(),
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('Cancel'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: _isLoading || _selectedDates.isEmpty
                                ? null
                                : _handleSuggestDates,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                            child: _isLoading
                                ? const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2, color: Colors.white),
                                  )
                                : const Text('Confirm'),
                          ),
                        ],
                      ),
                      SizedBox(height: MediaQuery.of(context).padding.bottom),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDragHandle() {
    return Container(
      width: 40,
      height: 4,
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  void _handleSuggestDates() async {
    if (_selectedDates.isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      widget.onSuggestDates(_selectedDates);
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to suggest dates: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildCalendar() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: _buildSimpleCalendar(),
    );
  }

  Widget _buildSimpleCalendar() {
    return Column(
      children: [
        _buildCalendarHeader(),
        _buildCalendarGrid(),
      ],
    );
  }

  Widget _buildCalendarHeader() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () {
              setState(() {
                _focusedDay = DateTime(_focusedDay.year, _focusedDay.month - 1);
              });
            },
            icon: const Icon(Icons.chevron_left),
          ),
          Text(
            '${_getMonthName(_focusedDay.month)} ${_focusedDay.year}',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _focusedDay = DateTime(_focusedDay.year, _focusedDay.month + 1);
              });
            },
            icon: const Icon(Icons.chevron_right),
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarGrid() {
    final firstDayOfMonth = DateTime(_focusedDay.year, _focusedDay.month, 1);
    final lastDayOfMonth = DateTime(_focusedDay.year, _focusedDay.month + 1, 0);
    final firstDayWeekday = firstDayOfMonth.weekday % 7;

    final days = <Widget>[];

    // Week day headers
    const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    for (final day in weekDays) {
      days.add(
        Container(
          height: 32,
          alignment: Alignment.center,
          child: Text(
            day,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade600,
              fontSize: 12,
            ),
          ),
        ),
      );
    }

    // Empty cells for days before month starts
    for (int i = 0; i < firstDayWeekday; i++) {
      days.add(const SizedBox(height: 32));
    }

    // Days of the month
    for (int day = 1; day <= lastDayOfMonth.day; day++) {
      final date = DateTime(_focusedDay.year, _focusedDay.month, day);
      final isSelected = _selectedDates.any((d) => _isSameDay(d, date));
      final isToday = _isSameDay(date, DateTime.now());
      final isPast =
          date.isBefore(DateTime.now().subtract(const Duration(days: 1)));

      days.add(
        _buildDayCell(date, day, isSelected, isToday, isPast),
      );
    }

    return Container(
      padding: const EdgeInsets.all(8),
      child: GridView.count(
        shrinkWrap: true,
        crossAxisCount: 7,
        children: days,
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months[month - 1];
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  Widget _buildDayCell(
      DateTime date, int day, bool isSelected, bool isToday, bool isPast) {
    return Draggable<DateTime>(
      data: date,
      feedback: const SizedBox.shrink(),
      childWhenDragging: null,
      onDragStarted: isPast ? null : () => _onDragStarted(date),
      onDragEnd: isPast ? null : (details) => _onDragEnd(),
      child: DragTarget<DateTime>(
        onWillAcceptWithDetails: (details) => !isPast && details.data != date,
        onAcceptWithDetails: (details) => _onDragAccept(details.data, date),
        onMove: (details) => _onDragMove(details.data, date),
        builder: (context, candidateData, rejectedData) {
          final isHighlighted = candidateData.isNotEmpty;
          final isInDragRange = _isDragging && _isDateInDragRange(date);
          return GestureDetector(
            onTap: isPast ? null : () => _onDayTapped(date),
            child: Container(
              height: 32,
              margin: const EdgeInsets.all(1),
              decoration: BoxDecoration(
                color: isSelected
                    ? Colors.blue
                    : isInDragRange
                        ? Colors.blue.withValues(alpha: 0.7)
                        : isHighlighted
                            ? Colors.blue.withValues(alpha: 0.3)
                            : isToday
                                ? Colors.blue.shade100
                                : null,
                borderRadius: BorderRadius.circular(4),
                border: isToday && !isSelected && !isInDragRange
                    ? Border.all(color: Colors.blue)
                    : isHighlighted
                        ? Border.all(color: Colors.blue, width: 2)
                        : null,
              ),
              alignment: Alignment.center,
              child: Text(
                day.toString(),
                style: TextStyle(
                  color: isPast
                      ? Colors.grey.shade400
                      : isSelected || isInDragRange
                          ? Colors.white
                          : isHighlighted
                              ? Colors.blue
                              : isToday
                                  ? Colors.blue
                                  : Colors.black,
                  fontWeight:
                      isSelected || isToday || isHighlighted || isInDragRange
                          ? FontWeight.w600
                          : FontWeight.normal,
                  fontSize: 12,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSelectedDatesInfo() {
    final sortedDates = List<DateTime>.from(_selectedDates)..sort();
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected: ${_selectedDates.length} date${_selectedDates.length == 1 ? '' : 's'}',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 12,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            _formatSelectedDates(sortedDates),
            style: const TextStyle(
              fontSize: 11,
              color: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  void _onDayTapped(DateTime date) {
    if (_isDragging) return;

    setState(() {
      if (_selectedDates.any((d) => _isSameDay(d, date))) {
        _selectedDates.removeWhere((d) => _isSameDay(d, date));
      } else {
        _selectedDates.add(date);
      }
    });
  }

  void _onDragStarted(DateTime date) {
    setState(() {
      _isDragging = true;
      _rangeStart = date;
      _rangeEnd = null;
      _originalSelectedDates = List.from(_selectedDates);

      if (!_selectedDates.any((d) => _isSameDay(d, date))) {
        _selectedDates.add(date);
      }
    });
  }

  void _onDragEnd() {
    setState(() {
      _isDragging = false;
      _rangeStart = null;
      _rangeEnd = null;
    });
  }

  void _onDragAccept(DateTime startDate, DateTime endDate) {
    _onDragMove(startDate, endDate);
  }

  void _onDragMove(DateTime startDate, DateTime endDate) {
    if (!_isDragging) return;

    setState(() {
      _rangeEnd = endDate;
      _selectedDates.clear();
      _selectedDates.addAll(_originalSelectedDates);

      final start = startDate.isBefore(endDate) ? startDate : endDate;
      final end = startDate.isBefore(endDate) ? endDate : startDate;

      for (DateTime date = start;
          date.isBefore(end.add(const Duration(days: 1)));
          date = date.add(const Duration(days: 1))) {
        if (!_selectedDates.any((d) => _isSameDay(d, date))) {
          _selectedDates.add(date);
        }
      }
    });
  }

  bool _isDateInDragRange(DateTime date) {
    if (_rangeStart == null) return false;

    final start = _rangeStart!;
    final end = _rangeEnd ?? start;

    final rangeStart = start.isBefore(end) ? start : end;
    final rangeEnd = start.isBefore(end) ? end : start;

    return !date.isBefore(rangeStart) && !date.isAfter(rangeEnd);
  }

  String _formatSelectedDates(List<DateTime> dates) {
    if (dates.isEmpty) return '';

    final dateRanges = _groupConsecutiveDatesInDialog(dates);
    final formattedRanges = <String>[];

    for (final range in dateRanges) {
      if (range.length == 1) {
        // Single date
        formattedRanges.add('${range.first.day}/${range.first.month}');
      } else if (range.length == 2) {
        // Two consecutive dates
        formattedRanges.add(
            '${range.first.day}/${range.first.month}, ${range.last.day}/${range.last.month}');
      } else {
        // Range of 3 or more dates
        formattedRanges.add(
            '${range.first.day}/${range.first.month}-${range.last.day}/${range.last.month}');
      }
    }

    return formattedRanges.join(', ');
  }

  List<List<DateTime>> _groupConsecutiveDatesInDialog(
      List<DateTime> sortedDates) {
    if (sortedDates.isEmpty) return [];

    final groups = <List<DateTime>>[];
    List<DateTime> currentGroup = [sortedDates.first];

    for (int i = 1; i < sortedDates.length; i++) {
      final current = sortedDates[i];
      final previous = sortedDates[i - 1];

      // Check if current date is consecutive to previous date
      if (current.difference(previous).inDays == 1) {
        currentGroup.add(current);
      } else {
        // Start a new group
        groups.add(currentGroup);
        currentGroup = [current];
      }
    }

    // Add the last group
    groups.add(currentGroup);
    return groups;
  }
}
