import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/tag_selector.dart';
import '../../data/models/event_model.dart';
import '../../data/models/linkable_item_model.dart';
import '../widgets/linkable_item_selector.dart';

class EditEventDialog extends StatefulWidget {
  final String spaceId;
  final EventModel event;
  final Function(UpdateEventRequest) onUpdateEvent;

  const EditEventDialog({
    super.key,
    required this.spaceId,
    required this.event,
    required this.onUpdateEvent,
  });

  @override
  State<EditEventDialog> createState() => _EditEventDialogState();
}

class _EditEventDialogState extends State<EditEventDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();

  // Time selection
  EventTimeMode _timeMode = EventTimeMode.specificTime;
  TimeOfDay? _selectedTime;
  TimeOfDay? _selectedEndTime;
  TimeOfDayPeriod? _selectedTimeOfDay;

  // Linkable items
  LinkableItem? _selectedLinkableItem;

  // Tags
  List<String> _selectedTags = [];

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    // Initialize form fields with existing event data
    _titleController.text = widget.event.title;
    _descriptionController.text = widget.event.description ?? '';
    _locationController.text = widget.event.location ?? '';
    _selectedTags = List.from(widget.event.tags);

    // Initialize time selection based on existing eventTime
    if (widget.event.eventTime != null) {
      _parseExistingTime(widget.event.eventTime!);
    }

    // Initialize linked items
    if (widget.event.linkedTodoId != null) {
      _selectedLinkableItem = LinkableItem(
        id: widget.event.linkedTodoId!,
        title: 'Linked Todo', // We'll need to fetch the actual title
        type: LinkableItemType.todo,
      );
    } else if (widget.event.linkedToGoId != null) {
      _selectedLinkableItem = LinkableItem(
        id: widget.event.linkedToGoId!,
        title: 'Linked ToGo', // We'll need to fetch the actual title
        type: LinkableItemType.togo,
      );
    }
  }

  void _parseExistingTime(String eventTime) {
    if (eventTime == 'full-day') {
      _timeMode = EventTimeMode.fullDay;
    } else if (eventTime.contains('-')) {
      // Time range format: "14:00-16:00"
      _timeMode = EventTimeMode.timeRange;
      final parts = eventTime.split('-');
      if (parts.length == 2) {
        _selectedTime = _parseTimeString(parts[0]);
        _selectedEndTime = _parseTimeString(parts[1]);
      }
    } else if (['morning', 'afternoon', 'evening', 'midnight']
        .contains(eventTime)) {
      // Time of day period
      _timeMode = EventTimeMode.timeOfDay;
      switch (eventTime) {
        case 'morning':
          _selectedTimeOfDay = TimeOfDayPeriod.morning;
          break;
        case 'afternoon':
          _selectedTimeOfDay = TimeOfDayPeriod.afternoon;
          break;
        case 'evening':
          _selectedTimeOfDay = TimeOfDayPeriod.evening;
          break;
        case 'midnight':
          _selectedTimeOfDay = TimeOfDayPeriod.midnight;
          break;
      }
    } else {
      // Specific time format: "14:30"
      _timeMode = EventTimeMode.specificTime;
      _selectedTime = _parseTimeString(eventTime);
    }
  }

  TimeOfDay? _parseTimeString(String timeString) {
    try {
      final parts = timeString.split(':');
      if (parts.length == 2) {
        final hour = int.parse(parts[0]);
        final minute = int.parse(parts[1]);
        return TimeOfDay(hour: hour, minute: minute);
      }
    } catch (e) {
      // Ignore parsing errors
    }
    return null;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.9,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Drag handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    const Text(
                      'Edit Event',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(color: AppColors.textSecondary),
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _updateEvent,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Update'),
                    ),
                  ],
                ),
              ),

              const Divider(height: 1),

              // Form content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.all(20),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildTitleField(),
                        const SizedBox(height: 16),
                        _buildDescriptionField(),
                        const SizedBox(height: 16),
                        _buildTimeSection(),
                        const SizedBox(height: 16),
                        _buildLocationField(),
                        const SizedBox(height: 16),
                        _buildLinkableItemSection(),
                        const SizedBox(height: 16),
                        _buildTagsSection(),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTitleField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Event Title',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _titleController,
          decoration: const InputDecoration(
            hintText: 'Enter event title',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter an event title';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildDescriptionField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Description (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _descriptionController,
          maxLines: 3,
          decoration: const InputDecoration(
            hintText: 'Add event description...',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Time (Optional)',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            if (_hasTimeSelection())
              TextButton.icon(
                onPressed: _clearTime,
                icon: const Icon(Icons.clear, size: 16),
                label: const Text('Clear'),
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.textSecondary,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        _buildTimeModeSelector(),
        const SizedBox(height: 8),
        _buildTimeInputs(),
      ],
    );
  }

  Widget _buildLocationField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Location (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _locationController,
          decoration: const InputDecoration(
            hintText: 'Add location...',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
      ],
    );
  }

  Widget _buildLinkableItemSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Link to Todo/ToGo (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        LinkableItemSelector(
          spaceId: widget.spaceId,
          selectedItem: _selectedLinkableItem,
          onItemSelected: (item) =>
              setState(() => _selectedLinkableItem = item),
        ),
      ],
    );
  }

  Widget _buildTagsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Tags (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        TagSelector(
          spaceId: widget.spaceId,
          selectedTags: _selectedTags,
          onTagsChanged: (tags) {
            setState(() {
              _selectedTags = tags;
            });
          },
          hintText: 'Add tags for this event...',
          allowCustomTags: true,
          maxTags: 10,
        ),
      ],
    );
  }

  void _updateEvent() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate title - always required
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter an event title')),
      );
      return;
    }

    // Validate time range if time range mode is selected
    if (_timeMode == EventTimeMode.timeRange) {
      if (_selectedTime == null || _selectedEndTime == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text(
                  'Please select both start and end times for time range')),
        );
        return;
      }
    }

    // Format time string based on selected mode
    String? eventTimeString;
    switch (_timeMode) {
      case EventTimeMode.specificTime:
        if (_selectedTime != null) {
          eventTimeString =
              '${_selectedTime!.hour.toString().padLeft(2, '0')}:${_selectedTime!.minute.toString().padLeft(2, '0')}';
        }
        break;
      case EventTimeMode.timeRange:
        if (_selectedTime != null && _selectedEndTime != null) {
          final startTime =
              '${_selectedTime!.hour.toString().padLeft(2, '0')}:${_selectedTime!.minute.toString().padLeft(2, '0')}';
          final endTime =
              '${_selectedEndTime!.hour.toString().padLeft(2, '0')}:${_selectedEndTime!.minute.toString().padLeft(2, '0')}';
          eventTimeString = '$startTime-$endTime';
        }
        break;
      case EventTimeMode.fullDay:
        eventTimeString = 'full-day';
        break;
      case EventTimeMode.timeOfDay:
        if (_selectedTimeOfDay != null) {
          eventTimeString = _selectedTimeOfDay!.name;
        }
        break;
    }

    final updateRequest = UpdateEventRequest(
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      eventTime: eventTimeString,
      location: _locationController.text.trim().isEmpty
          ? null
          : _locationController.text.trim(),
      linkedTodoId: _selectedLinkableItem?.type == LinkableItemType.todo
          ? _selectedLinkableItem?.id
          : null,
      linkedToGoId: _selectedLinkableItem?.type == LinkableItemType.togo
          ? _selectedLinkableItem?.id
          : null,
      tags: _selectedTags.isNotEmpty ? _selectedTags : null,
    );

    widget.onUpdateEvent(updateRequest);
    Navigator.pop(context);
  }

  bool _hasTimeSelection() {
    return _selectedTime != null ||
        _selectedEndTime != null ||
        _selectedTimeOfDay != null ||
        _timeMode == EventTimeMode.fullDay;
  }

  void _clearTime() {
    setState(() {
      _selectedTime = null;
      _selectedEndTime = null;
      _selectedTimeOfDay = null;
      _timeMode = EventTimeMode.specificTime; // Reset to default mode
    });
  }

  Widget _buildTimeModeSelector() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildTimeModeButton(
                mode: EventTimeMode.specificTime,
                label: 'Specific Time',
                icon: Icons.schedule,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTimeModeButton(
                mode: EventTimeMode.timeRange,
                label: 'Time Range',
                icon: Icons.schedule_outlined,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildTimeModeButton(
                mode: EventTimeMode.timeOfDay,
                label: 'Time of Day',
                icon: Icons.wb_sunny_outlined,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildTimeModeButton(
                mode: EventTimeMode.fullDay,
                label: 'Full Day',
                icon: Icons.all_inclusive,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTimeModeButton({
    required EventTimeMode mode,
    required String label,
    required IconData icon,
  }) {
    final isSelected = _timeMode == mode;
    return GestureDetector(
      onTap: () {
        setState(() {
          _timeMode = mode;
          // Clear time selections when mode changes
          if (mode == EventTimeMode.fullDay) {
            _selectedTime = null;
            _selectedEndTime = null;
            _selectedTimeOfDay = null;
          } else if (mode == EventTimeMode.specificTime) {
            _selectedEndTime = null;
            _selectedTimeOfDay = null;
          } else if (mode == EventTimeMode.timeRange) {
            _selectedTimeOfDay = null;
          } else if (mode == EventTimeMode.timeOfDay) {
            _selectedTime = null;
            _selectedEndTime = null;
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primary.withValues(alpha: 0.1)
              : Colors.grey[100],
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? AppColors.primary : AppColors.textSecondary,
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeInputs() {
    if (_timeMode == EventTimeMode.fullDay) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
        ),
        child: const Row(
          children: [
            Icon(Icons.all_inclusive, color: AppColors.primary, size: 20),
            SizedBox(width: 8),
            Text(
              'This event will last the entire day',
              style: TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    if (_timeMode == EventTimeMode.timeOfDay) {
      return _buildTimeOfDaySelector();
    }

    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _selectStartTime,
            icon: const Icon(Icons.access_time, size: 18),
            label: Text(
              _selectedTime != null
                  ? '${_selectedTime!.hour.toString().padLeft(2, '0')}:${_selectedTime!.minute.toString().padLeft(2, '0')}'
                  : _timeMode == EventTimeMode.timeRange
                      ? 'Start Time'
                      : 'Select Time',
            ),
          ),
        ),
        if (_timeMode == EventTimeMode.timeRange) ...[
          const SizedBox(width: 8),
          const Text('-',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
          const SizedBox(width: 8),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _selectedTime != null ? _selectEndTime : null,
              icon: const Icon(Icons.access_time_filled, size: 18),
              label: Text(
                _selectedEndTime != null
                    ? '${_selectedEndTime!.hour.toString().padLeft(2, '0')}:${_selectedEndTime!.minute.toString().padLeft(2, '0')}'
                    : 'End Time',
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTimeOfDaySelector() {
    return Row(
      children: [
        Expanded(
          child: _buildTimeOfDayButton(
            period: TimeOfDayPeriod.morning,
            label: 'Morning',
            icon: Icons.wb_sunny,
          ),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: _buildTimeOfDayButton(
            period: TimeOfDayPeriod.afternoon,
            label: 'Afternoon',
            icon: Icons.wb_sunny_outlined,
          ),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: _buildTimeOfDayButton(
            period: TimeOfDayPeriod.evening,
            label: 'Evening',
            icon: Icons.nights_stay_outlined,
          ),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: _buildTimeOfDayButton(
            period: TimeOfDayPeriod.midnight,
            label: 'Midnight',
            icon: Icons.bedtime,
          ),
        ),
      ],
    );
  }

  Widget _buildTimeOfDayButton({
    required TimeOfDayPeriod period,
    required String label,
    required IconData icon,
  }) {
    final isSelected = _selectedTimeOfDay == period;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTimeOfDay = period;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primary.withValues(alpha: 0.1)
              : Colors.grey[100],
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? AppColors.primary : AppColors.textSecondary,
              size: 16,
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectStartTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _selectedTime ?? TimeOfDay.now(),
    );
    if (time != null) {
      setState(() {
        _selectedTime = time;
        // Clear end time if it's before start time
        if (_selectedEndTime != null && _timeMode == EventTimeMode.timeRange) {
          final startMinutes = time.hour * 60 + time.minute;
          final endMinutes =
              _selectedEndTime!.hour * 60 + _selectedEndTime!.minute;
          if (endMinutes <= startMinutes) {
            _selectedEndTime = null;
          }
        }
      });
    }
  }

  Future<void> _selectEndTime() async {
    if (_selectedTime == null) return;

    final startMinutes = _selectedTime!.hour * 60 + _selectedTime!.minute;
    final initialEndTime = _selectedEndTime ??
        TimeOfDay(
          hour: (_selectedTime!.hour + 1) % 24,
          minute: _selectedTime!.minute,
        );

    final time = await showTimePicker(
      context: context,
      initialTime: initialEndTime,
    );

    if (time != null) {
      final endMinutes = time.hour * 60 + time.minute;
      if (endMinutes > startMinutes) {
        setState(() => _selectedEndTime = time);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('End time must be after start time'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
