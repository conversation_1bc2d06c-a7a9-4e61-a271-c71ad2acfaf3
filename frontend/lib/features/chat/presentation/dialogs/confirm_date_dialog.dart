import 'package:flutter/material.dart';
import '../../data/models/event_model.dart';

class ConfirmDateDialog extends StatefulWidget {
  final EventModel event;
  final Function(DateTime confirmedDate, String? confirmedTime) onConfirmDate;

  const ConfirmDateDialog({
    super.key,
    required this.event,
    required this.onConfirmDate,
  });

  @override
  State<ConfirmDateDialog> createState() => _ConfirmDateDialogState();
}

class _ConfirmDateDialogState extends State<ConfirmDateDialog> {
  DateTime? _selectedDate;
  String? _selectedTime;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.8,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              _buildDragHandle(),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Row(
                        children: [
                          const Icon(Icons.check_circle, color: Colors.green),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Confirm Event Date',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Select the final date and time for "${widget.event.title}"',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                      const SizedBox(height: 24),

                      // Available dates section
                      Text(
                        'Available Dates:',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                      const SizedBox(height: 12),
                      _buildAvailableDates(),
                      const SizedBox(height: 24),

                      // Time selection section
                      if (_selectedDate != null) ...[
                        Text(
                          'Time (Optional):',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                        const SizedBox(height: 12),
                        _buildTimeSelection(),
                        const SizedBox(height: 24),
                      ],

                      // Action buttons
                      Row(
                        children: [
                          const Spacer(),
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('Cancel'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: _isLoading || _selectedDate == null
                                ? null
                                : _handleConfirmDate,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                            child: _isLoading
                                ? const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2, color: Colors.white),
                                  )
                                : const Text('Confirm Date'),
                          ),
                        ],
                      ),
                      SizedBox(height: MediaQuery.of(context).padding.bottom),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDragHandle() {
    return Container(
      width: 40,
      height: 4,
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildAvailableDates() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: widget.event.eventDates.map((date) {
        final isSelected = _selectedDate != null && 
            _selectedDate!.year == date.year &&
            _selectedDate!.month == date.month &&
            _selectedDate!.day == date.day;
        
        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedDate = date;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected ? Colors.green : Colors.grey[100],
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected ? Colors.green : Colors.grey[300]!,
              ),
            ),
            child: Text(
              '${date.day}/${date.month}/${date.year}',
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.black87,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildTimeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Use suggested time if available
        if (widget.event.eventTime != null) ...[
          GestureDetector(
            onTap: () {
              setState(() {
                _selectedTime = widget.event.eventTime;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: _selectedTime == widget.event.eventTime 
                    ? Colors.green[50] 
                    : Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _selectedTime == widget.event.eventTime 
                      ? Colors.green 
                      : Colors.grey[300]!,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: _selectedTime == widget.event.eventTime 
                        ? Colors.green 
                        : Colors.grey[600],
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Suggested: ${widget.event.eventTime}',
                    style: TextStyle(
                      color: _selectedTime == widget.event.eventTime 
                          ? Colors.green[700] 
                          : Colors.grey[700],
                      fontWeight: _selectedTime == widget.event.eventTime 
                          ? FontWeight.w600 
                          : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 12),
        ],
        
        // Custom time input
        TextFormField(
          decoration: const InputDecoration(
            labelText: 'Custom Time (HH:MM)',
            hintText: '14:30',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.schedule),
          ),
          onChanged: (value) {
            setState(() {
              _selectedTime = value.isNotEmpty ? value : null;
            });
          },
        ),
      ],
    );
  }

  void _handleConfirmDate() async {
    if (_selectedDate == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      widget.onConfirmDate(_selectedDate!, _selectedTime);
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to confirm date: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
