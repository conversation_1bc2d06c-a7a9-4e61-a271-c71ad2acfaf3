import 'package:flutter/material.dart';

import '../../domain/services/google_location_service.dart';
import '../../../../core/config/google_api_config.dart';

/// 位置服務測試頁面
/// 用於測試和調試 Google Location API 集成
class LocationTestPage extends StatefulWidget {
  const LocationTestPage({super.key});

  @override
  State<LocationTestPage> createState() => _LocationTestPageState();
}

class _LocationTestPageState extends State<LocationTestPage> {
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = false;
  String _statusMessage = '';
  List<String> _searchResults = [];

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在初始化 Google Location Service...';
    });

    try {
      await GoogleLocationService.initialize();
      setState(() {
        _statusMessage = 'Google Location Service 初始化完成';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Google Location Service 初始化失敗: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testSearch() async {
    final query = _searchController.text.trim();
    if (query.isEmpty) return;

    setState(() {
      _isLoading = true;
      _searchResults.clear();
      _statusMessage = 'Searching: $query';
    });

    try {
      final results = await GoogleLocationService.searchAddresses(query);
      setState(() {
        _searchResults =
            results.map((r) => '${r.name} - ${r.address}').toList();
        _statusMessage = 'Found ${results.length} results';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Search failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testGoogleApi() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在測試 Google API...';
    });

    try {
      final isConnected = await GoogleLocationService.testApiConnection();
      setState(() {
        _statusMessage = isConnected ? 'Google API 連接成功' : 'Google API 連接失敗';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Google API 測試錯誤: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showStatusReport() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.6,
          minChildSize: 0.4,
          maxChildSize: 0.8,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                // Drag handle
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                // Header
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                  child: Row(
                    children: [
                      const Icon(Icons.location_on, color: Colors.blue),
                      const SizedBox(width: 8),
                      const Text(
                        'Google Location Service Status',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                        iconSize: 20,
                      ),
                    ],
                  ),
                ),
                // Content
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildStatusItem(
                            'API Key 配置',
                            GoogleLocationService.isApiKeyConfigured
                                ? '✅ 已配置'
                                : '❌ 未配置'),
                        _buildStatusItem(
                            '服務初始化',
                            GoogleLocationService.isInitialized
                                ? '✅ 已初始化'
                                : '❌ 未初始化'),
                        _buildStatusItem('Google API 配置',
                            GoogleApiConfig.isConfigured ? '✅ 已配置' : '⚠️ 未配置'),
                        _buildStatusItem('報告時間', DateTime.now().toString()),
                      ],
                    ),
                  ),
                ),
                // Footer
                Container(
                  padding: const EdgeInsets.all(20),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('Close'),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatusItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Location Service Test'),
        actions: [
          IconButton(
            onPressed: _showStatusReport,
            icon: const Icon(Icons.info_outline),
            tooltip: '狀態報告',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Google API 配置狀態
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Google API 配置',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(GoogleApiConfig.statusMessage),
                    if (!GoogleApiConfig.isConfigured) ...[
                      const SizedBox(height: 8),
                      const Text(
                        '要啟用 Google API，請查看 docs/GOOGLE_API_SETUP.md',
                        style: TextStyle(color: Colors.orange),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 測試按鈕
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _initializeServices,
                    child: const Text('Reinitialize'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testGoogleApi,
                    child: const Text('Test Google API'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Search Test
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Address Search Test',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _searchController,
                      decoration: const InputDecoration(
                        labelText: 'Enter Address',
                        hintText: 'e.g.: Central Plaza, Langham Place',
                        border: OutlineInputBorder(),
                      ),
                      onSubmitted: (_) => _testSearch(),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _testSearch,
                      child: const Text('Search'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 狀態信息
            if (_statusMessage.isNotEmpty)
              Card(
                color: Colors.blue[50],
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(_statusMessage),
                ),
              ),

            const SizedBox(height: 16),

            // 搜索結果
            if (_searchResults.isNotEmpty)
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '搜索結果',
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Expanded(
                          child: ListView.builder(
                            itemCount: _searchResults.length,
                            itemBuilder: (context, index) {
                              return ListTile(
                                leading: const Icon(Icons.location_on),
                                title: Text(_searchResults[index]),
                                dense: true,
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

            // 加載指示器
            if (_isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
