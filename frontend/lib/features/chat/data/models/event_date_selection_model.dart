class SelectEventDatesRequest {
  final List<String> dates;

  SelectEventDatesRequest({
    required this.dates,
  });

  Map<String, dynamic> toJson() {
    return {
      'dates': dates,
    };
  }
}

class DeselectEventDatesRequest {
  final List<String> dates;

  DeselectEventDatesRequest({
    required this.dates,
  });

  Map<String, dynamic> toJson() {
    return {
      'dates': dates,
    };
  }
}

class EventDateSelectionResponse {
  final List<String> selectedDates;
  final Map<String, List<String>> allSelections;

  EventDateSelectionResponse({
    required this.selectedDates,
    required this.allSelections,
  });

  factory EventDateSelectionResponse.fromJson(Map<String, dynamic> json) {
    return EventDateSelectionResponse(
      selectedDates: List<String>.from(json['selectedDates'] ?? []),
      allSelections: Map<String, List<String>>.from(
        (json['allSelections'] as Map<String, dynamic>? ?? {}).map(
          (key, value) => MapEntry(key, List<String>.from(value ?? [])),
        ),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'selectedDates': selectedDates,
      'allSelections': allSelections,
    };
  }
}

class EventDateSelectionSummary {
  final Map<String, DateSelectionInfo> summary;

  EventDateSelectionSummary({
    required this.summary,
  });

  factory EventDateSelectionSummary.fromJson(Map<String, dynamic> json) {
    final summaryData = json['summary'] as Map<String, dynamic>? ?? {};

    return EventDateSelectionSummary(
      summary: summaryData.map(
        (key, value) => MapEntry(
          key,
          DateSelectionInfo.fromJson(value as Map<String, dynamic>),
        ),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'summary': summary.map(
        (key, value) => MapEntry(key, value.toJson()),
      ),
    };
  }
}

class DateSelectionInfo {
  final List<String> userIds;
  final int count;

  DateSelectionInfo({
    required this.userIds,
    required this.count,
  });

  factory DateSelectionInfo.fromJson(Map<String, dynamic> json) {
    return DateSelectionInfo(
      userIds: List<String>.from(json['userIds'] ?? []),
      count: json['count'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userIds': userIds,
      'count': count,
    };
  }
}
