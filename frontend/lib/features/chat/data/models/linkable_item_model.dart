enum LinkableItemType {
  todo,
  togo,
}

class LinkableItem {
  final String id;
  final String title;
  final String? description;
  final LinkableItemType type;
  final String? location;
  final DateTime? dueDate;
  final String? status;

  LinkableItem({
    required this.id,
    required this.title,
    this.description,
    required this.type,
    this.location,
    this.dueDate,
    this.status,
  });

  factory LinkableItem.fromTodo(Map<String, dynamic> todoJson) {
    return LinkableItem(
      id: todo<PERSON>son['id'],
      title: todo<PERSON><PERSON>['title'],
      description: todo<PERSON>son['details'],
      type: LinkableItemType.todo,
      location: todoJson['location'],
      dueDate: todoJson['dueDate'] != null
          ? DateTime.parse(todoJson['dueDate'])
          : null,
      status: todoJson['status'],
    );
  }

  factory LinkableItem.fromToGo(Map<String, dynamic> togoJson) {
    // Use address if available, otherwise construct from coordinates
    String? location;
    if (togo<PERSON>son['address'] != null &&
        togoJson['address'].toString().isNotEmpty) {
      location = togoJson['address'];
    } else if (togoJson['latitude'] != null && togoJson['longitude'] != null) {
      location = '${togoJson['latitude']}, ${togoJson['longitude']}';
    }

    return LinkableItem(
      id: togoJson['id'],
      title: togoJson['name'],
      description: togoJson['description'],
      type: LinkableItemType.togo,
      location: location,
      dueDate: null,
      status: null,
    );
  }

  String get displayTitle {
    // Use text representations that match the app bar icons
    // Icons.checklist ≈ ☑ (ballot box with check)
    // Icons.place ≈ 📍 (round pushpin)
    String icon = type == LinkableItemType.todo ? '☑' : '📍';
    return '$icon $title';
  }

  String get displaySubtitle {
    // Only show location for both Todo and ToGo items
    // Use a different icon for location to avoid confusion with ToGo icon
    if (location != null && location!.isNotEmpty) {
      return '🏠 $location';
    }
    return '';
  }

  String get typeDisplayName {
    switch (type) {
      case LinkableItemType.todo:
        return 'Todo';
      case LinkableItemType.togo:
        return 'ToGo';
    }
  }
}
