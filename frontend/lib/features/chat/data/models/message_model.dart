import '../../../../core/services/time_service.dart';

enum MessageType {
  text,
  image,
  file,
  location,
  poll,
  dice,
  luckyDraw,
  split,
  settle,
  todo,
  togo,
  event,
  system,
}

enum MessageStatus {
  sending,
  sent,
  delivered,
  read,
  failed,
}

class MessageModel {
  final String id;
  final String spaceId;
  final String senderId;
  final String senderName;
  final String? senderAvatar;
  final MessageType type;
  final String content;
  final Map<String, dynamic>? metadata;
  final String? replyToId;
  final MessageModel? replyTo;
  final DateTime? editedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final MessageStatus status;
  final bool isDeleted;

  MessageModel({
    required this.id,
    required this.spaceId,
    required this.senderId,
    required this.senderName,
    this.senderAvatar,
    required this.type,
    required this.content,
    this.metadata,
    this.replyToId,
    this.replyTo,
    this.editedAt,
    required this.createdAt,
    required this.updatedAt,
    this.status = MessageStatus.sent,
    this.isDeleted = false,
  });

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    return MessageModel(
      id: json['id'],
      spaceId: json['spaceId'],
      senderId: json['senderId'] ?? json['sender']?['id'],
      senderName:
          json['senderName'] ?? json['sender']?['displayName'] ?? 'Unknown',
      senderAvatar: json['senderAvatar'] ?? json['sender']?['avatarUrl'],
      type: _parseMessageType(json['type']),
      content: json['content'] ?? '',
      metadata: json['metadata'] != null
          ? (json['metadata'] is Map
              ? Map<String, dynamic>.from(json['metadata'] as Map)
              : null)
          : null,
      replyToId: json['replyToId'],
      replyTo: json['replyTo'] != null
          ? MessageModel.fromJson(json['replyTo'])
          : null,
      editedAt: json['editedAt'] != null
          ? TimeService.instance.parseDateTime(json['editedAt'])
          : null,
      createdAt: TimeService.instance.parseDateTime(json['createdAt']),
      updatedAt: TimeService.instance
          .parseDateTime(json['updatedAt'] ?? json['createdAt']),
      status: _parseMessageStatus(json['status']),
      isDeleted: json['isDeleted'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'spaceId': spaceId,
      'senderId': senderId,
      'senderName': senderName,
      'senderAvatar': senderAvatar,
      'type': type.name,
      'content': content,
      'metadata': metadata,
      'replyToId': replyToId,
      'replyTo': replyTo?.toJson(),
      'editedAt': editedAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isDeleted': isDeleted,
    };
  }

  static MessageType _parseMessageType(String? type) {
    switch (type?.toLowerCase()) {
      case 'text':
        return MessageType.text;
      case 'image':
        return MessageType.image;
      case 'file':
        return MessageType.file;
      case 'location':
        return MessageType.location;
      case 'poll':
        return MessageType.poll;
      case 'dice':
        return MessageType.dice;
      case 'luckydraw':
        return MessageType.luckyDraw;
      case 'split':
        return MessageType.split;
      case 'settle':
        return MessageType.settle;
      case 'todo':
        return MessageType.todo;
      case 'togo':
        return MessageType.togo;
      case 'event':
        return MessageType.event;
      case 'system':
        return MessageType.system;
      default:
        return MessageType.text;
    }
  }

  static MessageStatus _parseMessageStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'sending':
        return MessageStatus.sending;
      case 'sent':
        return MessageStatus.sent;
      case 'delivered':
        return MessageStatus.delivered;
      case 'read':
        return MessageStatus.read;
      case 'failed':
        return MessageStatus.failed;
      default:
        return MessageStatus.sent;
    }
  }

  bool get isEdited => editedAt != null;

  MessageModel copyWith({
    String? id,
    String? spaceId,
    String? senderId,
    String? senderName,
    String? senderAvatar,
    MessageType? type,
    String? content,
    Map<String, dynamic>? metadata,
    String? replyToId,
    MessageModel? replyTo,
    DateTime? editedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    MessageStatus? status,
    bool? isDeleted,
  }) {
    return MessageModel(
      id: id ?? this.id,
      spaceId: spaceId ?? this.spaceId,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderAvatar: senderAvatar ?? this.senderAvatar,
      type: type ?? this.type,
      content: content ?? this.content,
      metadata: metadata ?? this.metadata,
      replyToId: replyToId ?? this.replyToId,
      replyTo: replyTo ?? this.replyTo,
      editedAt: editedAt ?? this.editedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }
}

class CreateMessageRequest {
  final String content;
  final MessageType type;
  final Map<String, dynamic>? metadata;
  final String? replyToId;

  CreateMessageRequest({
    required this.content,
    this.type = MessageType.text,
    this.metadata,
    this.replyToId,
  });

  Map<String, dynamic> toJson() {
    return {
      'content': content,
      'type': _messageTypeToString(type),
      if (metadata != null) 'metadata': metadata,
      if (replyToId != null) 'replyToId': replyToId,
    };
  }

  static String _messageTypeToString(MessageType type) {
    switch (type) {
      case MessageType.text:
        return 'text';
      case MessageType.image:
        return 'image';
      case MessageType.file:
        return 'file';
      case MessageType.location:
        return 'location';
      case MessageType.poll:
        return 'poll';
      case MessageType.dice:
        return 'dice';
      case MessageType.luckyDraw:
        return 'luckydraw';
      case MessageType.split:
        return 'split';
      case MessageType.settle:
        return 'settle';
      case MessageType.todo:
        return 'todo';
      case MessageType.togo:
        return 'togo';
      case MessageType.event:
        return 'event';
      case MessageType.system:
        return 'system';
    }
  }
}

class UpdateMessageRequest {
  final String content;

  UpdateMessageRequest({
    required this.content,
  });

  Map<String, dynamic> toJson() {
    return {
      'content': content,
    };
  }
}

class GetMessagesRequest {
  final int limit;
  final int offset;
  final DateTime? before;
  final DateTime? after;

  GetMessagesRequest({
    this.limit = 50,
    this.offset = 0,
    this.before,
    this.after,
  });

  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{
      'limit': limit.toString(),
      'offset': offset.toString(),
    };

    if (before != null) {
      params['before'] = before!.toIso8601String();
    }

    if (after != null) {
      params['after'] = after!.toIso8601String();
    }

    return params;
  }
}
