import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../../core/platform/platform_config.dart';
import '../../../auth/domain/services/auth_service.dart';
import '../models/event_date_selection_model.dart';

class EventDateSelectionService {
  final AuthService _authService;
  final PlatformConfig _platformConfig = PlatformConfig();

  EventDateSelectionService(this._authService);

  Future<EventDateSelectionResponse> selectEventDates({
    required String spaceId,
    required String eventId,
    required SelectEventDatesRequest request,
  }) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.post(
        Uri.parse('${_platformConfig.getApiBaseUrl()}/spaces/$spaceId/events/$eventId/date-selections/select'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          return EventDateSelectionResponse.fromJson(responseData['data'] as Map<String, dynamic>);
        } else {
          throw Exception(responseData['message'] ?? 'Failed to select event dates');
        }
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to select event dates');
      }
    } catch (e) {
      throw Exception('Failed to select event dates: $e');
    }
  }

  Future<EventDateSelectionResponse> deselectEventDates({
    required String spaceId,
    required String eventId,
    required DeselectEventDatesRequest request,
  }) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.post(
        Uri.parse('${_platformConfig.getApiBaseUrl()}/spaces/$spaceId/events/$eventId/date-selections/deselect'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          return EventDateSelectionResponse.fromJson(responseData['data'] as Map<String, dynamic>);
        } else {
          throw Exception(responseData['message'] ?? 'Failed to deselect event dates');
        }
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to deselect event dates');
      }
    } catch (e) {
      throw Exception('Failed to deselect event dates: $e');
    }
  }

  Future<EventDateSelectionResponse> getUserDateSelections({
    required String spaceId,
    required String eventId,
  }) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.get(
        Uri.parse('${_platformConfig.getApiBaseUrl()}/spaces/$spaceId/events/$eventId/date-selections'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          return EventDateSelectionResponse.fromJson(responseData['data'] as Map<String, dynamic>);
        } else {
          throw Exception(responseData['message'] ?? 'Failed to get user date selections');
        }
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to get user date selections');
      }
    } catch (e) {
      throw Exception('Failed to get user date selections: $e');
    }
  }

  Future<EventDateSelectionSummary> getEventDateSelectionSummary({
    required String spaceId,
    required String eventId,
  }) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.get(
        Uri.parse('${_platformConfig.getApiBaseUrl()}/spaces/$spaceId/events/$eventId/date-selections/summary'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          return EventDateSelectionSummary.fromJson(responseData['data'] as Map<String, dynamic>);
        } else {
          throw Exception(responseData['message'] ?? 'Failed to get event date selection summary');
        }
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to get event date selection summary');
      }
    } catch (e) {
      throw Exception('Failed to get event date selection summary: $e');
    }
  }
}
