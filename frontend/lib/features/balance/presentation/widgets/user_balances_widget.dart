import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../data/models/balance_model.dart';
import '../../../auth/domain/services/auth_service.dart';

class UserBalancesWidget extends StatelessWidget {
  final List<UserBalance> userBalances;
  final Function(String fromUserId, String toUserId)? onSettleDebts;

  const UserBalancesWidget({
    super.key,
    required this.userBalances,
    this.onSettleDebts,
  });

  @override
  Widget build(BuildContext context) {
    // Filter out current user and sort remaining balances
    final filteredAndSortedBalances = _filterAndSortUserBalances(userBalances);

    if (filteredAndSortedBalances.isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      children: [
        ...filteredAndSortedBalances
            .map((userBalance) => _buildUserBalanceCard(context, userBalance)),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.people,
              size: 48,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'All Settled Up!',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'No outstanding balances with other members.\nAll debts are settled!',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
          ),
        ],
      ),
    );
  }

  bool _isCurrentUser(String userId) {
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserId = authService.currentUser?.id;
    return currentUserId == userId;
  }

  String _getUserDisplayName(UserBalance userBalance) {
    if (_isCurrentUser(userBalance.userId)) {
      return userBalance.userName;
    }
    return userBalance.userName;
  }

  List<UserBalance> _filterAndSortUserBalances(List<UserBalance> userBalances) {
    // Filter out current user - users don't owe themselves
    // Show all debt relationships, including overpayments (when settle > owed)

    final filteredList = userBalances.where((userBalance) {
      final isCurrentUser = _isCurrentUser(userBalance.userId);
      return !isCurrentUser && userBalance.netBalance != 0;
    }).toList();

    // Sort by name alphabetically
    filteredList.sort((a, b) => a.userName.compareTo(b.userName));

    return filteredList;
  }

  Widget _getDebtAmountWidget(UserBalance userBalance) {
    if (userBalance.currencyBalances.isEmpty) {
      return const Text(
        'Settled',
        style: TextStyle(
          color: Colors.grey,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      );
    }

    // Use first (and only) currency balance
    final currencyBalance = userBalance.currencyBalances.first;
    final amount = currencyBalance.netBalance;
    String text;
    Color color;

    if (amount == 0) {
      text = '${currencyBalance.currency}0.00';
      color = Colors.grey;
    } else if (amount > 0) {
      // Positive balance = you owe them = show as negative amount (red)
      text = '-${currencyBalance.currency}${amount.toStringAsFixed(2)}';
      color = Colors.red;
    } else {
      // Negative balance = they owe you = show as positive amount (green)
      text = '+${currencyBalance.currency}${amount.abs().toStringAsFixed(2)}';
      color = Colors.green;
    }

    return Text(
      text,
      style: TextStyle(
        color: color,
        fontWeight: FontWeight.bold,
        fontSize: 16,
      ),
    );
  }

  String _getDebtRelationshipLabel(UserBalance userBalance) {
    final amount = userBalance.netBalance.abs();

    if (amount == 0) {
      return 'All settled';
    }

    // Get current user info to add (me) identifier
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserName = authService.currentUser?.displayName ?? 'You';

    // Use current user's name with (me) instead of "you"
    // Handle all debt relationships including overpayments
    // 修复逻辑：根据后端计算方式
    // netBalance = 该用户支付给当前用户的总额 - 当前用户支付给该用户的总额
    // Positive netBalance = 该用户多付了 = 当前用户欠该用户
    // Negative netBalance = 该用户少付了 = 该用户欠当前用户
    if (userBalance.netBalance > 0) {
      // 该用户有正余额 = 当前用户欠该用户
      return '$currentUserName(me) owe ${userBalance.userName}';
    } else {
      // 该用户有负余额 = 该用户欠当前用户
      return '${userBalance.userName} owes $currentUserName(me)';
    }
  }

  Widget _buildUserBalanceCard(BuildContext context, UserBalance userBalance) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // User Info Row
            Row(
              children: [
                // Avatar with "You" badge
                Stack(
                  children: [
                    CircleAvatar(
                      radius: 24,
                      backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                      backgroundImage: userBalance.userAvatar != null
                          ? NetworkImage(userBalance.userAvatar!)
                          : null,
                      child: userBalance.userAvatar == null
                          ? Text(
                              userBalance.userName.isNotEmpty
                                  ? userBalance.userName[0].toUpperCase()
                                  : '?',
                              style: const TextStyle(
                                color: AppColors.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            )
                          : null,
                    ),
                    if (_isCurrentUser(userBalance.userId))
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.white,
                              width: 1.5,
                            ),
                          ),
                          child: const Text(
                            'You',
                            style: TextStyle(
                              fontSize: 8,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),

                const SizedBox(width: 12),

                // Name and Status
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getUserDisplayName(userBalance),
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        _getDebtRelationshipLabel(userBalance),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.textSecondary,
                            ),
                      ),
                    ],
                  ),
                ),

                // Net Balance
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    _getDebtAmountWidget(userBalance),
                  ],
                ),
              ],
            ),

            // Only show debt relationship, not payment details

            // Pending Payments
            if (userBalance.pendingPayments.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Divider(height: 1),
              const SizedBox(height: 8),
              _buildPendingPayments(context, userBalance),
            ],

            // Action Buttons (don't show settle button for current user)
            // Allow settle up even when "All settled" for new payments or adjustments
            if (!_isCurrentUser(userBalance.userId)) ...[
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: () => _showSettleDialog(context, userBalance),
                  icon: const Icon(Icons.account_balance, size: 16),
                  label: const Text('Settle Up'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.primary,
                    side: const BorderSide(
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPendingPayments(BuildContext context, UserBalance userBalance) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Pending Payments (${userBalance.pendingPayments.length})',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
        ),
        const SizedBox(height: 6),
        ...userBalance.pendingPayments.take(3).map((payment) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                Icon(
                  payment.fromUserId == userBalance.userId
                      ? Icons.call_made
                      : Icons.call_received,
                  size: 12,
                  color: payment.fromUserId == userBalance.userId
                      ? Colors.red.shade600
                      : Colors.green.shade600,
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    payment.fromUserId == userBalance.userId
                        ? 'Pay ${payment.toUserName}'
                        : 'Receive from ${payment.fromUserName}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
                Text(
                  '${payment.currency}${payment.amount.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ],
            ),
          );
        }),
        if (userBalance.pendingPayments.length > 3)
          Text(
            '... and ${userBalance.pendingPayments.length - 3} more',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
          ),
      ],
    );
  }

  void _showSettleDialog(BuildContext context, UserBalance userBalance) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.handshake, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Text(
                    'Settle with ${userBalance.userName}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  Text(
                    'Mark all debts between you and ${userBalance.userName} as settled?',
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  // Actions
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.pop(context),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);
                            final authService =
                                DependencyInjection.getIt<AuthService>();
                            final currentUserId =
                                authService.currentUser?.id ?? '';

                            if (currentUserId.isEmpty) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                      'Unable to identify current user. Please try logging in again.'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                              return;
                            }

                            if (onSettleDebts != null) {
                              onSettleDebts!(currentUserId, userBalance.userId);
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                      'Settling debts with ${userBalance.userName}...'),
                                  backgroundColor: Colors.blue,
                                ),
                              );
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content:
                                      Text('Settle function not available'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Settle'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
