import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../data/models/balance_model.dart';

class BalanceFilterBar extends StatelessWidget {
  final BalanceFilter currentFilter;
  final BalanceSort currentSort;
  final Function(BalanceFilter) onFilterChanged;
  final Function(BalanceSort) onSortChanged;

  const BalanceFilterBar({
    super.key,
    required this.currentFilter,
    required this.currentSort,
    required this.onFilterChanged,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          // Filter Dropdown
          Expanded(
            child: _buildFilterDropdown(context),
          ),

          const SizedBox(width: 12),

          // Sort Dropdown
          Expanded(
            child: _buildSortDropdown(context),
          ),

          const SizedBox(width: 12),

          // Search Button
          IconButton(
            onPressed: () => _showSearchDialog(context),
            icon: const Icon(Icons.search),
            tooltip: 'Search splits',
          ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<BalanceFilter>(
          value: currentFilter,
          isExpanded: true,
          icon: const Icon(
            Icons.filter_list,
            color: AppColors.primary,
            size: 20,
          ),
          items: BalanceFilter.values.map((filter) {
            return DropdownMenuItem(
              value: filter,
              child: Row(
                children: [
                  Icon(
                    _getFilterIcon(filter),
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getFilterLabel(filter),
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (filter) {
            if (filter != null) {
              onFilterChanged(filter);
            }
          },
        ),
      ),
    );
  }

  Widget _buildSortDropdown(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<BalanceSort>(
          value: currentSort,
          isExpanded: true,
          icon: const Icon(
            Icons.sort,
            color: AppColors.primary,
            size: 20,
          ),
          items: BalanceSort.values.map((sort) {
            return DropdownMenuItem(
              value: sort,
              child: Row(
                children: [
                  Icon(
                    _getSortIcon(sort),
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getSortLabel(sort),
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (sort) {
            if (sort != null) {
              onSortChanged(sort);
            }
          },
        ),
      ),
    );
  }

  IconData _getFilterIcon(BalanceFilter filter) {
    switch (filter) {
      case BalanceFilter.all:
        return Icons.list;
      case BalanceFilter.pending:
        return Icons.pending;
      case BalanceFilter.settled:
        return Icons.check_circle;
      case BalanceFilter.myExpenses:
        return Icons.account_circle;
      case BalanceFilter.owedToMe:
        return Icons.call_received;
      case BalanceFilter.iOwe:
        return Icons.call_made;
    }
  }

  String _getFilterLabel(BalanceFilter filter) {
    switch (filter) {
      case BalanceFilter.all:
        return 'All Splits';
      case BalanceFilter.pending:
        return 'Pending';
      case BalanceFilter.settled:
        return 'Settled';
      case BalanceFilter.myExpenses:
        return 'My Expenses';
      case BalanceFilter.owedToMe:
        return 'Owed to Me';
      case BalanceFilter.iOwe:
        return 'I Owe';
    }
  }

  IconData _getSortIcon(BalanceSort sort) {
    switch (sort) {
      case BalanceSort.dateNewest:
        return Icons.schedule;
      case BalanceSort.dateOldest:
        return Icons.history;
      case BalanceSort.amountHighest:
        return Icons.trending_up;
      case BalanceSort.amountLowest:
        return Icons.trending_down;
      case BalanceSort.alphabetical:
        return Icons.sort_by_alpha;
    }
  }

  String _getSortLabel(BalanceSort sort) {
    switch (sort) {
      case BalanceSort.dateNewest:
        return 'Newest First';
      case BalanceSort.dateOldest:
        return 'Oldest First';
      case BalanceSort.amountHighest:
        return 'Highest Amount';
      case BalanceSort.amountLowest:
        return 'Lowest Amount';
      case BalanceSort.alphabetical:
        return 'Alphabetical';
    }
  }

  void _showSearchDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.4,
          minChildSize: 0.3,
          maxChildSize: 0.6,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                // Drag handle
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                // Header
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                  child: Row(
                    children: [
                      const Icon(Icons.search, color: Colors.blue),
                      const SizedBox(width: 8),
                      const Text(
                        'Search Splits',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                        iconSize: 20,
                      ),
                    ],
                  ),
                ),
                // Content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      children: [
                        TextField(
                          autofocus: true,
                          decoration: const InputDecoration(
                            hintText: 'Enter split title or description...',
                            prefixIcon: Icon(Icons.search),
                            border: OutlineInputBorder(),
                          ),
                          onSubmitted: (query) {
                            Navigator.pop(context);
                            // TODO: Implement search functionality
                          },
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
                // Actions
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      top: BorderSide(color: Colors.grey[200]!),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);
                            // TODO: Implement search functionality
                          },
                          child: const Text('Search'),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
