import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/currency_constants.dart';
import '../../../auth/domain/services/auth_service.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../spaces/presentation/widgets/currency_picker_widget.dart';
import '../../data/models/balance_model.dart';

class PaymentOption {
  final int index;
  final String title;
  final String? fromUserId;
  final String? toUserId;
  final double? amount;

  PaymentOption({
    required this.index,
    required this.title,
    this.fromUserId,
    this.toUserId,
    this.amount,
  });

  bool get isCustomAmount => fromUserId == null || toUserId == null;
}

class RecordPaymentDialog extends StatefulWidget {
  final UserBalance userBalance;
  final String currentUserId;
  final String spaceCurrency;
  final Function(
          String fromUserId, String toUserId, double amount, String currency)
      onRecordPayment;

  const RecordPaymentDialog({
    super.key,
    required this.userBalance,
    required this.currentUserId,
    required this.spaceCurrency,
    required this.onRecordPayment,
  });

  @override
  State<RecordPaymentDialog> createState() => _RecordPaymentDialogState();
}

class _RecordPaymentDialogState extends State<RecordPaymentDialog> {
  int _selectedOption = 0;
  final TextEditingController _customAmountController = TextEditingController();
  String? _selectedFromUserId;
  String? _selectedToUserId;
  String _currency = CurrencyConstants.defaultCurrency;

  @override
  void initState() {
    super.initState();
    // Use space currency as default
    _currency = widget.spaceCurrency;
    // Initialize default selection based on debt relationship
    _initializeDefaultSelection();
  }

  void _initializeDefaultSelection() {
    // Always start with the first available option (debt settlement if exists, otherwise custom)
    _selectedOption = 0;
  }

  void _showCurrencyPicker() {
    CurrencyPickerBottomSheet.show(
      context,
      selectedCurrency: _currency,
    ).then((selectedCurrency) {
      if (selectedCurrency != null && mounted) {
        setState(() => _currency = selectedCurrency);
      }
    });
  }

  List<PaymentOption> get _availableOptions {
    final netBalance = widget.userBalance.netBalance;
    final options = <PaymentOption>[];

    if (netBalance > 0) {
      // Current user owes the other person
      options.add(PaymentOption(
        index: 0,
        title:
            '${_getCurrentUserName()}(me) paid $_otherUserName $_currency${_suggestedAmount.toStringAsFixed(2)}',
        fromUserId: widget.currentUserId,
        toUserId: widget.userBalance.userId,
        amount: _suggestedAmount,
      ));
    } else if (netBalance < 0) {
      // Other person owes the current user
      options.add(PaymentOption(
        index: 0,
        title:
            '$_otherUserName paid ${_getCurrentUserName()}(me) $_currency${_suggestedAmount.toStringAsFixed(2)}',
        fromUserId: widget.userBalance.userId,
        toUserId: widget.currentUserId,
        amount: _suggestedAmount,
      ));
    }

    // Always add custom amount option
    options.add(PaymentOption(
      index: options.length,
      title: 'Enter a custom amount...',
      fromUserId: null,
      toUserId: null,
      amount: null,
    ));

    return options;
  }

  double get _suggestedAmount {
    return widget.userBalance.netBalance.abs();
  }

  String get _otherUserName {
    return widget.userBalance.userName;
  }

  bool get _isCustomAmountSelected {
    final options = _availableOptions;
    if (_selectedOption >= options.length) return false;
    return options[_selectedOption].isCustomAmount;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              // Drag handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                child: Row(
                  children: [
                    const Icon(
                      Icons.account_balance_wallet,
                      color: AppColors.primary,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Settle Up with $_otherUserName',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      iconSize: 20,
                    ),
                  ],
                ),
              ),
              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    children: [
                      // Payment Options
                      ..._availableOptions.asMap().entries.map((entry) {
                        final option = entry.value;
                        return Padding(
                          padding: EdgeInsets.only(
                              bottom: entry.key < _availableOptions.length - 1
                                  ? 12
                                  : 0),
                          child: _buildPaymentOption(
                            index: option.index,
                            title: option.title,
                            isSelected: _selectedOption == option.index,
                            onTap: () =>
                                setState(() => _selectedOption = option.index),
                          ),
                        );
                      }),

                      // Custom Amount Section
                      if (_isCustomAmountSelected) ...[
                        const SizedBox(height: 16),
                        _buildCustomAmountSection(),
                      ],
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
              // Actions
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    top: BorderSide(color: Colors.grey[200]!),
                  ),
                ),
                child: SafeArea(
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _canSubmit() ? _handleSubmit : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Settle Up',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPaymentOption({
    required int index,
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
          color: isSelected
              ? AppColors.primary.withValues(alpha: 0.1)
              : Colors.white,
        ),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? AppColors.primary : Colors.grey.shade400,
                  width: 2,
                ),
                color: isSelected ? AppColors.primary : Colors.transparent,
              ),
              child: isSelected
                  ? const Icon(
                      Icons.check,
                      size: 12,
                      color: Colors.white,
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: isSelected
                          ? AppColors.primary
                          : AppColors.textPrimary,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomAmountSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey.shade50,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Amount Input
          Row(
            children: [
              // Currency selector
              GestureDetector(
                onTap: _showCurrencyPicker,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _currency,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(width: 4),
                      const Icon(Icons.arrow_drop_down, size: 16),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: TextField(
                  controller: _customAmountController,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                        RegExp(r'^\d*\.?\d{0,2}')),
                  ],
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                  decoration: const InputDecoration(
                    hintText: '0.00',
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                  ),
                  onChanged: (value) => setState(() {}),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // From/To Selection
          _buildUserSelector(
            label: 'From:',
            selectedUserId: _selectedFromUserId,
            onChanged: (userId) => setState(() => _selectedFromUserId = userId),
          ),

          const SizedBox(height: 12),

          _buildUserSelector(
            label: 'To:',
            selectedUserId: _selectedToUserId,
            onChanged: (userId) => setState(() => _selectedToUserId = userId),
          ),
        ],
      ),
    );
  }

  Widget _buildUserSelector({
    required String label,
    required String? selectedUserId,
    required Function(String?) onChanged,
  }) {
    return Row(
      children: [
        SizedBox(
          width: 50,
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () => _showUserSelectionDialog(onChanged),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
                color: Colors.white,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      _getUserDisplayName(selectedUserId) ?? 'Select User',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  const Icon(
                    Icons.arrow_drop_down,
                    color: Colors.grey,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  String? _getUserDisplayName(String? userId) {
    if (userId == widget.currentUserId) {
      return '${_getCurrentUserName()}(me)';
    } else if (userId == widget.userBalance.userId) {
      return widget.userBalance.userName;
    }
    return null;
  }

  void _showUserSelectionDialog(Function(String?) onChanged) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.person, color: Colors.blue),
                  const SizedBox(width: 8),
                  const Text(
                    'Select User',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  ListTile(
                    title: Text('${_getCurrentUserName()}(me)'),
                    onTap: () {
                      onChanged(widget.currentUserId);
                      Navigator.pop(context);
                    },
                  ),
                  ListTile(
                    title: Text(widget.userBalance.userName),
                    onTap: () {
                      onChanged(widget.userBalance.userId);
                      Navigator.pop(context);
                    },
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _canSubmit() {
    final options = _availableOptions;
    if (_selectedOption >= options.length) return false;

    final selectedOption = options[_selectedOption];

    if (!selectedOption.isCustomAmount) {
      return true; // Predefined amounts are always valid
    } else {
      // Custom amount validation
      final amount = double.tryParse(_customAmountController.text);
      return amount != null &&
          amount > 0 &&
          _selectedFromUserId != null &&
          _selectedToUserId != null &&
          _selectedFromUserId != _selectedToUserId;
    }
  }

  void _handleSubmit() {
    final options = _availableOptions;
    if (_selectedOption >= options.length) return;

    final selectedOption = options[_selectedOption];

    String fromUserId;
    String toUserId;
    double amount;

    if (!selectedOption.isCustomAmount) {
      // Predefined option
      fromUserId = selectedOption.fromUserId!;
      toUserId = selectedOption.toUserId!;
      amount = selectedOption.amount!;
    } else {
      // Custom amount
      fromUserId = _selectedFromUserId!;
      toUserId = _selectedToUserId!;
      amount = double.parse(_customAmountController.text);
    }

    Navigator.pop(context);
    widget.onRecordPayment(fromUserId, toUserId, amount, _currency);
  }

  @override
  void dispose() {
    _customAmountController.dispose();
    super.dispose();
  }

  // Get current user name for display
  String _getCurrentUserName() {
    final authService = DependencyInjection.getIt<AuthService>();
    return authService.currentUser?.displayName ?? 'You';
  }
}
