import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/loading_state_manager.dart';
import '../../../../core/di/dependency_injection.dart';

import '../bloc/balance_bloc.dart';
import '../widgets/balance_summary_card.dart';
import '../widgets/split_list_widget.dart';
import '../widgets/recent_settle_widget.dart';
import '../widgets/expense_categories_widget.dart';
import '../widgets/user_balances_widget.dart';
import '../widgets/record_payment_dialog.dart';
import '../widgets/edit_settle_dialog.dart';
import '../../data/models/balance_model.dart';
import 'split_detail_page.dart';
import '../../../chat/presentation/widgets/create_split_dialog.dart';
import '../../../chat/data/models/split_model.dart';
import '../../../chat/data/models/message_model.dart';
import '../../../chat/domain/repositories/chat_repository.dart';
import '../../../auth/domain/services/auth_service.dart';

class BalancePage extends StatefulWidget {
  final String spaceId;

  const BalancePage({
    super.key,
    required this.spaceId,
  });

  @override
  State<BalancePage> createState() => _BalancePageState();
}

class _BalancePageState extends State<BalancePage> {
  @override
  void initState() {
    super.initState();
    // Load balance data
    context.read<BalanceBloc>().add(BalanceLoadRequested(widget.spaceId));
  }

  Future<void> _onRefresh() async {
    // Trigger refresh of balance data
    context.read<BalanceBloc>().add(BalanceRefreshRequested(widget.spaceId));

    // Wait for the refresh to complete
    await Future.delayed(const Duration(milliseconds: 500));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.grey.shade50,
      child: BlocBuilder<BalanceBloc, BalanceState>(
        builder: (context, state) {
          if (state is BalanceLoading) {
            return const LoadingStateManager(
              type: LoadingStateType.spinner,
              isLoading: true,
              child: SizedBox.shrink(),
            );
          } else if (state is BalanceError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline,
                      size: 64, color: AppColors.error),
                  const SizedBox(height: 16),
                  Text(
                    'Error Loading Balance',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => context
                        .read<BalanceBloc>()
                        .add(BalanceLoadRequested(widget.spaceId)),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          } else if (state is BalanceLoaded) {
            return RefreshIndicator(
              onRefresh: () async {
                context
                    .read<BalanceBloc>()
                    .add(BalanceRefreshRequested(widget.spaceId));
              },
              child: _buildUnifiedBalanceView(state),
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildUnifiedBalanceView(BalanceLoaded state) {
    return RefreshIndicator(
      onRefresh: _onRefresh,
      color: AppColors.primary,
      backgroundColor: AppColors.surface,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Balance Summary Card
            BalanceSummaryCard(summary: state.summary),

            const SizedBox(height: 12),

            // Quick Actions
            _buildQuickActions(),

            const SizedBox(height: 12),

            // Recent Splits Section
            _buildSectionHeader('Recent Splits',
                onViewAll: () => _showAllSplits(state)),

            const SizedBox(height: 8),

            SplitListWidget(
              splits: state.splits.take(5).toList(),
              isCompact: true,
              onSplitTap: _onSplitTap,
              onEditSplit: _onEditSplit,
              onDeleteSplit: _onDeleteSplit,
            ),

            const SizedBox(height: 16),

            // Recent Settle Section
            _buildSectionHeader('Recent Settle',
                onViewAll: () => _showAllSettles(state)),

            const SizedBox(height: 8),

            RecentSettleWidget(
              settles: state.recentSettles.take(5).toList(),
              isCompact: true,
              onEditSettle: _onEditSettle,
              onDeleteSettle: _onDeleteSettle,
            ),

            const SizedBox(height: 16),

            // Your Balance Section
            _buildSectionHeader('Your Balance'),

            const SizedBox(height: 8),

            UserBalancesWidget(
              userBalances: state.summary.userBalances,
              onSettleDebts: _onSettleDebts,
            ),

            const SizedBox(height: 16),

            // Space Balance Section
            _buildSectionHeader('Space Balance'),

            const SizedBox(height: 8),

            _buildSpaceBalanceSection(state),

            const SizedBox(height: 16),

            // Expense Categories Section
            _buildSectionHeader('Expense Categories'),

            const SizedBox(height: 8),

            ExpenseCategoriesWidget(
              categories: state.categories,
              totalExpenses: state.summary.totalExpenses,
            ),

            // Add bottom padding
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    icon: Icons.receipt_long,
                    label: 'Add Split',
                    onTap: _showAddSplitDialog,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    icon: Icons.account_balance,
                    label: 'Settle Up',
                    onTap: _showSettleUpDialog,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(icon, color: AppColors.textSecondary),
            const SizedBox(height: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, {VoidCallback? onViewAll}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        if (onViewAll != null)
          TextButton(
            onPressed: onViewAll,
            child: const Text('View All'),
          ),
      ],
    );
  }

  Widget _buildSpaceBalanceSection(BalanceLoaded state) {
    // Show all users with their net balances and debt relationships
    final userBalances = state.summary.userBalances;

    if (userBalances.every((balance) => balance.netBalance == 0)) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.people,
                size: 48,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'All Settled Up!',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'No outstanding balances with other members.\nAll debts are settled!',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Show all users with their net balances and relationships
        ...userBalances
            .where((balance) => balance.netBalance != 0)
            .map((userBalance) {
          return Card(
            margin: const EdgeInsets.only(bottom: 12),
            elevation: 2,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildUserBalanceOverviewItem(userBalance, userBalances),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildUserBalanceOverviewItem(
      UserBalance userBalance, List<UserBalance> allBalances) {
    // netBalance > 0: user owes others money (red, show as negative)
    // netBalance < 0: others owe user money (green, show as positive)

    // Get current user info
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserId = authService.currentUser?.id ?? '';

    // Find who this user owes money to and who owes money to this user
    final owesTo = <String>[];
    final getsFrom = <String>[];

    // Use netBalance to determine the relationship direction from user's perspective
    if (userBalance.netBalance > 0) {
      // This user owes money (positive netBalance) - show "Owes money: creditors"
      // Find users with negative balance (who this user should pay)
      final creditors = allBalances
          .where((b) => b.netBalance < 0 && b.userId != userBalance.userId)
          .map((b) =>
              b.userId == currentUserId ? '${b.userName}(me)' : b.userName)
          .toList();
      owesTo.addAll(creditors);
    } else if (userBalance.netBalance < 0) {
      // Others owe this user money (negative netBalance) - show "Gets money: debtors"
      // Find users with positive balance (who owe this user money)
      final debtors = allBalances
          .where((b) => b.netBalance > 0 && b.userId != userBalance.userId)
          .map((b) =>
              b.userId == currentUserId ? '${b.userName}(me)' : b.userName)
          .toList();
      getsFrom.addAll(debtors);
    }

    // Remove duplicates and sort
    final uniqueOwesTo = owesTo.toSet().toList()..sort();
    final uniqueGetsFrom = getsFrom.toSet().toList()..sort();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // User info and net balance
        Row(
          children: [
            // User avatar
            CircleAvatar(
              radius: 20,
              backgroundColor: AppColors.primary.withValues(alpha: 0.1),
              child: Text(
                userBalance.userName.isNotEmpty
                    ? userBalance.userName[0].toUpperCase()
                    : '?',
                style: const TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),

            const SizedBox(width: 12),

            // User name
            Expanded(
              child: Text(
                userBalance.userId == currentUserId
                    ? '${userBalance.userName}(me)'
                    : userBalance.userName,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ),

            // Single currency balance display
            Builder(
              builder: (context) {
                if (userBalance.currencyBalances.isEmpty) {
                  return const Text('Settled');
                }

                final currencyBalance = userBalance.currencyBalances.first;
                return Text(
                  '${currencyBalance.netBalance > 0 ? '-' : '+'}${currencyBalance.currency}${currencyBalance.netBalance.abs().toStringAsFixed(0)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: currencyBalance.netBalance > 0
                            ? Colors.red.shade600 // User owes money (red)
                            : Colors.green.shade600, // User gets money (green)
                      ),
                );
              },
            ),
          ],
        ),

        const SizedBox(height: 8),

        // Relationship details
        if (uniqueGetsFrom.isNotEmpty) ...[
          Text(
            'Gets money: ${uniqueGetsFrom.join(', ')}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
          ),
        ],
        if (uniqueOwesTo.isNotEmpty) ...[
          Text(
            'Owes money: ${uniqueOwesTo.join(', ')}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
          ),
        ],
      ],
    );
  }

  void _showSettleUpDialog() {
    final currentState = context.read<BalanceBloc>().state;
    if (currentState is! BalanceLoaded) return;

    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserId = authService.currentUser?.id ?? '';

    final availableUsers = currentState.summary.userBalances
        .where((user) => user.userId != currentUserId)
        .toList();

    if (availableUsers.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No other members in this space!'),
        ),
      );
      return;
    }

    // If only one user available, go directly to Record Payment dialog
    if (availableUsers.length == 1) {
      _showRecordPaymentDialog(availableUsers.first, currentUserId);
      return;
    }

    // Multiple users available - show selection dialog
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.account_balance_wallet,
                      color: AppColors.primary),
                  const SizedBox(width: 8),
                  const Text(
                    'Settle Up',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Choose who to settle up with:'),
                  const SizedBox(height: 16),
                  ...availableUsers.map((user) {
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor:
                            AppColors.primary.withValues(alpha: 0.1),
                        child: Text(
                          user.userName.isNotEmpty
                              ? user.userName[0].toUpperCase()
                              : '?',
                          style: const TextStyle(
                            color: AppColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      title: Text(user.userName),
                      subtitle: Builder(
                        builder: (context) {
                          if (user.currencyBalances.isEmpty) {
                            return const Text('Settled');
                          }

                          final currencyBalance = user.currencyBalances.first;
                          return Text(
                            'Net: ${currencyBalance.netBalance >= 0 ? '+' : ''}${currencyBalance.currency}${currencyBalance.netBalance.toStringAsFixed(2)}',
                            style: TextStyle(
                              color: currencyBalance.netBalance >= 0
                                  ? Colors.green
                                  : Colors.red,
                              fontWeight: FontWeight.w600,
                            ),
                          );
                        },
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        _showRecordPaymentDialog(user, currentUserId);
                      },
                    );
                  }),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showRecordPaymentDialog(UserBalance userBalance, String currentUserId) {
    final currentState = context.read<BalanceBloc>().state;
    final spaceCurrency = currentState is BalanceLoaded
        ? currentState.summary.spaceCurrency
        : 'USD';

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => RecordPaymentDialog(
        userBalance: userBalance,
        currentUserId: currentUserId,
        spaceCurrency: spaceCurrency,
        onRecordPayment: (fromUserId, toUserId, amount, currency) {
          _onRecordPayment(fromUserId, toUserId, amount, currency, userBalance);
        },
      ),
    );
  }

  void _onRecordPayment(String fromUserId, String toUserId, double amount,
      String currency, UserBalance userBalance) {
    // Get user names
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserId = authService.currentUser?.id ?? '';
    final currentUserName = authService.currentUser?.displayName ?? 'You';

    String fromUserName;
    String toUserName;

    if (fromUserId == currentUserId) {
      fromUserName = currentUserName;
      toUserName = userBalance.userName;
    } else {
      fromUserName = userBalance.userName;
      toUserName = currentUserName;
    }

    // Record payment with specific amount using balance bloc
    context.read<BalanceBloc>().add(PaymentRecorded(
          spaceId: widget.spaceId,
          fromUserId: fromUserId,
          fromUserName: fromUserName,
          toUserId: toUserId,
          toUserName: toUserName,
          amount: amount,
          currency: currency,
        ));

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text('Payment recorded: $currency${amount.toStringAsFixed(2)}'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _showAddSplitDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreateSplitDialog(
        spaceId: widget.spaceId,
        onCreateSplit: (splitRequest) {
          _createSplit(splitRequest);
        },
      ),
    );
  }

  Future<void> _createSplit(CreateSplitRequest splitRequest) async {
    try {
      // Get ChatRepository from dependency injection
      final chatRepository = DependencyInjection.getIt<ChatRepository>();

      // Convert CreateSplitRequest to message format
      final splitData = {
        'title': splitRequest.title,
        'description': splitRequest.description,
        'category': splitRequest.category,
        'totalAmount': splitRequest.totalAmount,
        'currency': splitRequest.currency,
        'paidByUserId': splitRequest.paidByUserId,
        'paidByUserName': '', // Will be set by backend
        'splitType': splitRequest.splitType.name,
        'participants':
            <Map<String, dynamic>>[], // Will be populated by backend
        'creatorId': '', // Will be set by backend
        'createdAt': DateTime.now().toIso8601String(),
        'settledAt': null,
        'isSettled': false,
        'participantUserIds': splitRequest.participantUserIds,
        'customAmounts': splitRequest.customAmounts,
      };

      // Create message request
      final messageRequest = CreateMessageRequest(
        content: splitRequest.title, // Use title as content
        type: MessageType.split,
        metadata: splitData,
      );

      // Send the split message
      await chatRepository.sendMessage(widget.spaceId, messageRequest);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Split created successfully!'),
            backgroundColor: AppColors.primary,
          ),
        );

        // Refresh balance data to show the new split
        context
            .read<BalanceBloc>()
            .add(BalanceRefreshRequested(widget.spaceId));
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create split: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _onSplitTap(String splitId) {
    // Find the split data
    final currentState = context.read<BalanceBloc>().state;
    if (currentState is BalanceLoaded) {
      final split = currentState.splits.firstWhere(
        (s) => s.creatorId == splitId,
        orElse: () => currentState.splits.first,
      );

      // Capture the BalanceBloc instance before navigation
      final balanceBloc = context.read<BalanceBloc>();

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (newContext) => BlocProvider.value(
            value: balanceBloc,
            child: SplitDetailPage(
              spaceId: widget.spaceId,
              splitId: splitId,
              splitData: split,
            ),
          ),
        ),
      );
    }
  }

  void _onSettleDebts(String fromUserId, String toUserId) {
    context.read<BalanceBloc>().add(DebtsSettled(
          spaceId: widget.spaceId,
          fromUserId: fromUserId,
          toUserId: toUserId,
        ));
  }

  void _showAllSplits(BalanceLoaded state) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.8,
          minChildSize: 0.6,
          maxChildSize: 0.95,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                // Drag handle
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                // Header
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                  child: Row(
                    children: [
                      const Icon(Icons.receipt_long, color: AppColors.primary),
                      const SizedBox(width: 8),
                      const Text(
                        'All Splits',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                        iconSize: 20,
                      ),
                    ],
                  ),
                ),
                // Splits List
                Expanded(
                  child: SplitListWidget(
                    splits: state.splits,
                    isCompact: false,
                    onSplitTap: (splitId) {
                      Navigator.pop(context); // Close dialog first
                      _onSplitTap(splitId);
                    },
                    onEditSplit: (split) {
                      Navigator.pop(context); // Close dialog first
                      _onEditSplit(split);
                    },
                    onDeleteSplit: (splitId) {
                      Navigator.pop(context); // Close dialog first
                      _onDeleteSplit(splitId);
                    },
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  void _showAllSettles(BalanceLoaded state) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.8,
          minChildSize: 0.6,
          maxChildSize: 0.95,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                // Drag handle
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                // Header
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                  child: Row(
                    children: [
                      const Icon(Icons.handshake, color: AppColors.primary),
                      const SizedBox(width: 8),
                      const Text(
                        'All Settles',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                        iconSize: 20,
                      ),
                    ],
                  ),
                ),
                // Settles List
                Expanded(
                  child: RecentSettleWidget(
                    settles: state.recentSettles,
                    isCompact: false,
                    onEditSettle: _onEditSettle,
                    onDeleteSettle: _onDeleteSettle,
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  void _onEditSettle(SettleRecord settle) {
    // Get BalanceBloc reference before showing dialog
    final balanceBloc = context.read<BalanceBloc>();

    // Show edit settle dialog
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (dialogContext) => EditSettleDialog(
        settle: settle,
        onSave: (updatedSettle) {
          // Use the BalanceBloc reference from outside the dialog
          balanceBloc.add(SettleUpdated(
            spaceId: widget.spaceId,
            settleId: settle.id,
            amount: updatedSettle.amount,
            description: '', // Description is auto-generated
            currency: updatedSettle.currency,
          ));

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Settle updated successfully'),
              backgroundColor: AppColors.primary,
            ),
          );
        },
      ),
    );
  }

  void _onEditSplit(SplitData split) {
    // Show a snackbar for now - edit functionality can be implemented later
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Edit split feature coming soon!'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _onDeleteSplit(String splitId) {
    // Directly execute delete operation (confirmation already handled in widget)
    context.read<BalanceBloc>().add(SplitDeleted(
          spaceId: widget.spaceId,
          splitId: splitId,
        ));

    // Don't show success message immediately - wait for the operation to complete
    // The success/error message will be handled by the bloc state changes
  }

  void _onDeleteSettle(SettleRecord settle) {
    // Directly execute delete operation (confirmation already handled in widget)
    context.read<BalanceBloc>().add(SettleDeleted(
          spaceId: widget.spaceId,
          settleId: settle.id,
          messageId: settle.messageId, // Pass messageId for proper deletion
        ));

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Settle deleted successfully'),
        backgroundColor: Colors.red,
      ),
    );
  }
}
