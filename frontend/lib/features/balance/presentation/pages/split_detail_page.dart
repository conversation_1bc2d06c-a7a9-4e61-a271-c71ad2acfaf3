import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/loading_state_manager.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../chat/data/models/split_model.dart';
import '../bloc/balance_bloc.dart';
import '../../../auth/domain/services/auth_service.dart';

class SplitDetailPage extends StatefulWidget {
  final String spaceId;
  final String splitId;
  final SplitData? splitData; // Optional initial data

  const SplitDetailPage({
    super.key,
    required this.spaceId,
    required this.splitId,
    this.splitData,
  });

  @override
  State<SplitDetailPage> createState() => _SplitDetailPageState();
}

class _SplitDetailPageState extends State<SplitDetailPage> {
  late SplitData? _splitData;

  @override
  void initState() {
    super.initState();
    _splitData = widget.splitData;

    // If no initial data provided, load from backend
    if (_splitData == null) {
      // TODO: Load split data from backend
      // For now, show loading state
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Split Details'),
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textPrimary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editSplit,
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy, size: 20),
                    SizedBox(width: 8),
                    Text('Duplicate'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 20, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _splitData == null
          ? const LoadingStateManager(
              type: LoadingStateType.spinner,
              isLoading: true,
              child: SizedBox.shrink(),
            )
          : _buildSplitDetails(),
    );
  }

  Widget _buildSplitDetails() {
    final split = _splitData!;
    final categoryInfo = _getCategoryInfo(split.category ?? 'Other');

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Split Header Card
          _buildHeaderCard(split, categoryInfo),

          const SizedBox(height: 16),

          // Participants Card
          _buildParticipantsCard(split),
        ],
      ),
    );
  }

  List<SplitParticipant> _getSortedParticipants(SplitData split) {
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserId = authService.currentUser?.id;

    // Filter out the payer and sort to put current user first
    final participants = split.participants.where((participant) {
      return participant.userId != split.paidByUserId;
    }).toList();

    participants.sort((a, b) {
      final aIsCurrentUser = a.userId == currentUserId;
      final bIsCurrentUser = b.userId == currentUserId;

      if (aIsCurrentUser && !bIsCurrentUser) {
        return -1; // a (current user) comes first
      } else if (!aIsCurrentUser && bIsCurrentUser) {
        return 1; // b (current user) comes first
      } else {
        // Sort by name alphabetically
        return a.userName.compareTo(b.userName);
      }
    });

    return participants;
  }

  Widget _buildHeaderCard(SplitData split, Map<String, dynamic> categoryInfo) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Color(categoryInfo['color']).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    categoryInfo['icon'],
                    style: const TextStyle(fontSize: 24),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        split.title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      if (split.description != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          split.description!,
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Amount and Status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Total Amount',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${split.currency}${split.totalAmount.toStringAsFixed(2)}',
                      style:
                          Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppColors.primary,
                              ),
                    ),
                  ],
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Text(
                    'Split Record',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Meta Information
            Row(
              children: [
                const Icon(Icons.person,
                    size: 16, color: AppColors.textSecondary),
                const SizedBox(width: 4),
                Text(
                  'Paid by ${split.paidByUserName}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                ),
                const SizedBox(width: 16),
                const Icon(Icons.calendar_today,
                    size: 16, color: AppColors.textSecondary),
                const SizedBox(width: 4),
                Text(
                  DateFormat('MMM dd, yyyy').format(split.createdAt),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildParticipantsCard(SplitData split) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Participants',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            // Show payer information separately
            if (split.participants
                .any((p) => p.userId == split.paidByUserId)) ...[
              Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.blue.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: Colors.blue.withValues(alpha: 0.1),
                        child: Icon(
                          Icons.account_balance_wallet,
                          color: Colors.blue.shade600,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _getPayerDisplayName(split),
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                            Text(
                              'Paid ${split.currency}${split.totalAmount.toStringAsFixed(2)} for everyone',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: Colors.blue.shade600,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.blue,
                            width: 1,
                          ),
                        ),
                        child: Text(
                          'Payer',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],

            // Show other participants who need to pay (current user first)
            ..._getSortedParticipants(split).map((participant) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.surfaceVariant.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.border.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor:
                            AppColors.primary.withValues(alpha: 0.1),
                        child: Text(
                          participant.userName.isNotEmpty
                              ? participant.userName[0].toUpperCase()
                              : '?',
                          style: const TextStyle(
                            color: AppColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _getParticipantDisplayName(participant),
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                            Text(
                              'Owes ${split.currency}${participant.amount.toStringAsFixed(2)}',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: AppColors.primary,
                            width: 1,
                          ),
                        ),
                        child: const Text(
                          'Owes',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Map<String, dynamic> _getCategoryInfo(String category) {
    final categories = {
      'Flights': {'icon': '✈️', 'color': 0xFF2196F3},
      'Lodging': {'icon': '🏨', 'color': 0xFF4CAF50},
      'Car rental': {'icon': '🚗', 'color': 0xFFFF9800},
      'Transit': {'icon': '🚌', 'color': 0xFF9C27B0},
      'Food': {'icon': '🍽️', 'color': 0xFFE91E63},
      'Drinks': {'icon': '🥤', 'color': 0xFF00BCD4},
      'Sightseeing': {'icon': '🏛️', 'color': 0xFF795548},
      'Activities': {'icon': '🎯', 'color': 0xFF607D8B},
      'Shopping': {'icon': '🛍️', 'color': 0xFFFF5722},
      'Gas': {'icon': '⛽', 'color': 0xFF3F51B5},
      'Groceries': {'icon': '🛒', 'color': 0xFF8BC34A},
      'Other': {'icon': '📋', 'color': 0xFF9E9E9E},
    };

    return categories[category] ?? categories['Other']!;
  }

  void _editSplit() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Edit split feature coming soon!'),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'duplicate':
        _duplicateSplit();
        break;
      case 'delete':
        _deleteSplit();
        break;
    }
  }

  void _duplicateSplit() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Duplicate split feature coming soon!'),
      ),
    );
  }

  void _deleteSplit() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.delete, color: Colors.red),
                  const SizedBox(width: 8),
                  const Text(
                    'Delete Split',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  const Text(
                    'Are you sure you want to delete this split? This action cannot be undone.',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  // Actions
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.pop(context),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () async {
                            Navigator.pop(context);
                            await _performDelete();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Delete'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _performDelete() async {
    if (!mounted) return;

    // Show loading indicator
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            SizedBox(width: 12),
            Text('Deleting split...'),
          ],
        ),
        duration: Duration(seconds: 2),
      ),
    );

    try {
      // Check if we have the messageId from splitData
      final messageId = _splitData?.messageId;
      if (messageId == null) {
        throw Exception('Cannot delete split: message ID not available');
      }

      // Trigger delete split event using messageId
      context.read<BalanceBloc>().add(SplitDeleted(
            spaceId: widget.spaceId,
            splitId: messageId, // Use messageId instead of splitId
          ));

      // Wait a moment for the operation to complete
      await Future.delayed(const Duration(milliseconds: 1000));

      if (!mounted) return;

      // Go back to balance page
      Navigator.pop(context);

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Split deleted successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      if (!mounted) return;

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to delete split: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  String _getPayerDisplayName(SplitData split) {
    // Add (me) identifier for current user, but remove (Payer) since there's already a tag
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserId = authService.currentUser?.id ?? '';

    return split.paidByUserId == currentUserId
        ? '${split.paidByUserName}(me)'
        : split.paidByUserName;
  }

  String _getParticipantDisplayName(SplitParticipant participant) {
    // Add (me) identifier for current user
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserId = authService.currentUser?.id ?? '';

    return participant.userId == currentUserId
        ? '${participant.userName}(me)'
        : participant.userName;
  }
}
