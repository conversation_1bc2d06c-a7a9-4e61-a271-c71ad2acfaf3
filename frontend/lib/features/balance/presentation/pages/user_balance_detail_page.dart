import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/loading_state_manager.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../auth/domain/services/auth_service.dart';
import '../../data/models/balance_model.dart';
import '../../../chat/data/models/split_model.dart';
import '../bloc/balance_bloc.dart';

class UserBalanceDetailPage extends StatefulWidget {
  final String spaceId;
  final String userId;
  final UserBalance? userBalance; // Optional initial data

  const UserBalanceDetailPage({
    super.key,
    required this.spaceId,
    required this.userId,
    this.userBalance,
  });

  @override
  State<UserBalanceDetailPage> createState() => _UserBalanceDetailPageState();
}

class _UserBalanceDetailPageState extends State<UserBalanceDetailPage> {
  late UserBalance? _userBalance;
  List<SplitData> _userSplits = [];

  @override
  void initState() {
    super.initState();
    _userBalance = widget.userBalance;

    // Load user's splits
    _loadUserSplits();
  }

  void _loadUserSplits() {
    // TODO: Load splits where user is involved
    // For now, use empty list
    setState(() {
      _userSplits = [];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_userBalance?.userName ?? 'User Balance'),
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textPrimary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.message),
            onPressed: _sendMessage,
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'settle',
                child: Row(
                  children: [
                    Icon(Icons.account_balance, size: 20),
                    SizedBox(width: 8),
                    Text('Settle Up'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.file_download, size: 20),
                    SizedBox(width: 8),
                    Text('Export History'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _userBalance == null
          ? const LoadingStateManager(
              type: LoadingStateType.spinner,
              isLoading: true,
              child: SizedBox.shrink(),
            )
          : _buildUserBalanceDetails(),
    );
  }

  Widget _buildUserBalanceDetails() {
    final userBalance = _userBalance!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User Info Card
          _buildUserInfoCard(userBalance),

          const SizedBox(height: 16),

          // Balance Summary Card
          _buildBalanceSummaryCard(userBalance),

          const SizedBox(height: 16),

          // Pending Payments Card
          if (userBalance.pendingPayments.isNotEmpty)
            _buildPendingPaymentsCard(userBalance),

          const SizedBox(height: 16),

          // Recent Splits Card
          _buildRecentSplitsCard(),

          const SizedBox(height: 16),

          // Actions Card
          _buildActionsCard(userBalance),
        ],
      ),
    );
  }

  Widget _buildUserInfoCard(UserBalance userBalance) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: AppColors.primary.withValues(alpha: 0.1),
              backgroundImage: userBalance.userAvatar != null
                  ? NetworkImage(userBalance.userAvatar!)
                  : null,
              child: userBalance.userAvatar == null
                  ? Text(
                      userBalance.userName.isNotEmpty
                          ? userBalance.userName[0].toUpperCase()
                          : '?',
                      style: const TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    userBalance.userName,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: userBalance.isSettled
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      userBalance.isSettled ? 'All Settled' : 'Has Pending',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: userBalance.isSettled
                            ? Colors.green.shade700
                            : Colors.orange.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceSummaryCard(UserBalance userBalance) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Balance Summary',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildBalanceItem(
                    'Total Paid',
                    userBalance.totalPaid,
                    Colors.green.shade100,
                    Colors.green.shade600,
                  ),
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: AppColors.border,
                ),
                Expanded(
                  child: _buildBalanceItem(
                    'Total Owes',
                    userBalance.totalOwed,
                    Colors.orange.shade100,
                    Colors.orange.shade600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: userBalance.netBalance >= 0
                    ? Colors.green.withValues(alpha: 0.1)
                    : Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Net Balance',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  BlocBuilder<BalanceBloc, BalanceState>(
                    builder: (context, state) {
                      final currency = state is BalanceLoaded
                          ? state.summary.spaceCurrency
                          : 'USD';
                      return Text(
                        '${userBalance.netBalance >= 0 ? '+' : ''}$currency${userBalance.netBalance.toStringAsFixed(2)}',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: userBalance.netBalance >= 0
                                      ? Colors.green.shade700
                                      : Colors.red.shade700,
                                ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceItem(
      String label, double amount, Color bgColor, Color textColor) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Column(
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            amount.toStringAsFixed(2), // TODO: Support multi-currency display
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPendingPaymentsCard(UserBalance userBalance) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Pending Payments',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            ...userBalance.pendingPayments.map((payment) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  children: [
                    Icon(
                      payment.fromUserId == userBalance.userId
                          ? Icons.arrow_upward
                          : Icons.arrow_downward,
                      size: 16,
                      color: payment.fromUserId == userBalance.userId
                          ? Colors.red.shade600
                          : Colors.green.shade600,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        payment.fromUserId == userBalance.userId
                            ? 'Pay ${payment.toUserName}'
                            : 'Receive from ${payment.fromUserName}',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                    Text(
                      '${payment.currency}${payment.amount.toStringAsFixed(2)}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: payment.fromUserId == userBalance.userId
                                ? Colors.red.shade600
                                : Colors.green.shade600,
                          ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentSplitsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Splits',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                TextButton(
                  onPressed: _viewAllSplits,
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_userSplits.isEmpty)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.receipt_long,
                      size: 48,
                      color: AppColors.textSecondary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'No splits found',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                    ),
                  ],
                ),
              )
            else
              ..._userSplits.take(3).map((split) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.receipt,
                          size: 16,
                          color: AppColors.primary,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              split.title,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                            Text(
                              DateFormat('MMM dd, yyyy')
                                  .format(split.createdAt),
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        '${split.currency}${split.totalAmount.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.primary,
                            ),
                      ),
                    ],
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsCard(UserBalance userBalance) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _sendReminder,
                    icon: const Icon(Icons.notifications, size: 18),
                    label: const Text('Send Reminder'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      side: const BorderSide(color: AppColors.primary),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: userBalance.isSettled
                        ? null
                        : () => _settleUp(userBalance),
                    icon: const Icon(Icons.account_balance, size: 18),
                    label: const Text('Settle Up'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _sendMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Message feature coming soon!'),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'settle':
        if (_userBalance != null) {
          _settleUp(_userBalance!);
        }
        break;
      case 'export':
        _exportHistory();
        break;
    }
  }

  void _viewAllSplits() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('View all splits feature coming soon!'),
      ),
    );
  }

  void _sendReminder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Reminder sent successfully!'),
      ),
    );
  }

  void _settleUp(UserBalance userBalance) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.handshake, color: Colors.green),
                  const SizedBox(width: 8),
                  Text(
                    'Settle with ${userBalance.userName}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  Text(
                    'Mark all debts between you and ${userBalance.userName} as settled?',
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  // Actions
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.pop(context),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);

                            // Trigger settle debts event
                            final authService =
                                DependencyInjection.getIt<AuthService>();
                            final currentUserId =
                                authService.currentUser?.id ?? '';
                            context.read<BalanceBloc>().add(DebtsSettled(
                                  spaceId: widget.spaceId,
                                  fromUserId: currentUserId,
                                  toUserId: userBalance.userId,
                                ));

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Settled up with ${userBalance.userName}!'),
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Settle'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _exportHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Export history feature coming soon!'),
      ),
    );
  }
}
