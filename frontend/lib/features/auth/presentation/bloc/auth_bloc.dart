import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:logger/logger.dart';

import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/register_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/get_current_user_usecase.dart';
import '../../domain/services/auth_service.dart';
import '../../domain/services/profile_storage_service.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../../core/storage/secure_storage.dart';

// Events
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object> get props => [];
}

class AuthCheckRequested extends AuthEvent {}

class AuthLoginRequested extends AuthEvent {
  final String email;
  final String password;

  const AuthLoginRequested({
    required this.email,
    required this.password,
  });

  @override
  List<Object> get props => [email, password];
}

class AuthRegisterRequested extends AuthEvent {
  final String email;
  final String username;
  final String password;
  final String displayName;

  const AuthRegisterRequested({
    required this.email,
    required this.username,
    required this.password,
    required this.displayName,
  });

  @override
  List<Object> get props => [email, username, password, displayName];
}

class AuthLogoutRequested extends AuthEvent {}

class AuthTokenLoginRequested extends AuthEvent {
  final String accessToken;
  final String userId;

  const AuthTokenLoginRequested({
    required this.accessToken,
    required this.userId,
  });

  @override
  List<Object> get props => [accessToken, userId];
}

// States
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object> get props => [];
}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class AuthAuthenticated extends AuthState {
  final Map<String, dynamic> user;

  const AuthAuthenticated({required this.user});

  @override
  List<Object> get props => [user];
}

class AuthUnauthenticated extends AuthState {}

class AuthError extends AuthState {
  final String message;

  const AuthError({required this.message});

  @override
  List<Object> get props => [message];
}

// Bloc
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final LoginUseCase _loginUseCase;
  final RegisterUseCase _registerUseCase;
  final LogoutUseCase _logoutUseCase;
  final GetCurrentUserUseCase _getCurrentUserUseCase;

  AuthBloc({
    required LoginUseCase loginUseCase,
    required RegisterUseCase registerUseCase,
    required LogoutUseCase logoutUseCase,
    required GetCurrentUserUseCase getCurrentUserUseCase,
  })  : _loginUseCase = loginUseCase,
        _registerUseCase = registerUseCase,
        _logoutUseCase = logoutUseCase,
        _getCurrentUserUseCase = getCurrentUserUseCase,
        super(AuthInitial()) {
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthLoginRequested>(_onAuthLoginRequested);
    on<AuthRegisterRequested>(_onAuthRegisterRequested);
    on<AuthLogoutRequested>(_onAuthLogoutRequested);
    on<AuthTokenLoginRequested>(_onAuthTokenLoginRequested);
  }

  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    Logger().i('Starting auth check...');
    emit(AuthLoading());

    try {
      // 添加超時保護
      Logger().i('Executing getCurrentUser...');
      final user = await _getCurrentUserUseCase.execute().timeout(
        const Duration(seconds: 8),
        onTimeout: () {
          Logger().w('Auth check timed out, assuming unauthenticated');
          return null;
        },
      );

      Logger().i(
          'GetCurrentUser result: ${user != null ? "User found" : "No user"}');

      if (user != null) {
        // Sync AuthService with the current user
        final authService = DependencyInjection.getIt<AuthService>();
        authService.setCurrentUser(user);

        Logger().i('Emitting AuthAuthenticated state');
        emit(AuthAuthenticated(user: user.toJson()));
      } else {
        Logger().i('Emitting AuthUnauthenticated state');
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      Logger().e('Auth check failed: $e');
      Logger().i('Emitting AuthUnauthenticated state due to error');
      emit(AuthUnauthenticated());
    }
  }

  Future<void> _onAuthLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final authResponse = await _loginUseCase.execute(
        email: event.email,
        password: event.password,
      );

      // Sync AuthService with the logged-in user
      final authService = DependencyInjection.getIt<AuthService>();
      authService.setCurrentUser(authResponse.user);

      // Save profile for future quick login
      final profileStorage = DependencyInjection.getIt<ProfileStorageService>();
      await profileStorage.saveProfileFromLogin(authResponse.user,
          accessToken: authResponse.token);

      emit(AuthAuthenticated(user: authResponse.user.toJson()));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthRegisterRequested(
    AuthRegisterRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final authResponse = await _registerUseCase.execute(
        email: event.email,
        username: event.username,
        password: event.password,
        displayName: event.displayName,
      );

      // Sync AuthService with the registered user
      final authService = DependencyInjection.getIt<AuthService>();
      authService.setCurrentUser(authResponse.user);

      // Save profile for future quick login
      final profileStorage = DependencyInjection.getIt<ProfileStorageService>();
      await profileStorage.saveProfileFromLogin(authResponse.user,
          accessToken: authResponse.token);

      emit(AuthAuthenticated(user: authResponse.user.toJson()));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onAuthLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      await _logoutUseCase.execute();

      // Clear AuthService user data
      final authService = DependencyInjection.getIt<AuthService>();
      await authService.clearUserData();

      emit(AuthUnauthenticated());
    } catch (e) {
      // Even if logout fails, we should still clear the local state
      final authService = DependencyInjection.getIt<AuthService>();
      await authService.clearUserData();

      emit(AuthUnauthenticated());
    }
  }

  Future<void> _onAuthTokenLoginRequested(
    AuthTokenLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      // Store the token temporarily
      final secureStorage = DependencyInjection.getIt<SecureStorage>();
      await secureStorage.storeAccessToken(event.accessToken);

      // Try to get current user with this token
      final user = await _getCurrentUserUseCase.execute();

      if (user != null) {
        // Sync AuthService with the logged-in user
        final authService = DependencyInjection.getIt<AuthService>();
        authService.setCurrentUser(user);

        emit(AuthAuthenticated(user: user.toJson()));
      } else {
        // Token is invalid, clear it
        await secureStorage.clearAccessToken();
        emit(const AuthError(message: 'Invalid token, please login again'));
      }
    } catch (e) {
      // Token login failed, clear the token
      final secureStorage = DependencyInjection.getIt<SecureStorage>();
      await secureStorage.clearAccessToken();
      emit(AuthError(message: 'Auto-login failed: ${e.toString()}'));
    }
  }
}
