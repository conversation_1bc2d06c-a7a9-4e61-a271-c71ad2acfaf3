import 'auth_models.dart';

class SavedProfileModel {
  final String id;
  final String email;
  final String username;
  final String displayName;
  final String? avatarUrl;
  final DateTime lastLoginAt;
  final String? accessToken; // Optional: for auto-login

  const SavedProfileModel({
    required this.id,
    required this.email,
    required this.username,
    required this.displayName,
    this.avatarUrl,
    required this.lastLoginAt,
    this.accessToken,
  });

  factory SavedProfileModel.fromJson(Map<String, dynamic> json) {
    return SavedProfileModel(
      id: json['id'] as String,
      email: json['email'] as String,
      username: json['username'] as String,
      displayName: json['displayName'] as String,
      avatarUrl: json['avatarUrl'] as String?,
      lastLoginAt: DateTime.parse(json['lastLoginAt'] as String),
      accessToken: json['accessToken'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'username': username,
      'displayName': displayName,
      'avatarUrl': avatarUrl,
      'lastLoginAt': lastLoginAt.toIso8601String(),
      'accessToken': accessToken,
    };
  }

  factory SavedProfileModel.fromUserModel(
    UserModel user, {
    String? accessToken,
  }) {
    return SavedProfileModel(
      id: user.id,
      email: user.email,
      username: user.username,
      displayName: user.displayName,
      avatarUrl: user.avatarUrl,
      lastLoginAt: DateTime.now(),
      accessToken: accessToken,
    );
  }

  SavedProfileModel copyWith({
    String? id,
    String? email,
    String? username,
    String? displayName,
    String? avatarUrl,
    DateTime? lastLoginAt,
    String? accessToken,
  }) {
    return SavedProfileModel(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      accessToken: accessToken ?? this.accessToken,
    );
  }
}
