import 'dart:developer' as developer;
import '../usecases/get_current_user_usecase.dart';
import '../../../../core/storage/secure_storage.dart';
import '../../data/models/auth_models.dart';

/// Service for managing authentication state and user information
class AuthService {
  final GetCurrentUserUseCase _getCurrentUserUseCase;
  final SecureStorage _secureStorage;

  UserModel? _currentUser;

  AuthService({
    required GetCurrentUserUseCase getCurrentUserUseCase,
    required SecureStorage secureStorage,
  })  : _getCurrentUserUseCase = getCurrentUserUseCase,
        _secureStorage = secureStorage;

  /// Get current authenticated user
  UserModel? get currentUser => _currentUser;

  /// Check if user is authenticated
  bool get isAuthenticated => _currentUser != null;

  /// Get current access token
  Future<String?> getToken() async {
    return await _secureStorage.getAccessToken();
  }

  /// Initialize auth service and load current user
  Future<void> initialize() async {
    developer.log('AuthService.initialize() called', name: 'AuthService');
    try {
      // Try to get user from secure storage first
      final userMap = await _secureStorage.getUserInfo();
      developer.log('User from storage: $userMap', name: 'AuthService');
      if (userMap != null) {
        _currentUser = UserModel.fromJson(userMap);
        developer.log('Loaded user from storage: ${_currentUser?.id}',
            name: 'AuthService');
      }

      // Verify with server if we have a token
      final token = await _secureStorage.getAccessToken();
      developer.log('Token from storage: ${token != null ? "exists" : "null"}',
          name: 'AuthService');
      if (token != null) {
        try {
          final response = await _getCurrentUserUseCase.execute();
          _currentUser = response;
          developer.log('Updated user from server: ${_currentUser?.id}',
              name: 'AuthService');
        } catch (e) {
          developer.log('Failed to get user from server: $e',
              name: 'AuthService');
          // Token might be expired, clear stored data
          await clearUserData();
        }
      }
      developer.log(
          'AuthService initialized. Current user: ${_currentUser?.id}',
          name: 'AuthService');
    } catch (e) {
      developer.log('Error initializing AuthService: $e', name: 'AuthService');
      // Error loading user data, clear everything
      await clearUserData();
    }
  }

  /// Set current user (called after login/register)
  void setCurrentUser(UserModel user) {
    _currentUser = user;
  }

  /// Clear user data (called on logout)
  Future<void> clearUserData() async {
    _currentUser = null;
    await _secureStorage.clearAll();
  }

  /// Refresh current user data from server
  Future<void> refreshUser() async {
    try {
      final response = await _getCurrentUserUseCase.execute();
      _currentUser = response;

      // Update stored user info
      if (response != null) {
        await _secureStorage.storeUserInfo(response.toJson());
      }
    } catch (e) {
      // If refresh fails, user might need to login again
      await clearUserData();
      rethrow;
    }
  }
}
