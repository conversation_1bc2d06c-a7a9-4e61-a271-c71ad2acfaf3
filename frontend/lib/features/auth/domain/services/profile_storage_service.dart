import 'dart:convert';
import 'dart:developer' as developer;

import '../../../../core/storage/secure_storage.dart';
import '../../data/models/saved_profile_model.dart';
import '../../data/models/auth_models.dart';

class ProfileStorageService {
  final SecureStorage _secureStorage;
  static const String _savedProfilesKey = 'saved_profiles';
  static const int _maxProfiles = 5; // Maximum number of saved profiles

  ProfileStorageService({
    required SecureStorage secureStorage,
  }) : _secureStorage = secureStorage;

  /// Get all saved profiles
  Future<List<SavedProfileModel>> getSavedProfiles() async {
    try {
      final profilesJson = await _secureStorage.read(_savedProfilesKey);
      if (profilesJson == null || profilesJson.isEmpty) {
        return [];
      }

      final profilesList = jsonDecode(profilesJson) as List<dynamic>;
      final profiles = profilesList
          .map((json) =>
              SavedProfileModel.fromJson(json as Map<String, dynamic>))
          .toList();

      // Sort by last login time (most recent first)
      profiles.sort((a, b) => b.lastLoginAt.compareTo(a.lastLoginAt));

      developer.log('Retrieved ${profiles.length} saved profiles',
          name: 'ProfileStorage');
      return profiles;
    } catch (e) {
      developer.log('Error getting saved profiles: $e', name: 'ProfileStorage');
      return [];
    }
  }

  /// Save a profile after successful login
  Future<void> saveProfile(SavedProfileModel profile) async {
    try {
      final existingProfiles = await getSavedProfiles();

      // Remove existing profile with same ID if exists
      existingProfiles.removeWhere((p) => p.id == profile.id);

      // Add new profile at the beginning
      existingProfiles.insert(0, profile);

      // Keep only the most recent profiles (limit to max)
      if (existingProfiles.length > _maxProfiles) {
        existingProfiles.removeRange(_maxProfiles, existingProfiles.length);
      }

      // Save to storage
      final profilesJson =
          jsonEncode(existingProfiles.map((p) => p.toJson()).toList());
      await _secureStorage.write(_savedProfilesKey, profilesJson);

      developer.log('Saved profile: ${profile.displayName} (${profile.email})',
          name: 'ProfileStorage');
    } catch (e) {
      developer.log('Error saving profile: $e', name: 'ProfileStorage');
    }
  }

  /// Remove a saved profile
  Future<void> removeProfile(String profileId) async {
    try {
      final existingProfiles = await getSavedProfiles();
      existingProfiles.removeWhere((p) => p.id == profileId);

      final profilesJson =
          jsonEncode(existingProfiles.map((p) => p.toJson()).toList());
      await _secureStorage.write(_savedProfilesKey, profilesJson);

      developer.log('Removed profile: $profileId', name: 'ProfileStorage');
    } catch (e) {
      developer.log('Error removing profile: $e', name: 'ProfileStorage');
    }
  }

  /// Clear all saved profiles
  Future<void> clearAllProfiles() async {
    try {
      await _secureStorage.delete(_savedProfilesKey);
      developer.log('Cleared all saved profiles', name: 'ProfileStorage');
    } catch (e) {
      developer.log('Error clearing profiles: $e', name: 'ProfileStorage');
    }
  }

  /// Update profile information (e.g., after profile edit)
  Future<void> updateProfile(SavedProfileModel updatedProfile) async {
    try {
      final existingProfiles = await getSavedProfiles();
      final index =
          existingProfiles.indexWhere((p) => p.id == updatedProfile.id);

      if (index != -1) {
        existingProfiles[index] = updatedProfile.copyWith(
          lastLoginAt: DateTime.now(),
        );

        final profilesJson =
            jsonEncode(existingProfiles.map((p) => p.toJson()).toList());
        await _secureStorage.write(_savedProfilesKey, profilesJson);

        developer.log('Updated profile: ${updatedProfile.displayName}',
            name: 'ProfileStorage');
      }
    } catch (e) {
      developer.log('Error updating profile: $e', name: 'ProfileStorage');
    }
  }

  /// Check if a profile exists
  Future<bool> hasProfile(String profileId) async {
    final profiles = await getSavedProfiles();
    return profiles.any((p) => p.id == profileId);
  }

  /// Get a specific profile by ID
  Future<SavedProfileModel?> getProfile(String profileId) async {
    final profiles = await getSavedProfiles();
    try {
      return profiles.firstWhere((p) => p.id == profileId);
    } catch (e) {
      return null;
    }
  }

  /// Save profile from successful login
  Future<void> saveProfileFromLogin(UserModel user,
      {String? accessToken}) async {
    // Store the access token for auto-login
    final profile =
        SavedProfileModel.fromUserModel(user, accessToken: accessToken);
    await saveProfile(profile);
  }
}
