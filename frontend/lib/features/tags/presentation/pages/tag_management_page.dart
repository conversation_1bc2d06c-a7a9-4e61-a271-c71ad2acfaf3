import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/tag_model.dart';
import '../../../../core/blocs/tag/tag_bloc.dart';
import '../../../../core/blocs/tag/tag_event.dart';
import '../../../../core/blocs/tag/tag_state.dart';
import '../../../../core/di/dependency_injection.dart';
import '../widgets/tag_item_widget.dart';
import '../widgets/tag_create_dialog.dart';
import '../widgets/tag_edit_dialog.dart';

class TagManagementPage extends StatefulWidget {
  final String spaceId;

  const TagManagementPage({
    super.key,
    required this.spaceId,
  });

  @override
  State<TagManagementPage> createState() => _TagManagementPageState();
}

class _TagManagementPageState extends State<TagManagementPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  void _handleSearch(BuildContext context) {
    if (_searchQuery.isEmpty) {
      context.read<TagBloc>().add(const TagClearSearch());
    } else {
      context.read<TagBloc>().add(TagSearchRequested(
            spaceId: widget.spaceId,
            searchTerm: _searchQuery,
          ));
    }
  }

  void _showCreateTagDialog() {
    // Store the correct context that has access to TagBloc
    final tagBloc = context.read<TagBloc>();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (dialogContext) => TagCreateDialog(
        spaceId: widget.spaceId,
        tagBloc: tagBloc,
        onTagCreated: () {
          tagBloc.add(TagRefreshRequested(
            spaceId: widget.spaceId,
          ));
        },
      ),
    );
  }

  void _showEditTagDialog(TagData tag) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => TagEditDialog(
        spaceId: widget.spaceId,
        tag: tag,
        onTagUpdated: () {
          context.read<TagBloc>().add(TagRefreshRequested(
                spaceId: widget.spaceId,
              ));
        },
      ),
    );
  }

  void _showDeleteConfirmation(TagData tag, BuildContext blocContext) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (dialogContext) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.delete, color: AppColors.error),
                  const SizedBox(width: 8),
                  const Text(
                    'Delete Tag',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(dialogContext).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Are you sure you want to delete "${tag.name}"?',
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  if (tag.usageCount > 0)
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.warning.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: AppColors.warning),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.warning_amber,
                            color: AppColors.warning,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'This tag is used in ${tag.usageCount} item(s). Deleting it will remove the tag from all associated items.',
                              style:
                                  Theme.of(dialogContext).textTheme.bodySmall,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
            // Actions
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  top: BorderSide(color: Colors.grey[200]!),
                ),
              ),
              child: SafeArea(
                child: Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.of(dialogContext).pop(),
                        child: const Text('Cancel'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () async {
                          Navigator.of(dialogContext).pop();
                          await _deleteTag(tag, blocContext);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.error,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Delete'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _deleteTag(TagData tag, BuildContext blocContext) async {
    try {
      blocContext.read<TagBloc>().add(TagDeleteRequested(
            spaceId: widget.spaceId,
            tagId: tag.id,
          ));
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete tag: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => DependencyInjection.getIt<TagBloc>()
        ..add(TagLoadRequested(spaceId: widget.spaceId)),
      child: Builder(
        builder: (context) {
          // Handle search changes in the correct context
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              _handleSearch(context);
            }
          });

          return Scaffold(
            backgroundColor: AppColors.background,
            body: Stack(
              children: [
                Column(
                  children: [
                    // Search Bar
                    Container(
                      padding:
                          const EdgeInsets.all(AppConstants.defaultPadding),
                      decoration: const BoxDecoration(
                        color: AppColors.surface,
                        border: Border(
                          bottom: BorderSide(
                            color: AppColors.border,
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _searchController,
                              decoration: InputDecoration(
                                hintText: 'Search tags...',
                                prefixIcon: const Icon(Icons.search),
                                suffixIcon: _searchQuery.isNotEmpty
                                    ? IconButton(
                                        icon: const Icon(Icons.clear),
                                        onPressed: () {
                                          _searchController.clear();
                                        },
                                      )
                                    : null,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Tags List
                    Expanded(
                      child: BlocBuilder<TagBloc, TagState>(
                        builder: (context, state) {
                          if (state is TagLoading) {
                            return const Center(
                              child: CircularProgressIndicator(),
                            );
                          }

                          if (state is TagError) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.error_outline,
                                    size: 64,
                                    color: AppColors.error,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Error loading tags',
                                    style: Theme.of(context)
                                        .textTheme
                                        .headlineSmall,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    state.message,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(
                                          color: AppColors.textSecondary,
                                        ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 16),
                                  ElevatedButton(
                                    onPressed: () {
                                      context
                                          .read<TagBloc>()
                                          .add(TagRefreshRequested(
                                            spaceId: widget.spaceId,
                                          ));
                                    },
                                    child: const Text('Retry'),
                                  ),
                                ],
                              ),
                            );
                          }

                          List<TagData> tagsToShow = [];
                          if (state is TagLoaded) {
                            tagsToShow = state.filteredTags;
                          } else if (state is TagSearchResults) {
                            tagsToShow = state.searchResults;
                          }

                          if (tagsToShow.isEmpty) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    _searchQuery.isNotEmpty
                                        ? Icons.search_off
                                        : Icons.label_outline,
                                    size: 64,
                                    color: AppColors.textSecondary,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    _searchQuery.isNotEmpty
                                        ? 'No tags found for "$_searchQuery"'
                                        : 'No tags yet',
                                    style: Theme.of(context)
                                        .textTheme
                                        .headlineSmall
                                        ?.copyWith(
                                          color: AppColors.textSecondary,
                                        ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    _searchQuery.isNotEmpty
                                        ? 'Try a different search term'
                                        : 'Create your first tag to get started',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(
                                          color: AppColors.textSecondary,
                                        ),
                                  ),
                                  if (_searchQuery.isEmpty) ...[
                                    const SizedBox(height: 16),
                                    ElevatedButton.icon(
                                      onPressed: _showCreateTagDialog,
                                      icon: const Icon(Icons.add),
                                      label: const Text('Create Tag'),
                                    ),
                                  ],
                                ],
                              ),
                            );
                          }

                          return RefreshIndicator(
                            onRefresh: () async {
                              context.read<TagBloc>().add(TagRefreshRequested(
                                    spaceId: widget.spaceId,
                                  ));
                            },
                            child: ListView.builder(
                              padding: const EdgeInsets.all(
                                  AppConstants.defaultPadding),
                              itemCount: tagsToShow.length,
                              itemBuilder: (context, index) {
                                final tag = tagsToShow[index];
                                return TagItemWidget(
                                  tag: tag,
                                  spaceId: widget.spaceId,
                                  onTap:
                                      null, // Remove popup dialog, change to internal component expansion
                                  onEdit: () => _showEditTagDialog(tag),
                                  onDelete: () =>
                                      _showDeleteConfirmation(tag, context),
                                );
                              },
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
                Positioned(
                  bottom: 16,
                  right: 16,
                  child: FloatingActionButton(
                    onPressed: _showCreateTagDialog,
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    child: const Icon(Icons.add),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
