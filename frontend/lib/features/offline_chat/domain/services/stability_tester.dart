import 'dart:async';
import 'dart:math';

import 'package:flutter/foundation.dart';

import '../../data/models/offline_chat_models.dart';
import 'cross_platform_chat_service.dart';

/// 穩定性測試器
///
/// 功能：
/// 1. 長時間運行測試
/// 2. 異常情況測試
/// 3. 性能壓力測試
/// 4. 內存洩漏檢測
class StabilityTester {
  final CrossPlatformChatService _chatService;
  final List<StabilityTestResult> _testResults = [];

  // 測試狀態
  bool _isRunning = false;
  Timer? _testTimer;

  // 統計數據
  int _totalOperations = 0;
  int _successfulOperations = 0;
  int _failedOperations = 0;
  // DateTime? _testStartTime;

  // 事件流
  final StreamController<StabilityTestEvent> _eventController =
      StreamController<StabilityTestEvent>.broadcast();

  StabilityTester(this._chatService);

  /// 測試事件流
  Stream<StabilityTestEvent> get eventStream => _eventController.stream;

  /// 測試結果列表
  List<StabilityTestResult> get testResults => List.unmodifiable(_testResults);

  /// 是否正在運行測試
  bool get isRunning => _isRunning;

  /// 運行長時間穩定性測試
  Future<void> runLongTermStabilityTest({
    Duration duration = const Duration(hours: 1),
    Duration operationInterval = const Duration(seconds: 30),
  }) async {
    if (_isRunning) {
      debugPrint('⚠️ 穩定性測試已在運行中');
      return;
    }

    debugPrint('🛡️ 開始長時間穩定性測試 (${duration.inMinutes} 分鐘)');

    _isRunning = true;
    // _testStartTime = DateTime.now();
    _resetStatistics();

    _emitEvent(StabilityTestEventType.testStarted, '長時間穩定性測試');

    try {
      // 設置定期操作定時器
      _testTimer = Timer.periodic(operationInterval, (timer) {
        _performRandomOperation();
      });

      // 等待測試完成
      await Future.delayed(duration);

      // 停止測試
      await _stopTest();

      final result = StabilityTestResult(
        testType: StabilityTestType.longTerm,
        duration: duration,
        totalOperations: _totalOperations,
        successfulOperations: _successfulOperations,
        failedOperations: _failedOperations,
        successRate: _totalOperations > 0
            ? _successfulOperations / _totalOperations
            : 0.0,
      );

      _testResults.add(result);
      _emitEvent(StabilityTestEventType.testCompleted, '長時間穩定性測試完成');

      debugPrint('✅ 長時間穩定性測試完成');
      debugPrint('📊 操作統計: $_successfulOperations/$_totalOperations 成功');
    } catch (e) {
      debugPrint('❌ 長時間穩定性測試異常: $e');
      _emitEvent(StabilityTestEventType.testFailed, '測試異常: $e');
    } finally {
      _isRunning = false;
    }
  }

  /// 運行異常情況測試
  Future<void> runExceptionHandlingTest() async {
    debugPrint('🚨 開始異常情況測試');

    _emitEvent(StabilityTestEventType.testStarted, '異常情況測試');

    final testCases = [
      _testInvalidRoomCreation,
      _testInvalidMessageSending,
      _testConnectionInterruption,
      _testMemoryPressure,
      _testRapidOperations,
    ];

    int passedTests = 0;
    int totalTests = testCases.length;

    for (int i = 0; i < testCases.length; i++) {
      final testCase = testCases[i];
      try {
        debugPrint('🧪 執行異常測試 ${i + 1}/$totalTests');
        await testCase();
        passedTests++;
      } catch (e) {
        debugPrint('❌ 異常測試 ${i + 1} 失敗: $e');
      }

      // 測試間隔
      await Future.delayed(const Duration(seconds: 2));
    }

    final result = StabilityTestResult(
      testType: StabilityTestType.exceptionHandling,
      duration: const Duration(minutes: 5),
      totalOperations: totalTests,
      successfulOperations: passedTests,
      failedOperations: totalTests - passedTests,
      successRate: passedTests / totalTests,
    );

    _testResults.add(result);
    _emitEvent(StabilityTestEventType.testCompleted, '異常情況測試完成');

    debugPrint('✅ 異常情況測試完成: $passedTests/$totalTests 通過');
  }

  /// 運行性能壓力測試
  Future<void> runPerformanceStressTest({
    int messageCount = 1000,
    Duration testDuration = const Duration(minutes: 10),
  }) async {
    debugPrint('⚡ 開始性能壓力測試');

    _emitEvent(StabilityTestEventType.testStarted, '性能壓力測試');

    final startTime = DateTime.now();
    int sentMessages = 0;
    int failedMessages = 0;

    // 快速發送大量消息
    for (int i = 0; i < messageCount; i++) {
      try {
        final message = OfflineChatMessage(
          id: 'stress_test_msg_$i',
          senderId: 'stress_tester',
          senderName: 'Stress Tester',
          content:
              'Stress test message #$i - ${DateTime.now().millisecondsSinceEpoch}',
          type: OfflineMessageType.text,
          timestamp: DateTime.now(),
        );

        final success = await _chatService.sendMessage(message);
        if (success) {
          sentMessages++;
        } else {
          failedMessages++;
        }

        // 檢查是否超時
        if (DateTime.now().difference(startTime) > testDuration) {
          break;
        }

        // 短暫延遲以避免過度壓力
        if (i % 10 == 0) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      } catch (e) {
        failedMessages++;
        debugPrint('❌ 壓力測試消息 $i 發送失敗: $e');
      }
    }

    final endTime = DateTime.now();
    final actualDuration = endTime.difference(startTime);

    final result = StabilityTestResult(
      testType: StabilityTestType.performanceStress,
      duration: actualDuration,
      totalOperations: sentMessages + failedMessages,
      successfulOperations: sentMessages,
      failedOperations: failedMessages,
      successRate: (sentMessages + failedMessages) > 0
          ? sentMessages / (sentMessages + failedMessages)
          : 0.0,
      additionalMetrics: {
        'messagesPerSecond': sentMessages / actualDuration.inSeconds,
        'targetMessageCount': messageCount,
        'actualMessageCount': sentMessages + failedMessages,
      },
    );

    _testResults.add(result);
    _emitEvent(StabilityTestEventType.testCompleted, '性能壓力測試完成');

    debugPrint('✅ 性能壓力測試完成');
    debugPrint('📊 發送統計: $sentMessages 成功, $failedMessages 失敗');
    debugPrint(
        '📊 發送速率: ${result.additionalMetrics?['messagesPerSecond']?.toStringAsFixed(2)} 消息/秒');
  }

  /// 運行內存洩漏檢測
  Future<void> runMemoryLeakTest({
    int iterations = 100,
    Duration iterationDelay = const Duration(seconds: 5),
  }) async {
    debugPrint('🧠 開始內存洩漏檢測');

    _emitEvent(StabilityTestEventType.testStarted, '內存洩漏檢測');

    final memorySnapshots = <int>[];

    for (int i = 0; i < iterations; i++) {
      // 執行一系列操作
      await _performMemoryIntensiveOperations();

      // 記錄內存使用（簡化實現）
      final memoryUsage = _estimateMemoryUsage();
      memorySnapshots.add(memoryUsage);

      debugPrint('🧠 迭代 ${i + 1}/$iterations, 內存使用: ${memoryUsage}MB');

      await Future.delayed(iterationDelay);
    }

    // 分析內存趨勢
    final memoryTrend = _analyzeMemoryTrend(memorySnapshots);

    final result = StabilityTestResult(
      testType: StabilityTestType.memoryLeak,
      duration: Duration(seconds: iterations * iterationDelay.inSeconds),
      totalOperations: iterations,
      successfulOperations: iterations,
      failedOperations: 0,
      successRate: 1.0,
      additionalMetrics: {
        'initialMemoryMB': memorySnapshots.first,
        'finalMemoryMB': memorySnapshots.last,
        'memoryIncreaseMB': memorySnapshots.last - memorySnapshots.first,
        'memoryTrend': memoryTrend,
      },
    );

    _testResults.add(result);
    _emitEvent(StabilityTestEventType.testCompleted, '內存洩漏檢測完成');

    debugPrint('✅ 內存洩漏檢測完成');
    debugPrint(
        '📊 內存變化: ${memorySnapshots.first}MB → ${memorySnapshots.last}MB');
    debugPrint('📊 內存趨勢: $memoryTrend');
  }

  /// 停止當前測試
  Future<void> stopCurrentTest() async {
    if (_isRunning) {
      await _stopTest();
      _emitEvent(StabilityTestEventType.testStopped, '測試已手動停止');
      debugPrint('🛑 穩定性測試已停止');
    }
  }

  /// 獲取測試摘要
  StabilityTestSummary getTestSummary() {
    final totalTests = _testResults.length;
    final successfulTests =
        _testResults.where((r) => r.successRate > 0.8).length;

    return StabilityTestSummary(
      totalTests: totalTests,
      successfulTests: successfulTests,
      failedTests: totalTests - successfulTests,
      testResults: _testResults,
    );
  }

  /// 釋放資源
  void dispose() {
    _testTimer?.cancel();
    _eventController.close();
    _testResults.clear();
    _isRunning = false;
  }

  // 私有方法

  /// 停止測試
  Future<void> _stopTest() async {
    _testTimer?.cancel();
    _testTimer = null;
    _isRunning = false;
  }

  /// 重置統計數據
  void _resetStatistics() {
    _totalOperations = 0;
    _successfulOperations = 0;
    _failedOperations = 0;
  }

  /// 執行隨機操作
  Future<void> _performRandomOperation() async {
    final random = Random();
    final operations = [
      _performScanOperation,
      _performMessageOperation,
      _performStatusCheck,
    ];

    final operation = operations[random.nextInt(operations.length)];

    try {
      await operation();
      _successfulOperations++;
    } catch (e) {
      _failedOperations++;
      debugPrint('❌ 隨機操作失敗: $e');
    }

    _totalOperations++;
  }

  /// 執行掃描操作
  Future<void> _performScanOperation() async {
    await _chatService.scanForChatRooms();
  }

  /// 執行消息操作
  Future<void> _performMessageOperation() async {
    final message = OfflineChatMessage(
      id: 'test_msg_${DateTime.now().millisecondsSinceEpoch}',
      senderId: 'stability_tester',
      senderName: 'Stability Tester',
      content: 'Stability test message',
      type: OfflineMessageType.text,
      timestamp: DateTime.now(),
    );

    await _chatService.sendMessage(message);
  }

  /// 執行狀態檢查
  Future<void> _performStatusCheck() async {
    final status = _chatService.status;
    // final members = _chatService.connectedMembers;
    // final rooms = _chatService.discoveredRooms;

    // 簡單的狀態驗證
    if (status == CrossPlatformChatStatus.error) {
      throw Exception('Service in error state');
    }
  }

  /// 測試無效聊天室創建
  Future<void> _testInvalidRoomCreation() async {
    // 測試空名稱
    final invalidSession = OfflineChatSession(
      sessionId: '',
      roomName: '',
      roomCode: '',
      creatorId: '',
      creatorName: '',
      createdAt: DateTime.now(),
    );

    // 這應該失敗或被優雅處理
    await _chatService.createChatRoom(invalidSession);
  }

  /// 測試無效消息發送
  Future<void> _testInvalidMessageSending() async {
    // 測試空消息
    final invalidMessage = OfflineChatMessage(
      id: '',
      senderId: '',
      senderName: '',
      content: '',
      type: OfflineMessageType.text,
      timestamp: DateTime.now(),
    );

    // 這應該失敗或被優雅處理
    await _chatService.sendMessage(invalidMessage);
  }

  /// 測試連接中斷
  Future<void> _testConnectionInterruption() async {
    // 模擬連接中斷
    await _chatService.disconnect();
    await Future.delayed(const Duration(seconds: 1));

    // 嘗試在斷開狀態下執行操作
    await _performMessageOperation();
  }

  /// 測試內存壓力
  Future<void> _testMemoryPressure() async {
    // 創建大量臨時對象
    final largeList = List.generate(10000, (i) => 'Large string data $i' * 100);

    // 執行操作
    await _performScanOperation();

    // 清理（讓 GC 處理）
    largeList.clear();
  }

  /// 測試快速操作
  Future<void> _testRapidOperations() async {
    // 快速連續執行多個操作
    final futures = <Future>[];

    for (int i = 0; i < 10; i++) {
      futures.add(_performStatusCheck());
    }

    await Future.wait(futures);
  }

  /// 執行內存密集型操作
  Future<void> _performMemoryIntensiveOperations() async {
    // 創建和銷毀大量對象
    for (int i = 0; i < 100; i++) {
      final messages = List.generate(
          100,
          (j) => OfflineChatMessage(
                id: 'memory_test_${i}_$j',
                senderId: 'memory_tester',
                senderName: 'Memory Tester',
                content: 'Memory test message $i-$j',
                type: OfflineMessageType.text,
                timestamp: DateTime.now(),
              ));

      // 模擬處理
      for (final msg in messages) {
        msg.content.length;
      }
    }
  }

  /// 估算內存使用（簡化實現）
  int _estimateMemoryUsage() {
    // 這是一個簡化的內存估算
    // 實際實現可能需要使用平台特定的 API
    return Random().nextInt(50) + 50; // 50-100 MB
  }

  /// 分析內存趨勢
  String _analyzeMemoryTrend(List<int> snapshots) {
    if (snapshots.length < 2) return 'insufficient_data';

    final initial = snapshots.first;
    final final_ = snapshots.last;
    final increase = final_ - initial;

    if (increase > 20) {
      return 'increasing'; // 可能有內存洩漏
    } else if (increase < -10) {
      return 'decreasing';
    } else {
      return 'stable';
    }
  }

  /// 發送事件
  void _emitEvent(StabilityTestEventType type, String message) {
    _eventController.add(StabilityTestEvent(
      type: type,
      message: message,
      timestamp: DateTime.now(),
    ));
  }
}

/// 穩定性測試結果
class StabilityTestResult {
  final StabilityTestType testType;
  final Duration duration;
  final int totalOperations;
  final int successfulOperations;
  final int failedOperations;
  final double successRate;
  final Map<String, dynamic>? additionalMetrics;
  final DateTime timestamp;

  StabilityTestResult({
    required this.testType,
    required this.duration,
    required this.totalOperations,
    required this.successfulOperations,
    required this.failedOperations,
    required this.successRate,
    this.additionalMetrics,
  }) : timestamp = DateTime.now();
}

/// 穩定性測試摘要
class StabilityTestSummary {
  final int totalTests;
  final int successfulTests;
  final int failedTests;
  final List<StabilityTestResult> testResults;

  const StabilityTestSummary({
    required this.totalTests,
    required this.successfulTests,
    required this.failedTests,
    required this.testResults,
  });

  double get successRate => totalTests > 0 ? successfulTests / totalTests : 0.0;
}

/// 穩定性測試事件
class StabilityTestEvent {
  final StabilityTestEventType type;
  final String message;
  final DateTime timestamp;

  const StabilityTestEvent({
    required this.type,
    required this.message,
    required this.timestamp,
  });
}

/// 穩定性測試類型
enum StabilityTestType {
  longTerm,
  exceptionHandling,
  performanceStress,
  memoryLeak,
}

/// 穩定性測試事件類型
enum StabilityTestEventType {
  testStarted,
  testCompleted,
  testFailed,
  testStopped,
}
