import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../data/models/offline_chat_models.dart';
import 'cross_platform_chat_service.dart';
import 'session_manager.dart';
import 'message_manager.dart';
// import 'multi_device_manager.dart';

/// Android 統一服務
///
/// 根據使用場景自動選擇主機模式或客戶端模式：
/// - 創建聊天室時使用主機模式（WiFi 熱點）
/// - 加入聊天室時使用客戶端模式（連接到 iOS 熱點）
class AndroidHotspotService implements CrossPlatformChatService {
  static const MethodChannel _channel =
      MethodChannel('com.melo.offline_chat/wifi_hotspot');

  // 服務管理器
  final SessionManager _sessionManager = SessionManager();
  final MessageManager _messageManager = MessageManager();
  // final MultiDeviceManager _multiDeviceManager = MultiDeviceManager();

  // 狀態管理
  CrossPlatformChatStatus _status = CrossPlatformChatStatus.uninitialized;
  // final NetworkAdapterConfig _config = const NetworkAdapterConfig();

  // HTTP 服務器
  HttpServer? _httpServer;
  // String? _hotspotSSID;
  // String? _hotspotPassword;

  // 事件流控制器
  final StreamController<CrossPlatformChatStatus> _statusController =
      StreamController<CrossPlatformChatStatus>.broadcast();

  // Getters
  @override
  CrossPlatformChatStatus get status => _status;

  @override
  OfflineChatSession? get currentSession => _sessionManager.currentSession;

  @override
  List<OfflineChatMember> get connectedMembers => _sessionManager.members;

  @override
  List<DiscoveredChatRoom> get discoveredRooms =>
      _sessionManager.discoveredRooms;

  // Streams
  @override
  Stream<CrossPlatformChatStatus> get statusStream => _statusController.stream;

  @override
  Stream<List<DiscoveredChatRoom>> get discoveredRoomsStream =>
      _sessionManager.roomsStream;

  @override
  Stream<OfflineChatMessage> get messageStream => _messageManager.messageStream;

  @override
  Stream<List<OfflineChatMember>> get membersStream =>
      _sessionManager.membersStream;

  /// 初始化服務
  @override
  Future<bool> initialize() async {
    try {
      debugPrint('🔧 初始化 Android 熱點服務...');

      // 檢查權限
      if (!await _checkPermissions()) {
        debugPrint('❌ 權限檢查失敗');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }

      // 設置方法通道回調
      _setupMethodChannel();

      // 初始化原生服務
      final result = await _channel.invokeMethod('initialize');
      if (result['success'] == true) {
        _updateStatus(CrossPlatformChatStatus.ready);
        debugPrint('✅ Android 熱點服務初始化成功');
        return true;
      } else {
        debugPrint('❌ 原生服務初始化失敗: ${result['error']}');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }
    } catch (e) {
      debugPrint('❌ 初始化失敗: $e');
      _updateStatus(CrossPlatformChatStatus.error);
      return false;
    }
  }

  /// 創建聊天室
  @override
  Future<bool> createChatRoom(OfflineChatSession session) async {
    try {
      debugPrint('🏗️ 創建聊天室: ${session.roomName}');
      _updateStatus(CrossPlatformChatStatus.creatingRoom);

      // 創建 LocalOnlyHotspot
      final hotspotResult = await _createLocalOnlyHotspot();
      if (!hotspotResult) {
        debugPrint('❌ 創建熱點失敗');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }

      // 啟動 HTTP 服務器（使用不同端口避免衝突）
      if (!await _startHttpServer()) {
        debugPrint('❌ 啟動 HTTP 服務器失敗');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }

      // 設置會話
      _sessionManager.createSession(
        roomName: session.roomName,
        roomCode: session.roomCode,
        maxMembers: session.maxMembers,
      );

      _updateStatus(CrossPlatformChatStatus.hosting);
      debugPrint('✅ 聊天室創建成功，開始廣播');

      return true;
    } catch (e) {
      debugPrint('❌ 創建聊天室失敗: $e');
      _updateStatus(CrossPlatformChatStatus.error);
      return false;
    }
  }

  /// 掃描聊天室
  @override
  Future<bool> scanForChatRooms() async {
    try {
      debugPrint('🔍 開始掃描 WiFi 熱點...');
      _updateStatus(CrossPlatformChatStatus.scanning);

      // 掃描 WiFi 網絡
      final result = await _channel.invokeMethod('scanWifiNetworks');
      if (result['success'] == true) {
        final networks = result['networks'] as List<dynamic>;
        await _processDiscoveredNetworks(networks);
        return true;
      } else {
        debugPrint('❌ 掃描失敗: ${result['error']}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ 掃描聊天室失敗: $e');
      return false;
    }
  }

  /// 加入聊天室
  @override
  Future<bool> joinChatRoom(
    DiscoveredChatRoom room,
    String userNickname, [
    String? roomCode,
  ]) async {
    try {
      debugPrint('🚪 嘗試加入聊天室: ${room.roomName}');
      _updateStatus(CrossPlatformChatStatus.connecting);

      // 連接到 WiFi 熱點
      final connectResult = await _connectToHotspot(room);
      if (!connectResult) {
        debugPrint('❌ 連接熱點失敗');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }

      // 嘗試連接到 HTTP 服務器
      if (!await _connectToHttpServer(room, userNickname, roomCode)) {
        debugPrint('❌ 連接服務器失敗');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }

      // 加入會話
      _sessionManager.joinSession(room, userNickname);
      _updateStatus(CrossPlatformChatStatus.connected);

      debugPrint('✅ 成功加入聊天室');
      return true;
    } catch (e) {
      debugPrint('❌ 加入聊天室失敗: $e');
      _updateStatus(CrossPlatformChatStatus.error);
      return false;
    }
  }

  /// 發送消息
  @override
  Future<bool> sendMessage(OfflineChatMessage message) async {
    try {
      // 通過消息管理器發送
      await _messageManager.sendMessage(message);

      // 如果是主機，廣播給所有客戶端
      if (_sessionManager.isHost && _httpServer != null) {
        await _broadcastMessageToClients(message);
      } else {
        // 如果是客戶端，發送給服務器
        await _sendMessageToServer(message);
      }

      return true;
    } catch (e) {
      debugPrint('❌ 發送消息失敗: $e');
      return false;
    }
  }

  /// 斷開連接
  @override
  Future<void> disconnect() async {
    try {
      debugPrint('🚪 斷開連接...');

      // 停止 HTTP 服務器
      await _stopHttpServer();

      // 停止熱點
      await _stopHotspot();

      // 清理會話
      _sessionManager.leaveSession();

      _updateStatus(CrossPlatformChatStatus.ready);
      debugPrint('✅ 已斷開連接');
    } catch (e) {
      debugPrint('❌ 斷開連接失敗: $e');
    }
  }

  /// 釋放資源
  @override
  Future<void> dispose() async {
    await disconnect();
    await _statusController.close();
    await _sessionManager.dispose();
    await _messageManager.dispose();
  }

  // 私有方法

  /// 檢查權限
  Future<bool> _checkPermissions() async {
    final permissions = [
      Permission.location,
      Permission.locationWhenInUse,
    ];

    for (final permission in permissions) {
      final status = await permission.request();
      if (!status.isGranted) {
        debugPrint('❌ 權限被拒絕: $permission');
        return false;
      }
    }

    return true;
  }

  /// 設置方法通道回調
  void _setupMethodChannel() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'onHotspotStateChanged':
          _onHotspotStateChanged(call.arguments);
          break;
        case 'onWifiNetworkFound':
          _onWifiNetworkFound(call.arguments);
          break;
        case 'onConnectionStateChanged':
          _onConnectionStateChanged(call.arguments);
          break;
        default:
          debugPrint('⚠️ 未知方法調用: ${call.method}');
      }
    });
  }

  /// 創建 LocalOnlyHotspot
  Future<bool> _createLocalOnlyHotspot() async {
    try {
      // 使用現有的 createHotspot 方法
      final result = await _channel.invokeMethod('createHotspot', {
        'ssid': 'ChatRoom_${DateTime.now().millisecondsSinceEpoch}',
        'password': 'chatroom123',
        'sessionInfo': {
          'roomName':
              _sessionManager.currentSession?.roomName ?? 'Default Room',
          'roomCode': _sessionManager.currentSession?.roomCode ?? '123456',
        }
      });

      if (result['success'] == true) {
        debugPrint('📡 熱點創建成功');
        return true;
      } else {
        debugPrint('❌ 熱點創建失敗: ${result['error']}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ 創建熱點異常: $e');
      return false;
    }
  }

  /// 啟動 HTTP 服務器（使用端口 8889 避免與原生服務器衝突）
  Future<bool> _startHttpServer() async {
    try {
      // 使用端口 8889 避免與原生 TCP 服務器（8888）衝突
      const httpPort = 8889;
      _httpServer = await HttpServer.bind(InternetAddress.anyIPv4, httpPort);
      debugPrint('🌐 HTTP 服務器啟動在端口 $httpPort');

      _httpServer!.listen(_handleHttpRequest);
      return true;
    } catch (e) {
      debugPrint('❌ 啟動 HTTP 服務器失敗: $e');
      return false;
    }
  }

  /// 處理 HTTP 請求
  void _handleHttpRequest(HttpRequest request) {
    try {
      final uri = request.uri;
      debugPrint('📥 HTTP 請求: ${request.method} ${uri.path}');

      switch (uri.path) {
        case '/api/room/info':
          _handleRoomInfoRequest(request);
          break;
        case '/api/room/join':
          _handleJoinRoomRequest(request);
          break;
        case '/api/messages':
          if (request.method == 'GET') {
            _handleGetMessagesRequest(request);
          } else if (request.method == 'POST') {
            _handleSendMessageRequest(request);
          }
          break;
        case '/api/members':
          _handleGetMembersRequest(request);
          break;
        default:
          _sendHttpResponse(request, 404, {'error': 'Not found'});
      }
    } catch (e) {
      debugPrint('❌ 處理 HTTP 請求失敗: $e');
      _sendHttpResponse(request, 500, {'error': 'Internal server error'});
    }
  }

  /// 處理聊天室信息請求
  void _handleRoomInfoRequest(HttpRequest request) {
    final session = _sessionManager.currentSession;
    if (session == null) {
      _sendHttpResponse(request, 404, {'error': 'No active room'});
      return;
    }

    final roomInfo = {
      'sessionId': session.sessionId,
      'roomName': session.roomName,
      'roomCode': session.roomCode,
      'creatorName': session.creatorName,
      'memberCount': _sessionManager.members.length,
      'maxMembers': session.maxMembers,
      'createdAt': session.createdAt.toIso8601String(),
    };

    _sendHttpResponse(request, 200, roomInfo);
  }

  /// 發送 HTTP 響應
  void _sendHttpResponse(
      HttpRequest request, int statusCode, Map<String, dynamic> data) {
    request.response
      ..statusCode = statusCode
      ..headers.contentType = ContentType.json
      ..write(jsonEncode(data))
      ..close();
  }

  /// 更新狀態
  void _updateStatus(CrossPlatformChatStatus newStatus) {
    if (_status != newStatus) {
      _status = newStatus;
      _statusController.add(_status);
      debugPrint('📊 狀態更新: $newStatus');
    }
  }

  // 待實現的方法（將在後續添加）
  Future<void> _processDiscoveredNetworks(List<dynamic> networks) async {
    // TODO: 處理發現的網絡
  }

  Future<bool> _connectToHotspot(DiscoveredChatRoom room) async {
    // TODO: 連接到熱點
    return false;
  }

  Future<bool> _connectToHttpServer(
      DiscoveredChatRoom room, String userNickname, String? roomCode) async {
    // TODO: 連接到 HTTP 服務器
    return false;
  }

  Future<void> _broadcastMessageToClients(OfflineChatMessage message) async {
    // TODO: 廣播消息給客戶端
  }

  Future<void> _sendMessageToServer(OfflineChatMessage message) async {
    // TODO: 發送消息到服務器
  }

  Future<void> _stopHttpServer() async {
    await _httpServer?.close();
    _httpServer = null;
  }

  Future<void> _stopHotspot() async {
    try {
      await _channel.invokeMethod('stopHotspot');
    } catch (e) {
      debugPrint('⚠️ 停止熱點時出錯: $e');
    }
  }

  void _onHotspotStateChanged(Map<String, dynamic> args) {
    // TODO: 處理熱點狀態變化
  }

  void _onWifiNetworkFound(Map<String, dynamic> args) {
    // TODO: 處理發現的 WiFi 網絡
  }

  void _onConnectionStateChanged(Map<String, dynamic> args) {
    // TODO: 處理連接狀態變化
  }

  void _handleJoinRoomRequest(HttpRequest request) {
    // TODO: 處理加入聊天室請求
  }

  void _handleGetMessagesRequest(HttpRequest request) {
    // TODO: 處理獲取消息請求
  }

  void _handleSendMessageRequest(HttpRequest request) {
    // TODO: 處理發送消息請求
  }

  void _handleGetMembersRequest(HttpRequest request) {
    // TODO: 處理獲取成員請求
  }
}
