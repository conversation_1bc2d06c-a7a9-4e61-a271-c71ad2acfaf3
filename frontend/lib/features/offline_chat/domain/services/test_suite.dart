import 'dart:async';

import 'package:flutter/foundation.dart';

import '../../data/models/offline_chat_models.dart';
import 'cross_platform_chat_service.dart';

/// 離線聊天功能測試套件
///
/// 功能：
/// 1. 單設備功能測試
/// 2. 跨設備通信測試
/// 3. 多設備連接測試
/// 4. 性能和穩定性測試
class OfflineChatTestSuite {
  final CrossPlatformChatService _chatService;
  final List<TestResult> _testResults = [];

  // 測試配置
  final TestConfig _config;

  // 事件流
  final StreamController<TestEvent> _testEventController =
      StreamController<TestEvent>.broadcast();

  OfflineChatTestSuite(
    this._chatService, {
    TestConfig? config,
  }) : _config = config ?? const TestConfig();

  /// 測試事件流
  Stream<TestEvent> get testEventStream => _testEventController.stream;

  /// 測試結果列表
  List<TestResult> get testResults => List.unmodifiable(_testResults);

  /// 運行所有測試
  Future<TestSummary> runAllTests() async {
    debugPrint('🧪 開始運行離線聊天功能測試套件');
    _testResults.clear();

    final startTime = DateTime.now();

    try {
      // 1. 基礎功能測試
      await _runBasicTests();

      // 2. 連接功能測試
      await _runConnectionTests();

      // 3. 消息功能測試
      await _runMessageTests();

      // 4. 安全性測試
      await _runSecurityTests();

      // 5. 性能測試
      if (_config.enablePerformanceTests) {
        await _runPerformanceTests();
      }

      // 6. 穩定性測試
      if (_config.enableStabilityTests) {
        await _runStabilityTests();
      }
    } catch (e) {
      debugPrint('❌ 測試套件運行異常: $e');
      _addTestResult(TestResult(
        testName: 'Test Suite Execution',
        success: false,
        error: e.toString(),
        duration: DateTime.now().difference(startTime),
      ));
    }

    final endTime = DateTime.now();
    final summary = _generateTestSummary(startTime, endTime);

    debugPrint('🧪 測試套件運行完成');
    debugPrint('📊 測試結果: ${summary.passedTests}/${summary.totalTests} 通過');

    return summary;
  }

  /// 運行基礎功能測試
  Future<void> _runBasicTests() async {
    debugPrint('🔧 運行基礎功能測試...');

    // 測試服務初始化
    await _testServiceInitialization();

    // 測試狀態管理
    await _testStatusManagement();

    // 測試配置管理
    await _testConfigurationManagement();
  }

  /// 測試服務初始化
  Future<void> _testServiceInitialization() async {
    const testName = 'Service Initialization';
    final startTime = DateTime.now();

    try {
      _emitTestEvent(TestEventType.testStarted, testName);

      final success = await _chatService.initialize();

      _addTestResult(TestResult(
        testName: testName,
        success: success,
        duration: DateTime.now().difference(startTime),
        details: 'Service initialization ${success ? 'successful' : 'failed'}',
      ));

      _emitTestEvent(
        success ? TestEventType.testPassed : TestEventType.testFailed,
        testName,
      );
    } catch (e) {
      _addTestResult(TestResult(
        testName: testName,
        success: false,
        error: e.toString(),
        duration: DateTime.now().difference(startTime),
      ));

      _emitTestEvent(TestEventType.testFailed, testName);
    }
  }

  /// 測試狀態管理
  Future<void> _testStatusManagement() async {
    const testName = 'Status Management';
    final startTime = DateTime.now();

    try {
      _emitTestEvent(TestEventType.testStarted, testName);

      // 檢查初始狀態
      final initialStatus = _chatService.status;
      final expectedStatuses = [
        CrossPlatformChatStatus.ready,
        CrossPlatformChatStatus.uninitialized,
      ];

      final success = expectedStatuses.contains(initialStatus);

      _addTestResult(TestResult(
        testName: testName,
        success: success,
        duration: DateTime.now().difference(startTime),
        details: 'Initial status: $initialStatus',
      ));

      _emitTestEvent(
        success ? TestEventType.testPassed : TestEventType.testFailed,
        testName,
      );
    } catch (e) {
      _addTestResult(TestResult(
        testName: testName,
        success: false,
        error: e.toString(),
        duration: DateTime.now().difference(startTime),
      ));

      _emitTestEvent(TestEventType.testFailed, testName);
    }
  }

  /// 測試配置管理
  Future<void> _testConfigurationManagement() async {
    const testName = 'Configuration Management';
    final startTime = DateTime.now();

    try {
      _emitTestEvent(TestEventType.testStarted, testName);

      // 檢查基本屬性
      final connectedMembers = _chatService.connectedMembers;
      final discoveredRooms = _chatService.discoveredRooms;
      final currentSession = _chatService.currentSession;

      // 初始狀態應該為空
      final success = connectedMembers.isEmpty &&
          discoveredRooms.isEmpty &&
          currentSession == null;

      _addTestResult(TestResult(
        testName: testName,
        success: success,
        duration: DateTime.now().difference(startTime),
        details:
            'Members: ${connectedMembers.length}, Rooms: ${discoveredRooms.length}, Session: ${currentSession != null}',
      ));

      _emitTestEvent(
        success ? TestEventType.testPassed : TestEventType.testFailed,
        testName,
      );
    } catch (e) {
      _addTestResult(TestResult(
        testName: testName,
        success: false,
        error: e.toString(),
        duration: DateTime.now().difference(startTime),
      ));

      _emitTestEvent(TestEventType.testFailed, testName);
    }
  }

  /// 運行連接功能測試
  Future<void> _runConnectionTests() async {
    debugPrint('📡 運行連接功能測試...');

    // 測試聊天室創建
    await _testRoomCreation();

    // 測試聊天室掃描
    await _testRoomScanning();

    // 測試連接和斷開
    await _testConnectionLifecycle();
  }

  /// 測試聊天室創建
  Future<void> _testRoomCreation() async {
    const testName = 'Room Creation';
    final startTime = DateTime.now();

    try {
      _emitTestEvent(TestEventType.testStarted, testName);

      final session = OfflineChatSession(
        sessionId: 'test_session_${DateTime.now().millisecondsSinceEpoch}',
        roomName: 'Test Room',
        roomCode: 'test123',
        creatorId: 'test_creator',
        creatorName: 'Test Creator',
        createdAt: DateTime.now(),
        maxMembers: 8,
      );

      final success = await _chatService.createChatRoom(session);

      _addTestResult(TestResult(
        testName: testName,
        success: success,
        duration: DateTime.now().difference(startTime),
        details: 'Room creation ${success ? 'successful' : 'failed'}',
      ));

      _emitTestEvent(
        success ? TestEventType.testPassed : TestEventType.testFailed,
        testName,
      );
    } catch (e) {
      _addTestResult(TestResult(
        testName: testName,
        success: false,
        error: e.toString(),
        duration: DateTime.now().difference(startTime),
      ));

      _emitTestEvent(TestEventType.testFailed, testName);
    }
  }

  /// 測試聊天室掃描
  Future<void> _testRoomScanning() async {
    const testName = 'Room Scanning';
    final startTime = DateTime.now();

    try {
      _emitTestEvent(TestEventType.testStarted, testName);

      final success = await _chatService.scanForChatRooms();

      _addTestResult(TestResult(
        testName: testName,
        success: success,
        duration: DateTime.now().difference(startTime),
        details: 'Room scanning ${success ? 'successful' : 'failed'}',
      ));

      _emitTestEvent(
        success ? TestEventType.testPassed : TestEventType.testFailed,
        testName,
      );
    } catch (e) {
      _addTestResult(TestResult(
        testName: testName,
        success: false,
        error: e.toString(),
        duration: DateTime.now().difference(startTime),
      ));

      _emitTestEvent(TestEventType.testFailed, testName);
    }
  }

  /// 測試連接生命週期
  Future<void> _testConnectionLifecycle() async {
    const testName = 'Connection Lifecycle';
    final startTime = DateTime.now();

    try {
      _emitTestEvent(TestEventType.testStarted, testName);

      // 測試斷開連接
      await _chatService.disconnect();

      // 等待狀態穩定
      await Future.delayed(const Duration(seconds: 1));

      final finalStatus = _chatService.status;
      final success = finalStatus == CrossPlatformChatStatus.ready ||
          finalStatus == CrossPlatformChatStatus.disconnected;

      _addTestResult(TestResult(
        testName: testName,
        success: success,
        duration: DateTime.now().difference(startTime),
        details: 'Final status: $finalStatus',
      ));

      _emitTestEvent(
        success ? TestEventType.testPassed : TestEventType.testFailed,
        testName,
      );
    } catch (e) {
      _addTestResult(TestResult(
        testName: testName,
        success: false,
        error: e.toString(),
        duration: DateTime.now().difference(startTime),
      ));

      _emitTestEvent(TestEventType.testFailed, testName);
    }
  }

  /// 運行消息功能測試
  Future<void> _runMessageTests() async {
    debugPrint('💬 運行消息功能測試...');

    // 測試消息發送
    await _testMessageSending();
  }

  /// 測試消息發送
  Future<void> _testMessageSending() async {
    const testName = 'Message Sending';
    final startTime = DateTime.now();

    try {
      _emitTestEvent(TestEventType.testStarted, testName);

      final message = OfflineChatMessage(
        id: 'test_msg_${DateTime.now().millisecondsSinceEpoch}',
        senderId: 'test_sender',
        senderName: 'Test Sender',
        content: 'Test message content',
        type: OfflineMessageType.text,
        timestamp: DateTime.now(),
      );

      final success = await _chatService.sendMessage(message);

      _addTestResult(TestResult(
        testName: testName,
        success: success,
        duration: DateTime.now().difference(startTime),
        details: 'Message sending ${success ? 'successful' : 'failed'}',
      ));

      _emitTestEvent(
        success ? TestEventType.testPassed : TestEventType.testFailed,
        testName,
      );
    } catch (e) {
      _addTestResult(TestResult(
        testName: testName,
        success: false,
        error: e.toString(),
        duration: DateTime.now().difference(startTime),
      ));

      _emitTestEvent(TestEventType.testFailed, testName);
    }
  }

  /// 運行安全性測試
  Future<void> _runSecurityTests() async {
    debugPrint('🔒 運行安全性測試...');
    // 這裡可以添加安全性相關的測試
    // 例如：加密測試、身份驗證測試等
  }

  /// 運行性能測試
  Future<void> _runPerformanceTests() async {
    debugPrint('⚡ 運行性能測試...');
    // 這裡可以添加性能相關的測試
    // 例如：連接速度測試、消息延遲測試等
  }

  /// 運行穩定性測試
  Future<void> _runStabilityTests() async {
    debugPrint('🛡️ 運行穩定性測試...');
    // 這裡可以添加穩定性相關的測試
    // 例如：長時間運行測試、異常情況測試等
  }

  // 私有方法

  /// 添加測試結果
  void _addTestResult(TestResult result) {
    _testResults.add(result);
    debugPrint(
        '📊 測試結果: ${result.testName} - ${result.success ? '✅ 通過' : '❌ 失敗'}');
  }

  /// 發送測試事件
  void _emitTestEvent(TestEventType type, String testName) {
    _testEventController.add(TestEvent(
      type: type,
      testName: testName,
      timestamp: DateTime.now(),
    ));
  }

  /// 生成測試摘要
  TestSummary _generateTestSummary(DateTime startTime, DateTime endTime) {
    final totalTests = _testResults.length;
    final passedTests = _testResults.where((r) => r.success).length;
    final failedTests = totalTests - passedTests;

    return TestSummary(
      totalTests: totalTests,
      passedTests: passedTests,
      failedTests: failedTests,
      totalDuration: endTime.difference(startTime),
      testResults: _testResults,
    );
  }

  /// 釋放資源
  void dispose() {
    _testEventController.close();
    _testResults.clear();
  }
}

/// 測試配置
class TestConfig {
  final bool enablePerformanceTests;
  final bool enableStabilityTests;
  final Duration testTimeout;

  const TestConfig({
    this.enablePerformanceTests = false,
    this.enableStabilityTests = false,
    this.testTimeout = const Duration(minutes: 5),
  });
}

/// 測試結果
class TestResult {
  final String testName;
  final bool success;
  final String? error;
  final String? details;
  final Duration duration;
  final DateTime timestamp;

  TestResult({
    required this.testName,
    required this.success,
    this.error,
    this.details,
    required this.duration,
  }) : timestamp = DateTime.now();
}

/// 測試摘要
class TestSummary {
  final int totalTests;
  final int passedTests;
  final int failedTests;
  final Duration totalDuration;
  final List<TestResult> testResults;

  const TestSummary({
    required this.totalTests,
    required this.passedTests,
    required this.failedTests,
    required this.totalDuration,
    required this.testResults,
  });

  double get successRate => totalTests > 0 ? passedTests / totalTests : 0.0;
}

/// 測試事件
class TestEvent {
  final TestEventType type;
  final String testName;
  final DateTime timestamp;

  const TestEvent({
    required this.type,
    required this.testName,
    required this.timestamp,
  });
}

/// 測試事件類型
enum TestEventType {
  testStarted,
  testPassed,
  testFailed,
  testSkipped,
}
