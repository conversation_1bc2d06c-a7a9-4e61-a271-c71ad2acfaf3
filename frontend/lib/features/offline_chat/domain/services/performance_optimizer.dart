import 'dart:async';
import 'dart:math';

import 'package:flutter/foundation.dart';

/// 性能優化管理器
///
/// 功能：
/// 1. 電池消耗優化
/// 2. 連接速度優化
/// 3. 消息延遲優化
/// 4. 內存使用優化
class PerformanceOptimizer {
  // 性能配置
  // PerformanceConfig _config = const PerformanceConfig();

  // 性能統計
  final PerformanceStats _stats = PerformanceStats();

  // 自適應參數
  Duration _currentScanInterval = const Duration(seconds: 30);
  Duration _currentHeartbeatInterval = const Duration(seconds: 60);
  int _currentMessageBatchSize = 10;

  // 定時器
  Timer? _optimizationTimer;

  // 事件流
  final StreamController<PerformanceStats> _statsController =
      StreamController<PerformanceStats>.broadcast();

  /// 性能統計事件流
  Stream<PerformanceStats> get statsStream => _statsController.stream;

  /// 當前性能統計
  PerformanceStats get currentStats => _stats;

  /// 初始化性能優化器
  void initialize(PerformanceConfig config) {
    // _config = config;
    _startOptimizationTimer();
    debugPrint('⚡ 性能優化器已初始化');
  }

  /// 記錄掃描操作
  void recordScanOperation(Duration duration, int devicesFound) {
    _stats.scanOperations++;
    _stats.totalScanTime += duration.inMilliseconds;
    _stats.devicesDiscovered += devicesFound;

    // 自適應掃描間隔
    _adaptScanInterval(devicesFound);

    debugPrint('📊 掃描記錄: ${duration.inMilliseconds}ms, 發現 $devicesFound 設備');
  }

  /// 記錄連接操作
  void recordConnectionOperation(Duration duration, bool success) {
    _stats.connectionAttempts++;
    if (success) {
      _stats.successfulConnections++;
      _stats.totalConnectionTime += duration.inMilliseconds;
    } else {
      _stats.failedConnections++;
    }

    debugPrint('📊 連接記錄: ${duration.inMilliseconds}ms, 成功: $success');
  }

  /// 記錄消息操作
  void recordMessageOperation(Duration latency, bool success) {
    _stats.messagesSent++;
    if (success) {
      _stats.messagesDelivered++;
      _stats.totalMessageLatency += latency.inMilliseconds;
    } else {
      _stats.messagesFailed++;
    }

    debugPrint('📊 消息記錄: ${latency.inMilliseconds}ms, 成功: $success');
  }

  /// 記錄電池使用
  void recordBatteryUsage(double batteryLevel) {
    if (_stats.initialBatteryLevel == 0) {
      _stats.initialBatteryLevel = batteryLevel;
    }
    _stats.currentBatteryLevel = batteryLevel;

    final consumption = _stats.initialBatteryLevel - batteryLevel;
    if (consumption > 0) {
      _stats.batteryConsumption = consumption;
    }
  }

  /// 記錄內存使用
  void recordMemoryUsage(int memoryUsageMB) {
    _stats.memoryUsageMB = memoryUsageMB;
    if (memoryUsageMB > _stats.peakMemoryUsageMB) {
      _stats.peakMemoryUsageMB = memoryUsageMB;
    }
  }

  /// 獲取優化建議的掃描間隔
  Duration getOptimizedScanInterval() {
    return _currentScanInterval;
  }

  /// 獲取優化建議的心跳間隔
  Duration getOptimizedHeartbeatInterval() {
    return _currentHeartbeatInterval;
  }

  /// 獲取優化建議的消息批次大小
  int getOptimizedMessageBatchSize() {
    return _currentMessageBatchSize;
  }

  /// 獲取性能建議
  List<String> getPerformanceRecommendations() {
    final recommendations = <String>[];

    // 電池優化建議
    if (_stats.batteryConsumption > 0.1) {
      recommendations.add('建議增加掃描間隔以節省電池');
    }

    // 連接優化建議
    final connectionSuccessRate = _stats.connectionAttempts > 0
        ? _stats.successfulConnections / _stats.connectionAttempts
        : 0.0;
    if (connectionSuccessRate < 0.8) {
      recommendations.add('連接成功率較低，建議檢查網絡環境');
    }

    // 消息延遲建議
    final avgLatency = _stats.messagesDelivered > 0
        ? _stats.totalMessageLatency / _stats.messagesDelivered
        : 0.0;
    if (avgLatency > 1000) {
      recommendations.add('消息延遲較高，建議優化網絡連接');
    }

    // 內存使用建議
    if (_stats.memoryUsageMB > 100) {
      recommendations.add('內存使用較高，建議清理緩存');
    }

    return recommendations;
  }

  /// 應用性能優化
  void applyOptimizations() {
    // 根據統計數據調整參數
    _optimizeScanInterval();
    _optimizeHeartbeatInterval();
    _optimizeMessageBatching();

    debugPrint('⚡ 性能優化已應用');
  }

  /// 重置統計數據
  void resetStats() {
    _stats.reset();
    debugPrint('📊 性能統計已重置');
  }

  /// 釋放資源
  void dispose() {
    _optimizationTimer?.cancel();
    _statsController.close();
  }

  // 私有方法

  /// 啟動優化定時器
  void _startOptimizationTimer() {
    _optimizationTimer = Timer.periodic(
      const Duration(minutes: 5),
      (timer) {
        applyOptimizations();
        _statsController.add(_stats);
      },
    );
  }

  /// 自適應掃描間隔
  void _adaptScanInterval(int devicesFound) {
    if (devicesFound > 3) {
      // 發現很多設備，可以減少掃描頻率
      _currentScanInterval = Duration(
        seconds: min(120, _currentScanInterval.inSeconds + 10),
      );
    } else if (devicesFound == 0) {
      // 沒有發現設備，增加掃描頻率
      _currentScanInterval = Duration(
        seconds: max(10, _currentScanInterval.inSeconds - 5),
      );
    }
  }

  /// 優化掃描間隔
  void _optimizeScanInterval() {
    // 根據電池消耗調整
    if (_stats.batteryConsumption > 0.05) {
      _currentScanInterval = Duration(
        seconds: min(300, _currentScanInterval.inSeconds * 2),
      );
    }
  }

  /// 優化心跳間隔
  void _optimizeHeartbeatInterval() {
    // 根據連接穩定性調整
    final connectionSuccessRate = _stats.connectionAttempts > 0
        ? _stats.successfulConnections / _stats.connectionAttempts
        : 1.0;

    if (connectionSuccessRate > 0.9) {
      // 連接穩定，可以增加心跳間隔
      _currentHeartbeatInterval = Duration(
        seconds: min(120, _currentHeartbeatInterval.inSeconds + 10),
      );
    } else {
      // 連接不穩定，減少心跳間隔
      _currentHeartbeatInterval = Duration(
        seconds: max(30, _currentHeartbeatInterval.inSeconds - 10),
      );
    }
  }

  /// 優化消息批處理
  void _optimizeMessageBatching() {
    final avgLatency = _stats.messagesDelivered > 0
        ? _stats.totalMessageLatency / _stats.messagesDelivered
        : 0.0;

    if (avgLatency > 500) {
      // 延遲較高，減少批次大小
      _currentMessageBatchSize = max(1, _currentMessageBatchSize - 2);
    } else if (avgLatency < 100) {
      // 延遲較低，可以增加批次大小
      _currentMessageBatchSize = min(50, _currentMessageBatchSize + 5);
    }
  }
}

/// 性能配置
class PerformanceConfig {
  final bool enableBatteryOptimization;
  final bool enableConnectionOptimization;
  final bool enableMessageOptimization;
  final bool enableMemoryOptimization;
  final Duration maxScanInterval;
  final Duration minScanInterval;
  final Duration maxHeartbeatInterval;
  final Duration minHeartbeatInterval;

  const PerformanceConfig({
    this.enableBatteryOptimization = true,
    this.enableConnectionOptimization = true,
    this.enableMessageOptimization = true,
    this.enableMemoryOptimization = true,
    this.maxScanInterval = const Duration(minutes: 5),
    this.minScanInterval = const Duration(seconds: 10),
    this.maxHeartbeatInterval = const Duration(minutes: 2),
    this.minHeartbeatInterval = const Duration(seconds: 30),
  });
}

/// 性能統計
class PerformanceStats {
  // 掃描統計
  int scanOperations = 0;
  int totalScanTime = 0; // 毫秒
  int devicesDiscovered = 0;

  // 連接統計
  int connectionAttempts = 0;
  int successfulConnections = 0;
  int failedConnections = 0;
  int totalConnectionTime = 0; // 毫秒

  // 消息統計
  int messagesSent = 0;
  int messagesDelivered = 0;
  int messagesFailed = 0;
  int totalMessageLatency = 0; // 毫秒

  // 電池統計
  double initialBatteryLevel = 0.0;
  double currentBatteryLevel = 0.0;
  double batteryConsumption = 0.0;

  // 內存統計
  int memoryUsageMB = 0;
  int peakMemoryUsageMB = 0;

  /// 獲取平均掃描時間
  double get averageScanTime {
    return scanOperations > 0 ? totalScanTime / scanOperations : 0.0;
  }

  /// 獲取平均連接時間
  double get averageConnectionTime {
    return successfulConnections > 0
        ? totalConnectionTime / successfulConnections
        : 0.0;
  }

  /// 獲取平均消息延遲
  double get averageMessageLatency {
    return messagesDelivered > 0
        ? totalMessageLatency / messagesDelivered
        : 0.0;
  }

  /// 獲取連接成功率
  double get connectionSuccessRate {
    return connectionAttempts > 0
        ? successfulConnections / connectionAttempts
        : 0.0;
  }

  /// 獲取消息成功率
  double get messageSuccessRate {
    return messagesSent > 0 ? messagesDelivered / messagesSent : 0.0;
  }

  /// 重置統計數據
  void reset() {
    scanOperations = 0;
    totalScanTime = 0;
    devicesDiscovered = 0;
    connectionAttempts = 0;
    successfulConnections = 0;
    failedConnections = 0;
    totalConnectionTime = 0;
    messagesSent = 0;
    messagesDelivered = 0;
    messagesFailed = 0;
    totalMessageLatency = 0;
    initialBatteryLevel = 0.0;
    currentBatteryLevel = 0.0;
    batteryConsumption = 0.0;
    memoryUsageMB = 0;
    peakMemoryUsageMB = 0;
  }

  /// 轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'scanOperations': scanOperations,
      'averageScanTime': averageScanTime,
      'devicesDiscovered': devicesDiscovered,
      'connectionAttempts': connectionAttempts,
      'connectionSuccessRate': connectionSuccessRate,
      'averageConnectionTime': averageConnectionTime,
      'messagesSent': messagesSent,
      'messageSuccessRate': messageSuccessRate,
      'averageMessageLatency': averageMessageLatency,
      'batteryConsumption': batteryConsumption,
      'memoryUsageMB': memoryUsageMB,
      'peakMemoryUsageMB': peakMemoryUsageMB,
    };
  }
}
