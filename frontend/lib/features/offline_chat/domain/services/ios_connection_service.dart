import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import '../../data/models/offline_chat_models.dart';
import 'session_manager.dart';
// import 'message_manager.dart';
import 'cross_platform_chat_service.dart';

/// iOS WiFi 連接服務
///
/// 功能：
/// 1. 連接到 Android 設備創建的 WiFi 熱點
/// 2. 通過 HTTP 客戶端與 Android 服務器通信
/// 3. 處理聊天室加入和消息收發
class IOSConnectionService implements CrossPlatformChatService {
  static const MethodChannel _channel =
      MethodChannel('com.melo.offline_chat/wifi_connection');

  // 服務管理器
  final SessionManager _sessionManager = SessionManager();
  // final MessageManager _messageManager = MessageManager();

  // 狀態管理
  CrossPlatformChatStatus _status = CrossPlatformChatStatus.uninitialized;

  // 連接信息
  // String? _connectedSSID;
  String? _serverBaseURL;
  bool _isConnected = false;

  // 事件流控制器
  final StreamController<CrossPlatformChatStatus> _statusController =
      StreamController<CrossPlatformChatStatus>.broadcast();
  final StreamController<List<DiscoveredChatRoom>> _roomsController =
      StreamController<List<DiscoveredChatRoom>>.broadcast();
  final StreamController<OfflineChatMessage> _messageController =
      StreamController<OfflineChatMessage>.broadcast();
  final StreamController<List<OfflineChatMember>> _membersController =
      StreamController<List<OfflineChatMember>>.broadcast();

  @override
  CrossPlatformChatStatus get status => _status;

  @override
  OfflineChatSession? get currentSession => _sessionManager.currentSession;

  @override
  List<OfflineChatMember> get connectedMembers => _sessionManager.members;

  @override
  List<DiscoveredChatRoom> get discoveredRooms =>
      _sessionManager.discoveredRooms;

  @override
  Stream<CrossPlatformChatStatus> get statusStream => _statusController.stream;

  @override
  Stream<List<DiscoveredChatRoom>> get discoveredRoomsStream =>
      _roomsController.stream;

  @override
  Stream<OfflineChatMessage> get messageStream => _messageController.stream;

  @override
  Stream<List<OfflineChatMember>> get membersStream =>
      _membersController.stream;

  /// 初始化服務
  @override
  Future<bool> initialize() async {
    try {
      debugPrint('🍎 初始化 iOS 連接服務...');
      _updateStatus(CrossPlatformChatStatus.uninitialized);

      // 檢查平台支持
      if (!Platform.isIOS) {
        debugPrint('❌ 當前平台不是 iOS');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }

      // 設置方法通道回調
      _setupMethodChannel();

      // 初始化原生服務
      final result = await _channel.invokeMethod('initialize');
      if (result['success'] == true) {
        _updateStatus(CrossPlatformChatStatus.ready);
        debugPrint('✅ iOS 連接服務初始化成功');
        return true;
      } else {
        debugPrint('❌ 原生服務初始化失敗: ${result['error']}');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }
    } catch (e) {
      debugPrint('❌ iOS 連接服務初始化異常: $e');
      _updateStatus(CrossPlatformChatStatus.error);
      return false;
    }
  }

  /// 創建聊天室（iOS 不支持，返回 false）
  @override
  Future<bool> createChatRoom(OfflineChatSession session) async {
    debugPrint('❌ iOS 設備不支持創建聊天室，請使用 Android 設備作為主機');
    return false;
  }

  /// 掃描聊天室
  @override
  Future<bool> scanForChatRooms() async {
    try {
      debugPrint('🔍 開始掃描 WiFi 網絡...');
      _updateStatus(CrossPlatformChatStatus.scanning);

      // 掃描 WiFi 網絡
      final result = await _channel.invokeMethod('scanWifiNetworks');
      if (result['success'] == true) {
        final networks = result['networks'] as List<dynamic>? ?? [];
        debugPrint('📡 發現 ${networks.length} 個 WiFi 網絡');

        // 過濾出可能的聊天室熱點
        final chatRooms = _filterChatRoomNetworks(networks);

        // 清空舊的發現列表並添加新的
        _sessionManager.clearDiscoveredRooms();
        for (final room in chatRooms) {
          _sessionManager.addDiscoveredRoom(room);
        }

        _roomsController.add(chatRooms);

        _updateStatus(CrossPlatformChatStatus.ready);
        return true;
      } else {
        debugPrint('❌ WiFi 掃描失敗: ${result['error']}');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }
    } catch (e) {
      debugPrint('❌ 掃描 WiFi 網絡異常: $e');
      _updateStatus(CrossPlatformChatStatus.error);
      return false;
    }
  }

  /// 加入聊天室
  @override
  Future<bool> joinChatRoom(
    DiscoveredChatRoom room,
    String userNickname, [
    String? roomCode,
  ]) async {
    try {
      debugPrint('🔗 嘗試加入聊天室: ${room.roomName}');
      _updateStatus(CrossPlatformChatStatus.connecting);

      // 連接到 WiFi 熱點
      final connectResult =
          await _connectToWifi(room.roomName, roomCode ?? 'chatroom123');
      if (!connectResult) {
        debugPrint('❌ WiFi 連接失敗');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }

      // 等待連接穩定
      await Future.delayed(const Duration(seconds: 2));

      // 嘗試連接到聊天室服務器
      final joinResult =
          await _joinChatRoomServer(room, userNickname, roomCode);
      if (joinResult) {
        _updateStatus(CrossPlatformChatStatus.connected);
        debugPrint('✅ 成功加入聊天室: ${room.roomName}');
        return true;
      } else {
        debugPrint('❌ 加入聊天室服務器失敗');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }
    } catch (e) {
      debugPrint('❌ 加入聊天室異常: $e');
      _updateStatus(CrossPlatformChatStatus.error);
      return false;
    }
  }

  /// 發送消息
  @override
  Future<bool> sendMessage(OfflineChatMessage message) async {
    try {
      if (!_isConnected || _serverBaseURL == null) {
        debugPrint('❌ 未連接到聊天室服務器');
        return false;
      }

      debugPrint('📤 發送消息: ${message.content}');

      // 通過 HTTP 發送消息到 Android 服務器
      final result = await _channel.invokeMethod('sendHttpRequest', {
        'endpoint': '/api/messages/send',
        'method': 'POST',
        'body': {
          'id': message.id,
          'content': message.content,
          'senderId': message.senderId,
          'senderName': message.senderName,
          'timestamp': message.timestamp.millisecondsSinceEpoch,
          'type': message.type.toString(),
        }
      });

      if (result['success'] == true) {
        debugPrint('✅ 消息發送成功');
        return true;
      } else {
        debugPrint('❌ 消息發送失敗: ${result['error']}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ 發送消息異常: $e');
      return false;
    }
  }

  /// 斷開連接
  @override
  Future<void> disconnect() async {
    try {
      debugPrint('🔌 斷開 iOS 連接...');

      // 斷開 WiFi 連接
      await _channel.invokeMethod('disconnectWifi');

      // 清理狀態
      _isConnected = false;
      // _connectedSSID = null;
      _serverBaseURL = null;
      _sessionManager.leaveSession();

      _updateStatus(CrossPlatformChatStatus.disconnected);

      // 短暫延遲後恢復到就緒狀態
      await Future.delayed(const Duration(milliseconds: 500));
      _updateStatus(CrossPlatformChatStatus.ready);

      debugPrint('✅ iOS 連接已斷開');
    } catch (e) {
      debugPrint('❌ 斷開連接異常: $e');
    }
  }

  /// 釋放資源
  @override
  Future<void> dispose() async {
    await disconnect();
    await _statusController.close();
    await _roomsController.close();
    await _messageController.close();
    await _membersController.close();
  }

  /// 設置方法通道回調
  void _setupMethodChannel() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'onConnectionStateChanged':
          _onConnectionStateChanged(call.arguments);
          break;
        case 'onMessageReceived':
          _onMessageReceived(call.arguments);
          break;
        case 'onMemberJoined':
          _onMemberJoined(call.arguments);
          break;
        case 'onMemberLeft':
          _onMemberLeft(call.arguments);
          break;
        default:
          debugPrint('🤷‍♂️ 未知的方法調用: ${call.method}');
      }
    });
  }

  /// 連接到 WiFi 熱點
  Future<bool> _connectToWifi(String ssid, String password) async {
    try {
      final result = await _channel.invokeMethod('connectToWifi', {
        'ssid': ssid,
        'password': password,
      });

      if (result['success'] == true) {
        // _connectedSSID = ssid;
        _isConnected = true;
        debugPrint('📡 WiFi 連接成功: $ssid');
        return true;
      } else {
        debugPrint('❌ WiFi 連接失敗: ${result['error']}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ WiFi 連接異常: $e');
      return false;
    }
  }

  /// 加入聊天室服務器
  Future<bool> _joinChatRoomServer(
    DiscoveredChatRoom room,
    String userNickname,
    String? roomCode,
  ) async {
    try {
      final result = await _channel.invokeMethod('sendHttpRequest', {
        'endpoint': '/api/room/join',
        'method': 'POST',
        'body': {
          'roomId': room.sessionId,
          'userName': userNickname,
          'roomCode': roomCode,
          'deviceInfo': {
            'platform': 'iOS',
            'deviceName': 'iOS Device',
          }
        }
      });

      if (result['success'] == true && result['statusCode'] == 200) {
        final data = result['data'] as Map<String, dynamic>;

        // 使用 joinSession 方法來設置會話
        _sessionManager.joinSession(room, 'iOS User');

        // 處理成員列表
        if (data['members'] != null) {
          final members = (data['members'] as List)
              .map((m) => OfflineChatMember.fromJson(m))
              .toList();

          // 添加每個成員
          for (final member in members) {
            _sessionManager.addMember(member);
          }

          _membersController.add(_sessionManager.members);
        }

        return true;
      } else {
        debugPrint('❌ 加入聊天室服務器失敗: ${result['error']}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ 加入聊天室服務器異常: $e');
      return false;
    }
  }

  /// 過濾聊天室網絡
  List<DiscoveredChatRoom> _filterChatRoomNetworks(List<dynamic> networks) {
    final chatRooms = <DiscoveredChatRoom>[];

    for (final network in networks) {
      if (network is Map<String, dynamic>) {
        final ssid = network['ssid'] as String?;
        if (ssid != null && ssid.startsWith('ChatRoom_')) {
          // 這是一個聊天室熱點
          chatRooms.add(DiscoveredChatRoom(
            sessionId: ssid,
            roomName: ssid.replaceFirst('ChatRoom_', ''),
            creatorName: 'Android Host',
            memberCount: 1,
            maxMembers: 8,
            deviceId: 'android-host',
            signalStrength: -50, // 默認信號強度
            lastSeen: DateTime.now(),
          ));
        }
      }
    }

    return chatRooms;
  }

  /// 更新狀態
  void _updateStatus(CrossPlatformChatStatus newStatus) {
    if (_status != newStatus) {
      _status = newStatus;
      _statusController.add(_status);
      debugPrint('📱 iOS 連接狀態變更: $newStatus');
    }
  }

  /// 處理連接狀態變化
  void _onConnectionStateChanged(dynamic arguments) {
    if (arguments is Map<String, dynamic>) {
      final status = arguments['status'] as String?;
      final isConnected = arguments['isConnected'] as bool? ?? false;
      final serverURL = arguments['serverURL'] as String?;

      _isConnected = isConnected;
      _serverBaseURL = serverURL;

      debugPrint('📱 連接狀態變化: $status, 已連接: $isConnected');
    }
  }

  /// 處理接收到的消息
  void _onMessageReceived(dynamic arguments) {
    if (arguments is Map<String, dynamic>) {
      try {
        final message = OfflineChatMessage.fromJson(arguments);
        _messageController.add(message);
        debugPrint('📥 收到消息: ${message.content}');
      } catch (e) {
        debugPrint('❌ 解析消息失敗: $e');
      }
    }
  }

  /// 處理成員加入
  void _onMemberJoined(dynamic arguments) {
    if (arguments is Map<String, dynamic>) {
      try {
        final member = OfflineChatMember.fromJson(arguments);
        _sessionManager.addMember(member);
        _membersController.add(_sessionManager.members);
        debugPrint('👋 成員加入: ${member.userName}');
      } catch (e) {
        debugPrint('❌ 解析成員信息失敗: $e');
      }
    }
  }

  /// 處理成員離開
  void _onMemberLeft(dynamic arguments) {
    if (arguments is Map<String, dynamic>) {
      final deviceId = arguments['deviceId'] as String?;
      if (deviceId != null) {
        _sessionManager.removeMember(deviceId);
        _membersController.add(_sessionManager.members);
        debugPrint('👋 成員離開: $deviceId');
      }
    }
  }
}
