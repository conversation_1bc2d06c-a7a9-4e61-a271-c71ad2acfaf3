import 'dart:convert';
import 'dart:math';

import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';

import '../../data/models/offline_chat_models.dart';

/// 安全管理器
///
/// 功能：
/// 1. 消息加密/解密
/// 2. 房間碼驗證
/// 3. 設備身份驗證
/// 4. 防重放攻擊
class SecurityManager {
  // 加密密鑰
  String? _encryptionKey;

  // 房間碼
  String? _roomCode;

  // 設備身份
  String? _deviceId;
  String? _deviceSecret;

  // 防重放攻擊
  final Set<String> _usedNonces = {};
  final int _maxNonceAge = 300000; // 5分鐘

  // 配置
  final bool _enableEncryption;
  final bool _enableAuthentication;
  final bool _enableAntiReplay;

  SecurityManager({
    bool enableEncryption = true,
    bool enableAuthentication = true,
    bool enableAntiReplay = true,
  })  : _enableEncryption = enableEncryption,
        _enableAuthentication = enableAuthentication,
        _enableAntiReplay = enableAntiReplay;

  /// 初始化安全管理器
  void initialize({
    String? roomCode,
    String? deviceId,
  }) {
    _roomCode = roomCode;
    _deviceId = deviceId ?? _generateDeviceId();
    _deviceSecret = _generateDeviceSecret();

    if (_enableEncryption && roomCode != null) {
      _encryptionKey = _deriveEncryptionKey(roomCode);
    }

    debugPrint('🔐 安全管理器已初始化');
  }

  /// 加密消息
  EncryptedMessage? encryptMessage(OfflineChatMessage message) {
    if (!_enableEncryption || _encryptionKey == null) {
      return EncryptedMessage(
        encryptedContent: message.content,
        nonce: _generateNonce(),
        signature: null,
        isEncrypted: false,
      );
    }

    try {
      final nonce = _generateNonce();
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();

      // 創建要加密的數據
      final plainData = {
        'content': message.content,
        'senderId': message.senderId,
        'senderName': message.senderName,
        'timestamp': timestamp,
        'nonce': nonce,
      };

      final plainText = jsonEncode(plainData);
      final encryptedContent = _simpleEncrypt(plainText, _encryptionKey!);

      // 生成簽名
      String? signature;
      if (_enableAuthentication && _deviceSecret != null) {
        signature = _generateSignature(encryptedContent, nonce);
      }

      return EncryptedMessage(
        encryptedContent: encryptedContent,
        nonce: nonce,
        signature: signature,
        isEncrypted: true,
      );
    } catch (e) {
      debugPrint('❌ 消息加密失敗: $e');
      return null;
    }
  }

  /// 解密消息
  OfflineChatMessage? decryptMessage(EncryptedMessage encryptedMessage) {
    if (!encryptedMessage.isEncrypted) {
      // 未加密的消息，直接返回
      return OfflineChatMessage(
        id: _generateMessageId(),
        senderId: 'unknown',
        senderName: 'Unknown',
        content: encryptedMessage.encryptedContent,
        type: OfflineMessageType.text,
        timestamp: DateTime.now(),
      );
    }

    if (!_enableEncryption || _encryptionKey == null) {
      debugPrint('❌ 加密未啟用或缺少密鑰');
      return null;
    }

    try {
      // 驗證簽名
      if (_enableAuthentication && encryptedMessage.signature != null) {
        if (!_verifySignature(
          encryptedMessage.encryptedContent,
          encryptedMessage.nonce,
          encryptedMessage.signature!,
        )) {
          debugPrint('❌ 消息簽名驗證失敗');
          return null;
        }
      }

      // 防重放攻擊檢查
      if (_enableAntiReplay) {
        if (_usedNonces.contains(encryptedMessage.nonce)) {
          debugPrint('❌ 檢測到重放攻擊');
          return null;
        }
        _usedNonces.add(encryptedMessage.nonce);
        _cleanupOldNonces();
      }

      // 解密內容
      final plainText = _simpleDecrypt(
        encryptedMessage.encryptedContent,
        _encryptionKey!,
      );

      final plainData = jsonDecode(plainText) as Map<String, dynamic>;

      // 驗證時間戳（防止過期消息）
      final timestamp = int.tryParse(plainData['timestamp'] ?? '0') ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now - timestamp > _maxNonceAge) {
        debugPrint('❌ 消息已過期');
        return null;
      }

      return OfflineChatMessage(
        id: _generateMessageId(),
        senderId: plainData['senderId'] ?? 'unknown',
        senderName: plainData['senderName'] ?? 'Unknown',
        content: plainData['content'] ?? '',
        type: OfflineMessageType.text,
        timestamp: DateTime.fromMillisecondsSinceEpoch(timestamp),
      );
    } catch (e) {
      debugPrint('❌ 消息解密失敗: $e');
      return null;
    }
  }

  /// 驗證房間碼
  bool verifyRoomCode(String roomCode) {
    if (!_enableAuthentication) {
      return true;
    }

    return _roomCode == null || _roomCode == roomCode;
  }

  /// 生成設備認證令牌
  String generateDeviceToken() {
    if (!_enableAuthentication || _deviceId == null || _deviceSecret == null) {
      return '';
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final nonce = _generateNonce();

    final data = '$_deviceId:$timestamp:$nonce';
    final signature = _hmacSha256(data, _deviceSecret!);

    return base64Encode(utf8.encode('$data:$signature'));
  }

  /// 驗證設備認證令牌
  bool verifyDeviceToken(String token) {
    if (!_enableAuthentication) {
      return true;
    }

    try {
      final decoded = utf8.decode(base64Decode(token));
      final parts = decoded.split(':');

      if (parts.length != 4) {
        return false;
      }

      final deviceId = parts[0];
      final timestamp = int.tryParse(parts[1]) ?? 0;
      final nonce = parts[2];
      final signature = parts[3];

      // 檢查時間戳
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now - timestamp > _maxNonceAge) {
        return false;
      }

      // 驗證簽名（這裡簡化處理，實際應該有設備密鑰管理）
      final data = '$deviceId:$timestamp:$nonce';
      final expectedSignature = _hmacSha256(data, _deviceSecret ?? '');

      return signature == expectedSignature;
    } catch (e) {
      debugPrint('❌ 設備令牌驗證失敗: $e');
      return false;
    }
  }

  /// 重置安全管理器
  void reset() {
    _encryptionKey = null;
    _roomCode = null;
    _deviceId = null;
    _deviceSecret = null;
    _usedNonces.clear();
    debugPrint('🔐 安全管理器已重置');
  }

  // 私有方法

  /// 生成設備ID
  String _generateDeviceId() {
    final random = Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    return base64Encode(bytes).replaceAll('/', '_').replaceAll('+', '-');
  }

  /// 生成設備密鑰
  String _generateDeviceSecret() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Encode(bytes);
  }

  /// 從房間碼派生加密密鑰
  String _deriveEncryptionKey(String roomCode) {
    const salt = 'melo_offline_chat_salt';
    final input = utf8.encode(roomCode + salt);
    final digest = sha256.convert(input);
    return base64Encode(digest.bytes);
  }

  /// 生成隨機數
  String _generateNonce() {
    final random = Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    return base64Encode(bytes);
  }

  /// 生成消息ID
  String _generateMessageId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(9999);
    return 'msg_${timestamp}_$random';
  }

  /// 簡單加密（XOR + Base64）
  String _simpleEncrypt(String plainText, String key) {
    final plainBytes = utf8.encode(plainText);
    final keyBytes = utf8.encode(key);
    final encryptedBytes = <int>[];

    for (int i = 0; i < plainBytes.length; i++) {
      encryptedBytes.add(plainBytes[i] ^ keyBytes[i % keyBytes.length]);
    }

    return base64Encode(encryptedBytes);
  }

  /// 簡單解密（XOR + Base64）
  String _simpleDecrypt(String encryptedText, String key) {
    final encryptedBytes = base64Decode(encryptedText);
    final keyBytes = utf8.encode(key);
    final plainBytes = <int>[];

    for (int i = 0; i < encryptedBytes.length; i++) {
      plainBytes.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
    }

    return utf8.decode(plainBytes);
  }

  /// 生成HMAC-SHA256簽名
  String _hmacSha256(String data, String key) {
    final keyBytes = utf8.encode(key);
    final dataBytes = utf8.encode(data);
    final hmac = Hmac(sha256, keyBytes);
    final digest = hmac.convert(dataBytes);
    return base64Encode(digest.bytes);
  }

  /// 生成簽名
  String _generateSignature(String content, String nonce) {
    final data = '$content:$nonce:$_deviceId';
    return _hmacSha256(data, _deviceSecret!);
  }

  /// 驗證簽名
  bool _verifySignature(String content, String nonce, String signature) {
    final expectedSignature = _generateSignature(content, nonce);
    return signature == expectedSignature;
  }

  /// 清理舊的隨機數
  void _cleanupOldNonces() {
    if (_usedNonces.length > 1000) {
      final toRemove = _usedNonces.take(500).toList();
      for (final nonce in toRemove) {
        _usedNonces.remove(nonce);
      }
    }
  }
}

/// 加密消息
class EncryptedMessage {
  final String encryptedContent;
  final String nonce;
  final String? signature;
  final bool isEncrypted;

  const EncryptedMessage({
    required this.encryptedContent,
    required this.nonce,
    this.signature,
    required this.isEncrypted,
  });

  Map<String, dynamic> toJson() {
    return {
      'encryptedContent': encryptedContent,
      'nonce': nonce,
      'signature': signature,
      'isEncrypted': isEncrypted,
    };
  }

  factory EncryptedMessage.fromJson(Map<String, dynamic> json) {
    return EncryptedMessage(
      encryptedContent: json['encryptedContent'] ?? '',
      nonce: json['nonce'] ?? '',
      signature: json['signature'],
      isEncrypted: json['isEncrypted'] ?? false,
    );
  }
}
