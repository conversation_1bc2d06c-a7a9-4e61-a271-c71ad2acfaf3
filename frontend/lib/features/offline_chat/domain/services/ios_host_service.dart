import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import '../../data/models/offline_chat_models.dart';
import 'session_manager.dart';
import 'message_manager.dart';
import 'cross_platform_chat_service.dart';

/// iOS 主機服務
///
/// 功能：
/// 1. 管理 iOS Personal Hotspot 功能
/// 2. 創建 HTTP 服務器供 Android 設備連接
/// 3. 處理聊天室創建和成員管理
class IOSHostService implements CrossPlatformChatService {
  static const MethodChannel _channel =
      MethodChannel('com.melo.offline_chat/ios_hotspot');

  // 服務管理器
  final SessionManager _sessionManager = SessionManager();
  final MessageManager _messageManager = MessageManager();

  // 狀態管理
  CrossPlatformChatStatus _status = CrossPlatformChatStatus.uninitialized;

  // 服務器狀態
  bool _isServerRunning = false;
  // int _connectedClientsCount = 0;

  // 事件流控制器
  final StreamController<CrossPlatformChatStatus> _statusController =
      StreamController<CrossPlatformChatStatus>.broadcast();
  final StreamController<List<DiscoveredChatRoom>> _roomsController =
      StreamController<List<DiscoveredChatRoom>>.broadcast();
  final StreamController<OfflineChatMessage> _messageController =
      StreamController<OfflineChatMessage>.broadcast();
  final StreamController<List<OfflineChatMember>> _membersController =
      StreamController<List<OfflineChatMember>>.broadcast();

  @override
  CrossPlatformChatStatus get status => _status;

  @override
  OfflineChatSession? get currentSession => _sessionManager.currentSession;

  @override
  List<OfflineChatMember> get connectedMembers => _sessionManager.members;

  @override
  List<DiscoveredChatRoom> get discoveredRooms =>
      _sessionManager.discoveredRooms;

  @override
  Stream<CrossPlatformChatStatus> get statusStream => _statusController.stream;

  @override
  Stream<List<DiscoveredChatRoom>> get discoveredRoomsStream =>
      _roomsController.stream;

  @override
  Stream<OfflineChatMessage> get messageStream => _messageController.stream;

  @override
  Stream<List<OfflineChatMember>> get membersStream =>
      _membersController.stream;

  /// 初始化服務
  @override
  Future<bool> initialize() async {
    try {
      debugPrint('🍎 初始化 iOS 主機服務...');
      _updateStatus(CrossPlatformChatStatus.uninitialized);

      // 檢查平台支持
      if (!Platform.isIOS) {
        debugPrint('❌ 當前平台不是 iOS');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }

      // 設置方法通道回調
      _setupMethodChannel();

      // 初始化原生服務
      final result = await _channel.invokeMethod('initialize');
      if (result['success'] == true) {
        _updateStatus(CrossPlatformChatStatus.ready);
        debugPrint('✅ iOS 主機服務初始化成功');
        return true;
      } else {
        debugPrint('❌ 原生服務初始化失敗: ${result['error']}');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }
    } catch (e) {
      debugPrint('❌ iOS 主機服務初始化異常: $e');
      _updateStatus(CrossPlatformChatStatus.error);
      return false;
    }
  }

  /// 創建聊天室
  @override
  Future<bool> createChatRoom(OfflineChatSession session) async {
    try {
      debugPrint('🏗️ 創建 iOS 聊天室: ${session.roomName}');
      _updateStatus(CrossPlatformChatStatus.creatingRoom);

      // 創建會話
      final createdSession = _sessionManager.createSession(
        roomName: session.roomName,
        roomCode: session.roomCode,
        maxMembers: session.maxMembers,
      );

      // 啟動 iOS 熱點和 HTTP 服務器
      final result = await _channel.invokeMethod('createHotspot', {
        'sessionInfo': {
          'sessionId': createdSession.sessionId,
          'roomName': createdSession.roomName,
          'roomCode': createdSession.roomCode,
          'creatorName': createdSession.creatorName,
          'maxMembers': createdSession.maxMembers,
        }
      });

      if (result['success'] == true) {
        _updateStatus(CrossPlatformChatStatus.hosting);
        debugPrint('✅ iOS 聊天室創建成功');
        debugPrint('📱 ${result['message']}');

        // 如果有使用說明，顯示給用戶
        if (result['instructions'] != null) {
          debugPrint('💡 ${result['instructions']}');
        }

        return true;
      } else {
        debugPrint('❌ iOS 聊天室創建失敗: ${result['error']}');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }
    } catch (e) {
      debugPrint('❌ 創建 iOS 聊天室異常: $e');
      _updateStatus(CrossPlatformChatStatus.error);
      return false;
    }
  }

  /// 掃描聊天室（iOS 作為主機時不需要掃描）
  @override
  Future<bool> scanForChatRooms() async {
    debugPrint('💡 iOS 設備作為主機時不需要掃描聊天室');
    return true;
  }

  /// 加入聊天室（iOS 作為主機時不支持）
  @override
  Future<bool> joinChatRoom(
    DiscoveredChatRoom room,
    String userNickname, [
    String? roomCode,
  ]) async {
    debugPrint('❌ iOS 設備作為主機時不支持加入其他聊天室');
    return false;
  }

  /// 發送消息
  @override
  Future<bool> sendMessage(OfflineChatMessage message) async {
    try {
      debugPrint('🔍 檢查服務器狀態: _isServerRunning = $_isServerRunning');

      if (!_isServerRunning) {
        debugPrint('❌ HTTP 服務器未運行');
        return false;
      }

      debugPrint('📤 發送消息: ${message.content}');

      // 通過原生服務廣播消息
      final result = await _channel.invokeMethod('broadcastMessage', {
        'message': {
          'id': message.id,
          'content': message.content,
          'senderId': message.senderId,
          'senderName': message.senderName,
          'timestamp': message.timestamp.millisecondsSinceEpoch,
          'type': message.type.toString(),
        }
      });

      if (result['success'] == true) {
        // 添加到本地消息列表
        _messageManager.receiveMessage(message);
        _messageController.add(message);

        debugPrint('✅ 消息發送成功');
        return true;
      } else {
        debugPrint('❌ 消息發送失敗: ${result['error']}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ 發送消息異常: $e');
      return false;
    }
  }

  /// 斷開連接
  @override
  Future<void> disconnect() async {
    try {
      debugPrint('🔌 斷開 iOS 熱點...');

      // 停止熱點和服務器
      await _channel.invokeMethod('stopHotspot');

      // 清理狀態
      _isServerRunning = false;
      // _connectedClientsCount = 0;
      _sessionManager.leaveSession();

      _updateStatus(CrossPlatformChatStatus.disconnected);

      // 短暫延遲後恢復到就緒狀態
      await Future.delayed(const Duration(milliseconds: 500));
      _updateStatus(CrossPlatformChatStatus.ready);

      debugPrint('✅ iOS 熱點已斷開');
    } catch (e) {
      debugPrint('❌ 斷開連接異常: $e');
    }
  }

  /// 釋放資源
  @override
  Future<void> dispose() async {
    await disconnect();
    await _statusController.close();
    await _roomsController.close();
    await _messageController.close();
    await _membersController.close();
  }

  /// 設置方法通道回調
  void _setupMethodChannel() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'onHotspotStateChanged':
          _onHotspotStateChanged(call.arguments);
          break;
        case 'onMemberJoined':
          _onMemberJoined(call.arguments);
          break;
        case 'onMemberLeft':
          _onMemberLeft(call.arguments);
          break;
        case 'onMessageReceived':
          _onMessageReceived(call.arguments);
          break;
        default:
          debugPrint('🤷‍♂️ 未知的方法調用: ${call.method}');
      }
    });
  }

  /// 更新狀態
  void _updateStatus(CrossPlatformChatStatus newStatus) {
    if (_status != newStatus) {
      _status = newStatus;
      _statusController.add(_status);
      debugPrint('📱 iOS 主機狀態變更: $newStatus');
    }
  }

  /// 處理熱點狀態變化
  void _onHotspotStateChanged(dynamic arguments) {
    debugPrint('🔔 收到熱點狀態變化通知: $arguments');

    if (arguments is Map) {
      // 轉換為 Map<String, dynamic>
      final Map<String, dynamic> args = Map<String, dynamic>.from(arguments);

      final status = args['status'] as String?;
      final isServerRunning = args['isServerRunning'] as bool? ?? false;
      final connectedClients = args['connectedClients'] as int? ?? 0;

      _isServerRunning = isServerRunning;
      // _connectedClientsCount = connectedClients;

      debugPrint(
          '📱 熱點狀態變化: $status, 服務器運行: $isServerRunning, 連接客戶端: $connectedClients');
      debugPrint('🔧 更新內部狀態: _isServerRunning = $_isServerRunning');

      // 根據狀態更新服務狀態
      switch (status) {
        case 'hosting':
          _updateStatus(CrossPlatformChatStatus.hosting);
          break;
        case 'ready':
          _updateStatus(CrossPlatformChatStatus.ready);
          break;
        case 'error':
          _updateStatus(CrossPlatformChatStatus.error);
          break;
      }
    } else {
      debugPrint('⚠️ 熱點狀態變化參數格式錯誤: $arguments (類型: ${arguments.runtimeType})');
    }
  }

  /// 處理成員加入
  void _onMemberJoined(dynamic arguments) {
    if (arguments is Map<String, dynamic>) {
      try {
        final member = OfflineChatMember.fromJson(arguments);
        _sessionManager.addMember(member);
        _membersController.add(_sessionManager.members);
        debugPrint('👋 成員加入: ${member.userName}');
      } catch (e) {
        debugPrint('❌ 解析成員信息失敗: $e');
      }
    }
  }

  /// 處理成員離開
  void _onMemberLeft(dynamic arguments) {
    if (arguments is Map<String, dynamic>) {
      final deviceId = arguments['deviceId'] as String?;
      if (deviceId != null) {
        _sessionManager.removeMember(deviceId);
        _membersController.add(_sessionManager.members);
        debugPrint('👋 成員離開: $deviceId');
      }
    }
  }

  /// 處理接收到的消息
  void _onMessageReceived(dynamic arguments) {
    if (arguments is Map<String, dynamic>) {
      try {
        final message = OfflineChatMessage.fromJson(arguments);
        _messageManager.receiveMessage(message);
        _messageController.add(message);
        debugPrint('📥 收到消息: ${message.content}');
      } catch (e) {
        debugPrint('❌ 解析消息失敗: $e');
      }
    }
  }
}
