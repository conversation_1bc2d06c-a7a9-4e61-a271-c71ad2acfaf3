import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../data/models/offline_chat_models.dart';
import 'cross_platform_chat_service.dart';
import 'session_manager.dart';
import 'message_manager.dart';

/// iOS WiFi 熱點連接服務實現
///
/// 使用 iOS NEHotspotConfiguration 連接到 WiFi 熱點
/// 通過 HTTP 客戶端與 Android 熱點通信
class IOSHotspotService implements CrossPlatformChatService {
  static const MethodChannel _channel =
      MethodChannel('com.melo.offline_chat/ios_hotspot');

  // 服務管理器
  final SessionManager _sessionManager = SessionManager();
  final MessageManager _messageManager = MessageManager();

  // 狀態管理
  CrossPlatformChatStatus _status = CrossPlatformChatStatus.uninitialized;
  final NetworkAdapterConfig _config = const NetworkAdapterConfig();

  // HTTP 客戶端
  HttpClient? _httpClient;
  String? _serverAddress;
  Timer? _heartbeatTimer;
  Timer? _messagePollingTimer;

  // 事件流控制器
  final StreamController<CrossPlatformChatStatus> _statusController =
      StreamController<CrossPlatformChatStatus>.broadcast();

  // Getters
  @override
  CrossPlatformChatStatus get status => _status;

  @override
  OfflineChatSession? get currentSession => _sessionManager.currentSession;

  @override
  List<OfflineChatMember> get connectedMembers => _sessionManager.members;

  @override
  List<DiscoveredChatRoom> get discoveredRooms =>
      _sessionManager.discoveredRooms;

  // Streams
  @override
  Stream<CrossPlatformChatStatus> get statusStream => _statusController.stream;

  @override
  Stream<List<DiscoveredChatRoom>> get discoveredRoomsStream =>
      _sessionManager.roomsStream;

  @override
  Stream<OfflineChatMessage> get messageStream => _messageManager.messageStream;

  @override
  Stream<List<OfflineChatMember>> get membersStream =>
      _sessionManager.membersStream;

  /// 初始化服務
  @override
  Future<bool> initialize() async {
    try {
      debugPrint('🔧 初始化 iOS 熱點服務...');

      // 檢查權限
      if (!await _checkPermissions()) {
        debugPrint('❌ 權限檢查失敗');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }

      // 設置方法通道回調
      _setupMethodChannel();

      // 初始化 HTTP 客戶端
      _httpClient = HttpClient();
      _httpClient!.connectionTimeout = _config.connectionTimeout;

      _updateStatus(CrossPlatformChatStatus.ready);
      debugPrint('✅ iOS 熱點服務初始化成功');
      return true;
    } catch (e) {
      debugPrint('❌ 初始化失敗: $e');
      _updateStatus(CrossPlatformChatStatus.error);
      return false;
    }
  }

  /// 創建聊天室（iOS 作為熱點主機）
  @override
  Future<bool> createChatRoom(OfflineChatSession session) async {
    try {
      debugPrint('🏗️ iOS 創建聊天室: ${session.roomName}');
      _updateStatus(CrossPlatformChatStatus.creatingRoom);

      // iOS 可以使用個人熱點功能
      final hotspotResult = await _createPersonalHotspot();
      if (!hotspotResult) {
        debugPrint('❌ 創建個人熱點失敗');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }

      // 啟動 HTTP 服務器（類似 Android 實現）
      if (!await _startHttpServer()) {
        debugPrint('❌ 啟動 HTTP 服務器失敗');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }

      // 設置會話
      _sessionManager.createSession(
        roomName: session.roomName,
        roomCode: session.roomCode,
        maxMembers: session.maxMembers,
      );

      _updateStatus(CrossPlatformChatStatus.hosting);
      debugPrint('✅ iOS 聊天室創建成功');

      return true;
    } catch (e) {
      debugPrint('❌ 創建聊天室失敗: $e');
      _updateStatus(CrossPlatformChatStatus.error);
      return false;
    }
  }

  /// 掃描聊天室
  @override
  Future<bool> scanForChatRooms() async {
    try {
      debugPrint('🔍 開始掃描 WiFi 網絡...');
      _updateStatus(CrossPlatformChatStatus.scanning);

      // 掃描可用的 WiFi 網絡
      final result = await _channel.invokeMethod('scanWifiNetworks');
      if (result['success'] == true) {
        final networks = result['networks'] as List<dynamic>;
        await _processDiscoveredNetworks(networks);
        return true;
      } else {
        debugPrint('❌ 掃描失敗: ${result['error']}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ 掃描聊天室失敗: $e');
      return false;
    }
  }

  /// 加入聊天室
  @override
  Future<bool> joinChatRoom(
    DiscoveredChatRoom room,
    String userNickname, [
    String? roomCode,
  ]) async {
    try {
      debugPrint('🚪 嘗試加入聊天室: ${room.roomName}');
      _updateStatus(CrossPlatformChatStatus.connecting);

      // 連接到 WiFi 熱點
      final connectResult = await _connectToWifiHotspot(room);
      if (!connectResult) {
        debugPrint('❌ 連接 WiFi 熱點失敗');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }

      // 等待網絡連接穩定
      await Future.delayed(const Duration(seconds: 2));

      // 嘗試連接到 HTTP 服務器
      if (!await _connectToHttpServer(room, userNickname, roomCode)) {
        debugPrint('❌ 連接 HTTP 服務器失敗');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }

      // 加入會話
      _sessionManager.joinSession(room, userNickname);
      _updateStatus(CrossPlatformChatStatus.connected);

      // 開始心跳和消息輪詢
      _startHeartbeat();
      _startMessagePolling();

      debugPrint('✅ 成功加入聊天室');
      return true;
    } catch (e) {
      debugPrint('❌ 加入聊天室失敗: $e');
      _updateStatus(CrossPlatformChatStatus.error);
      return false;
    }
  }

  /// 發送消息
  @override
  Future<bool> sendMessage(OfflineChatMessage message) async {
    try {
      // 通過消息管理器發送
      await _messageManager.sendMessage(message);

      // 發送到服務器
      if (_serverAddress != null) {
        await _sendMessageToServer(message);
      }

      return true;
    } catch (e) {
      debugPrint('❌ 發送消息失敗: $e');
      return false;
    }
  }

  /// 斷開連接
  @override
  Future<void> disconnect() async {
    try {
      debugPrint('🚪 斷開連接...');

      // 停止定時器
      _heartbeatTimer?.cancel();
      _messagePollingTimer?.cancel();

      // 斷開 WiFi 連接
      await _disconnectFromWifi();

      // 清理會話
      _sessionManager.leaveSession();

      _updateStatus(CrossPlatformChatStatus.ready);
      debugPrint('✅ 已斷開連接');
    } catch (e) {
      debugPrint('❌ 斷開連接失敗: $e');
    }
  }

  /// 釋放資源
  @override
  Future<void> dispose() async {
    await disconnect();
    _httpClient?.close();
    await _statusController.close();
    await _sessionManager.dispose();
    await _messageManager.dispose();
  }

  // 私有方法

  /// 檢查權限
  Future<bool> _checkPermissions() async {
    final permissions = [
      Permission.location,
    ];

    for (final permission in permissions) {
      final status = await permission.request();
      if (!status.isGranted) {
        debugPrint('❌ 權限被拒絕: $permission');
        return false;
      }
    }

    return true;
  }

  /// 設置方法通道回調
  void _setupMethodChannel() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'onWifiStateChanged':
          _onWifiStateChanged(call.arguments);
          break;
        case 'onWifiNetworkFound':
          _onWifiNetworkFound(call.arguments);
          break;
        case 'onConnectionStateChanged':
          _onConnectionStateChanged(call.arguments);
          break;
        default:
          debugPrint('⚠️ 未知方法調用: ${call.method}');
      }
    });
  }

  /// 創建個人熱點（iOS）
  Future<bool> _createPersonalHotspot() async {
    try {
      // iOS 個人熱點需要用戶手動開啟
      // 這裡可以引導用戶開啟個人熱點
      final result = await _channel.invokeMethod('enablePersonalHotspot');
      return result['success'] == true;
    } catch (e) {
      debugPrint('❌ 創建個人熱點異常: $e');
      return false;
    }
  }

  /// 啟動 HTTP 服務器（iOS 作為主機時）
  Future<bool> _startHttpServer() async {
    try {
      // 實現類似 Android 的 HTTP 服務器
      // 這裡簡化實現，實際需要完整的 HTTP 服務器邏輯
      debugPrint('🌐 iOS HTTP 服務器啟動');
      return true;
    } catch (e) {
      debugPrint('❌ 啟動 HTTP 服務器失敗: $e');
      return false;
    }
  }

  /// 連接到 WiFi 熱點
  Future<bool> _connectToWifiHotspot(DiscoveredChatRoom room) async {
    try {
      // 使用 NEHotspotConfiguration 連接
      final result = await _channel.invokeMethod('connectToWifi', {
        'ssid': room.metadata?['ssid'] ?? '',
        'password': room.metadata?['password'] ?? '',
      });

      if (result['success'] == true) {
        _serverAddress = result['gatewayAddress'] as String?;
        debugPrint('📶 已連接到 WiFi: ${room.metadata?['ssid']}');
        return true;
      } else {
        debugPrint('❌ WiFi 連接失敗: ${result['error']}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ 連接 WiFi 異常: $e');
      return false;
    }
  }

  /// 連接到 HTTP 服務器
  Future<bool> _connectToHttpServer(
    DiscoveredChatRoom room,
    String userNickname,
    String? roomCode,
  ) async {
    try {
      if (_serverAddress == null) {
        debugPrint('❌ 服務器地址未知');
        return false;
      }

      // 測試連接
      final uri =
          Uri.http('$_serverAddress:${_config.serverPort}', '/api/room/info');
      final request = await _httpClient!.getUrl(uri);
      final response = await request.close();

      if (response.statusCode == 200) {
        final responseBody = await response.transform(utf8.decoder).join();
        final roomInfo = jsonDecode(responseBody) as Map<String, dynamic>;
        debugPrint('📡 服務器連接成功: ${roomInfo['roomName']}');

        // 發送加入請求
        return await _sendJoinRequest(userNickname, roomCode);
      } else {
        debugPrint('❌ 服務器響應錯誤: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ 連接服務器異常: $e');
      return false;
    }
  }

  /// 發送加入請求
  Future<bool> _sendJoinRequest(String userNickname, String? roomCode) async {
    try {
      final uri =
          Uri.http('$_serverAddress:${_config.serverPort}', '/api/room/join');
      final request = await _httpClient!.postUrl(uri);

      final joinData = {
        'userNickname': userNickname,
        'roomCode': roomCode,
        'deviceId': 'ios_device_${DateTime.now().millisecondsSinceEpoch}',
      };

      request.headers.contentType = ContentType.json;
      request.write(jsonEncode(joinData));

      final response = await request.close();
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('❌ 發送加入請求失敗: $e');
      return false;
    }
  }

  /// 開始心跳
  void _startHeartbeat() {
    _heartbeatTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _sendHeartbeat();
    });
  }

  /// 開始消息輪詢
  void _startMessagePolling() {
    _messagePollingTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      _pollMessages();
    });
  }

  /// 發送心跳
  Future<void> _sendHeartbeat() async {
    try {
      if (_serverAddress == null) return;

      final uri =
          Uri.http('$_serverAddress:${_config.serverPort}', '/api/heartbeat');
      final request = await _httpClient!.postUrl(uri);
      await request.close();
    } catch (e) {
      debugPrint('⚠️ 心跳發送失敗: $e');
    }
  }

  /// 輪詢消息
  Future<void> _pollMessages() async {
    try {
      if (_serverAddress == null) return;

      final uri =
          Uri.http('$_serverAddress:${_config.serverPort}', '/api/messages');
      final request = await _httpClient!.getUrl(uri);
      final response = await request.close();

      if (response.statusCode == 200) {
        final responseBody = await response.transform(utf8.decoder).join();
        final data = jsonDecode(responseBody) as Map<String, dynamic>;
        final messages = data['messages'] as List<dynamic>;

        for (final messageData in messages) {
          final message =
              OfflineChatMessage.fromJson(messageData as Map<String, dynamic>);
          _messageManager.receiveMessage(message);
        }
      }
    } catch (e) {
      debugPrint('⚠️ 輪詢消息失敗: $e');
    }
  }

  /// 發送消息到服務器
  Future<void> _sendMessageToServer(OfflineChatMessage message) async {
    try {
      if (_serverAddress == null) return;

      final uri =
          Uri.http('$_serverAddress:${_config.serverPort}', '/api/messages');
      final request = await _httpClient!.postUrl(uri);

      request.headers.contentType = ContentType.json;
      request.write(jsonEncode(message.toJson()));

      final response = await request.close();
      if (response.statusCode == 200) {
        _messageManager.confirmMessageSent(message.id);
      } else {
        _messageManager.markMessageFailed(message.id, '服務器錯誤');
      }
    } catch (e) {
      debugPrint('❌ 發送消息到服務器失敗: $e');
      _messageManager.markMessageFailed(message.id, e.toString());
    }
  }

  /// 更新狀態
  void _updateStatus(CrossPlatformChatStatus newStatus) {
    if (_status != newStatus) {
      _status = newStatus;
      _statusController.add(_status);
      debugPrint('📊 狀態更新: $newStatus');
    }
  }

  // 待實現的方法
  Future<void> _processDiscoveredNetworks(List<dynamic> networks) async {
    // TODO: 處理發現的網絡
  }

  Future<void> _disconnectFromWifi() async {
    try {
      await _channel.invokeMethod('disconnectWifi');
    } catch (e) {
      debugPrint('⚠️ 斷開 WiFi 時出錯: $e');
    }
  }

  void _onWifiStateChanged(Map<String, dynamic> args) {
    // TODO: 處理 WiFi 狀態變化
  }

  void _onWifiNetworkFound(Map<String, dynamic> args) {
    // TODO: 處理發現的 WiFi 網絡
  }

  void _onConnectionStateChanged(Map<String, dynamic> args) {
    // TODO: 處理連接狀態變化
  }
}
