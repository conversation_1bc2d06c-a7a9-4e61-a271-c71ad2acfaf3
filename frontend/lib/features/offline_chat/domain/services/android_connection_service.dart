import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;

import '../../data/models/offline_chat_models.dart';
import 'session_manager.dart';
// import 'message_manager.dart';
import 'cross_platform_chat_service.dart';

/// Android 連接服務
///
/// 功能：
/// 1. 連接到 iOS Personal Hotspot
/// 2. 通過 HTTP 客戶端與 iOS 服務器通信
/// 3. 處理聊天室加入和消息收發
class AndroidConnectionService implements CrossPlatformChatService {
  static const MethodChannel _channel =
      MethodChannel('com.melo.offline_chat/wifi_hotspot');

  // 服務管理器
  final SessionManager _sessionManager = SessionManager();
  // final MessageManager _messageManager = MessageManager();

  // 狀態管理
  CrossPlatformChatStatus _status = CrossPlatformChatStatus.uninitialized;

  // 連接信息
  // String? _connectedSSID;
  String? _serverBaseURL;
  bool _isConnected = false;

  // HTTP 客戶端
  late http.Client _httpClient;

  // 事件流控制器
  final StreamController<CrossPlatformChatStatus> _statusController =
      StreamController<CrossPlatformChatStatus>.broadcast();
  final StreamController<List<DiscoveredChatRoom>> _roomsController =
      StreamController<List<DiscoveredChatRoom>>.broadcast();
  final StreamController<OfflineChatMessage> _messageController =
      StreamController<OfflineChatMessage>.broadcast();
  final StreamController<List<OfflineChatMember>> _membersController =
      StreamController<List<OfflineChatMember>>.broadcast();

  @override
  CrossPlatformChatStatus get status => _status;

  @override
  OfflineChatSession? get currentSession => _sessionManager.currentSession;

  @override
  List<OfflineChatMember> get connectedMembers => _sessionManager.members;

  @override
  List<DiscoveredChatRoom> get discoveredRooms =>
      _sessionManager.discoveredRooms;

  @override
  Stream<CrossPlatformChatStatus> get statusStream => _statusController.stream;

  @override
  Stream<List<DiscoveredChatRoom>> get discoveredRoomsStream =>
      _roomsController.stream;

  @override
  Stream<OfflineChatMessage> get messageStream => _messageController.stream;

  @override
  Stream<List<OfflineChatMember>> get membersStream =>
      _membersController.stream;

  /// 初始化服務
  @override
  Future<bool> initialize() async {
    try {
      debugPrint('🤖 初始化 Android 連接服務...');
      _updateStatus(CrossPlatformChatStatus.uninitialized);

      // 檢查平台支持
      if (!Platform.isAndroid) {
        debugPrint('❌ 當前平台不是 Android');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }

      // 初始化 HTTP 客戶端
      _httpClient = http.Client();

      _updateStatus(CrossPlatformChatStatus.ready);
      debugPrint('✅ Android 連接服務初始化成功');
      return true;
    } catch (e) {
      debugPrint('❌ Android 連接服務初始化異常: $e');
      _updateStatus(CrossPlatformChatStatus.error);
      return false;
    }
  }

  /// 創建聊天室（Android 客戶端模式不支持）
  @override
  Future<bool> createChatRoom(OfflineChatSession session) async {
    debugPrint('❌ Android 客戶端模式不支持創建聊天室，請使用主機模式');
    return false;
  }

  /// 掃描聊天室（掃描 iOS Personal Hotspot）
  @override
  Future<bool> scanForChatRooms() async {
    try {
      debugPrint('🔍 掃描 iOS Personal Hotspot...');
      _updateStatus(CrossPlatformChatStatus.scanning);

      // 使用 Android WiFi 掃描功能
      final result = await _channel.invokeMethod('scanWifiNetworks');
      if (result['success'] == true) {
        final networks = result['networks'] as List<dynamic>? ?? [];
        debugPrint('📡 發現 ${networks.length} 個 WiFi 網絡');

        // 過濾出可能的 iOS Personal Hotspot
        final chatRooms = _filterIOSHotspots(networks);

        // 更新發現的聊天室
        _sessionManager.clearDiscoveredRooms();
        for (final room in chatRooms) {
          _sessionManager.addDiscoveredRoom(room);
        }

        _roomsController.add(chatRooms);
        _updateStatus(CrossPlatformChatStatus.ready);

        debugPrint('📱 發現 ${chatRooms.length} 個 iOS 聊天室');
        return true;
      } else {
        debugPrint('❌ WiFi 掃描失敗: ${result['error']}');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }
    } catch (e) {
      debugPrint('❌ 掃描 WiFi 網絡異常: $e');
      _updateStatus(CrossPlatformChatStatus.error);
      return false;
    }
  }

  /// 加入聊天室（連接到 iOS Personal Hotspot）
  @override
  Future<bool> joinChatRoom(
    DiscoveredChatRoom room,
    String userNickname, [
    String? roomCode,
  ]) async {
    try {
      debugPrint('🔗 嘗試連接到 iOS 聊天室: ${room.roomName}');
      _updateStatus(CrossPlatformChatStatus.connecting);

      // 連接到 iOS Personal Hotspot
      final connectResult =
          await _connectToIOSHotspot(room.roomName, roomCode ?? '');
      if (!connectResult) {
        debugPrint('❌ 連接到 iOS 熱點失敗');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }

      // 等待連接穩定
      await Future.delayed(const Duration(seconds: 3));

      // 嘗試連接到 iOS HTTP 服務器
      final joinResult = await _joinIOSChatRoom(room, userNickname, roomCode);
      if (joinResult) {
        _updateStatus(CrossPlatformChatStatus.connected);
        debugPrint('✅ 成功加入 iOS 聊天室: ${room.roomName}');
        return true;
      } else {
        debugPrint('❌ 加入 iOS 聊天室失敗');
        _updateStatus(CrossPlatformChatStatus.error);
        return false;
      }
    } catch (e) {
      debugPrint('❌ 加入聊天室異常: $e');
      _updateStatus(CrossPlatformChatStatus.error);
      return false;
    }
  }

  /// 發送消息
  @override
  Future<bool> sendMessage(OfflineChatMessage message) async {
    try {
      if (!_isConnected || _serverBaseURL == null) {
        debugPrint('❌ 未連接到 iOS 服務器');
        return false;
      }

      debugPrint('📤 發送消息到 iOS 服務器: ${message.content}');

      // 發送 HTTP 請求到 iOS 服務器
      final response = await _httpClient.post(
        Uri.parse('$_serverBaseURL/api/messages/send'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'id': message.id,
          'content': message.content,
          'senderId': message.senderId,
          'senderName': message.senderName,
          'timestamp': message.timestamp.millisecondsSinceEpoch,
          'type': message.type.toString(),
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          debugPrint('✅ 消息發送成功');
          return true;
        }
      }

      debugPrint('❌ 消息發送失敗: ${response.statusCode}');
      return false;
    } catch (e) {
      debugPrint('❌ 發送消息異常: $e');
      return false;
    }
  }

  /// 斷開連接
  @override
  Future<void> disconnect() async {
    try {
      debugPrint('🔌 斷開 Android 連接...');

      // 斷開 WiFi 連接
      await _channel.invokeMethod('disconnectWifi');

      // 清理狀態
      _isConnected = false;
      // _connectedSSID = null;
      _serverBaseURL = null;
      _sessionManager.leaveSession();

      _updateStatus(CrossPlatformChatStatus.disconnected);

      // 短暫延遲後恢復到就緒狀態
      await Future.delayed(const Duration(milliseconds: 500));
      _updateStatus(CrossPlatformChatStatus.ready);

      debugPrint('✅ Android 連接已斷開');
    } catch (e) {
      debugPrint('❌ 斷開連接異常: $e');
    }
  }

  /// 釋放資源
  @override
  Future<void> dispose() async {
    await disconnect();
    _httpClient.close();
    await _statusController.close();
    await _roomsController.close();
    await _messageController.close();
    await _membersController.close();
  }

  // 私有方法

  /// 連接到 iOS Personal Hotspot
  Future<bool> _connectToIOSHotspot(String ssid, String password) async {
    try {
      // 使用現有的 WiFi 連接方法
      final result = await _channel.invokeMethod('connectToWifi', {
        'ssid': ssid,
        'password': password,
      });

      if (result['success'] == true) {
        // _connectedSSID = ssid;
        _isConnected = true;

        // iOS Personal Hotspot 的默認網關通常是 172.20.10.1
        _serverBaseURL = 'http://172.20.10.1:8888';

        debugPrint('📡 成功連接到 iOS 熱點: $ssid');
        debugPrint('🌐 服務器地址: $_serverBaseURL');
        return true;
      } else {
        debugPrint('❌ 連接 iOS 熱點失敗: ${result['error']}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ 連接 iOS 熱點異常: $e');
      return false;
    }
  }

  /// 加入 iOS 聊天室
  Future<bool> _joinIOSChatRoom(
    DiscoveredChatRoom room,
    String userNickname,
    String? roomCode,
  ) async {
    try {
      debugPrint('🔗 嘗試加入 iOS 聊天室服務器...');

      // 發送加入請求到 iOS 服務器
      final response = await _httpClient
          .post(
            Uri.parse('$_serverBaseURL/api/room/join'),
            headers: {
              'Content-Type': 'application/json',
            },
            body: jsonEncode({
              'roomId': room.sessionId,
              'userName': userNickname,
              'roomCode': roomCode,
              'deviceInfo': {
                'platform': 'Android',
                'deviceName': 'Android Device',
              }
            }),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['success'] == true) {
          // 加入會話
          _sessionManager.joinSession(room, userNickname);

          // 處理成員列表
          if (data['members'] != null) {
            final members = (data['members'] as List)
                .map((m) => OfflineChatMember.fromJson(m))
                .toList();

            // 添加每個成員
            for (final member in members) {
              _sessionManager.addMember(member);
            }

            _membersController.add(_sessionManager.members);
          }

          debugPrint('✅ 成功加入 iOS 聊天室');
          return true;
        }
      }

      debugPrint('❌ 加入 iOS 聊天室失敗: ${response.statusCode}');
      return false;
    } catch (e) {
      debugPrint('❌ 加入 iOS 聊天室異常: $e');
      return false;
    }
  }

  /// 過濾 iOS Personal Hotspot
  List<DiscoveredChatRoom> _filterIOSHotspots(List<dynamic> networks) {
    final chatRooms = <DiscoveredChatRoom>[];

    for (final network in networks) {
      if (network is Map<String, dynamic>) {
        final ssid = network['ssid'] as String?;
        final signalStrength = network['level'] as int? ?? -50;

        if (ssid != null) {
          // 檢查是否可能是 iOS Personal Hotspot
          // iOS Personal Hotspot 通常以設備名稱命名
          if (_isLikelyIOSHotspot(ssid)) {
            chatRooms.add(DiscoveredChatRoom(
              sessionId: ssid,
              roomName: ssid,
              creatorName: 'iOS Host',
              memberCount: 1,
              maxMembers: 8,
              deviceId: 'ios-host',
              signalStrength: signalStrength,
              lastSeen: DateTime.now(),
            ));
          }
        }
      }
    }

    return chatRooms;
  }

  /// 判斷是否可能是 iOS Personal Hotspot
  bool _isLikelyIOSHotspot(String ssid) {
    // iOS Personal Hotspot 的特徵：
    // 1. 通常包含設備名稱（如 "Kate's iPhone"）
    // 2. 不包含常見的路由器品牌名稱
    // 3. 長度通常較短

    final lowerSSID = ssid.toLowerCase();

    // 排除常見的路由器 SSID
    final routerKeywords = [
      'tp-link',
      'netgear',
      'linksys',
      'asus',
      'dlink',
      'd-link',
      'belkin',
      'cisco',
      'huawei',
      'xiaomi',
      'tenda',
      'mercusys',
      'wifi',
      'router',
      'modem',
      'broadband',
      'internet'
    ];

    for (final keyword in routerKeywords) {
      if (lowerSSID.contains(keyword)) {
        return false;
      }
    }

    // iOS 設備的常見特徵
    final iosKeywords = [
      'iphone',
      'ipad',
      'ipod',
      'mac',
      'macbook',
      'imac',
      "'s ",
      ' phone',
      ' pad',
      ' device'
    ];

    for (final keyword in iosKeywords) {
      if (lowerSSID.contains(keyword)) {
        return true;
      }
    }

    // 如果 SSID 長度適中且不包含數字序列，可能是個人設備
    if (ssid.length >= 4 && ssid.length <= 20) {
      // 檢查是否包含過多數字（路由器通常有很多數字）
      final digitCount = ssid.replaceAll(RegExp(r'[^0-9]'), '').length;
      if (digitCount < ssid.length * 0.3) {
        return true;
      }
    }

    return false;
  }

  /// 更新狀態
  void _updateStatus(CrossPlatformChatStatus newStatus) {
    if (_status != newStatus) {
      _status = newStatus;
      _statusController.add(_status);
      debugPrint('🤖 Android 連接狀態變更: $newStatus');
    }
  }
}
