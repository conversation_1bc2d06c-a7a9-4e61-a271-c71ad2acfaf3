import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../domain/services/cross_platform_chat_service.dart';
import '../bloc/offline_chat_bloc.dart';
import '../widgets/create_offline_room_dialog.dart';
import '../widgets/join_offline_room_dialog.dart';
import '../widgets/offline_chat_room_widget.dart';

/// 離線聊天主頁面
class OfflineChatPage extends StatelessWidget {
  const OfflineChatPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => OfflineChatBloc(
        offlineChatService:
            DependencyInjection.getIt<CrossPlatformChatService>(),
      )..add(OfflineChatInitializeRequested()),
      child: const _OfflineChatPageContent(),
    );
  }
}

class _OfflineChatPageContent extends StatelessWidget {
  const _OfflineChatPageContent();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Offline Chat'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: BlocConsumer<OfflineChatBloc, OfflineChatState>(
        listener: (context, state) {
          if (state is OfflineChatError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is OfflineChatLoading) {
            return const _LoadingView();
          } else if (state is OfflineChatReady) {
            return const _ReadyView();
          } else if (state is OfflineChatDiscovering) {
            return _DiscoveringView(discoveredRooms: state.discoveredRooms);
          } else if (state is OfflineChatInRoom) {
            return OfflineChatRoomWidget(
              session: state.session,
              messages: state.messages,
              members: state.members,
              status: state.status,
            );
          } else if (state is OfflineChatError) {
            return _ErrorView(message: state.message);
          }

          return const _LoadingView();
        },
      ),
    );
  }
}

class _LoadingView extends StatelessWidget {
  const _LoadingView();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Initializing WiFi Direct...'),
        ],
      ),
    );
  }
}

class _ReadyView extends StatelessWidget {
  const _ReadyView();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 標題和說明
          const Icon(
            Icons.wifi_tethering,
            size: 80,
            color: AppColors.primary,
          ),
          const SizedBox(height: 24),

          Text(
            'Offline Chat Room',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          Text(
            'Chat with nearby friends using WiFi Direct without internet connection',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.textSecondary,
                ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 48),

          // 功能按鈕
          CustomButton(
            text: 'Create Chat Room',
            onPressed: () => _showCreateRoomDialog(context),
            icon: const Icon(Icons.add_circle_outline),
          ),

          const SizedBox(height: 16),

          CustomButton(
            text: 'Join Chat Room',
            onPressed: () => _startDiscovery(context),
            icon: const Icon(Icons.search),
            type: ButtonType.outline,
          ),

          const SizedBox(height: 32),

          // 使用說明
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.border),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.info_outline,
                      color: AppColors.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Instructions',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                _buildInstructionItem(
                  context,
                  '1. Ensure WiFi and location services are enabled',
                ),
                _buildInstructionItem(
                  context,
                  '2. Create chat room and set 6-digit room code',
                ),
                _buildInstructionItem(
                  context,
                  '3. Share room code with friends to join chat',
                ),
                _buildInstructionItem(
                  context,
                  '4. Enjoy instant chat without internet',
                ),
              ],
            ),
          ),

          // 添加底部間距，確保內容不會被截斷
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildInstructionItem(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
      ),
    );
  }

  void _showCreateRoomDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<OfflineChatBloc>(),
        child: const CreateOfflineRoomDialog(),
      ),
    );
  }

  void _startDiscovery(BuildContext context) {
    context.read<OfflineChatBloc>().add(OfflineChatStartDiscoveryRequested());
  }
}

class _DiscoveringView extends StatelessWidget {
  final List<dynamic> discoveredRooms; // 暫時使用 dynamic

  const _DiscoveringView({required this.discoveredRooms});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 標題
          Row(
            children: [
              IconButton(
                onPressed: () => _stopDiscovery(context),
                icon: const Icon(Icons.arrow_back),
              ),
              const SizedBox(width: 8),
              Text(
                'Search Nearby Chat Rooms',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 搜索指示器
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                const SizedBox(width: 16),
                Text(
                  'Searching for nearby chat rooms...',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.primary,
                      ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // 發現的聊天室列表
          Expanded(
            child: discoveredRooms.isEmpty
                ? _buildEmptyState(context)
                : _buildRoomsList(context),
          ),

          // 停止搜索按鈕
          CustomButton(
            text: 'Stop Search',
            onPressed: () => _stopDiscovery(context),
            type: ButtonType.outline,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: AppColors.textSecondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No nearby chat rooms found',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppColors.textSecondary,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Please ensure other devices have created chat rooms and enabled broadcasting',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRoomsList(BuildContext context) {
    return ListView.builder(
      itemCount: discoveredRooms.length,
      itemBuilder: (context, index) {
        // 暫時顯示佔位符，實際實現需要使用 DiscoveredChatRoom
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: const CircleAvatar(
              backgroundColor: AppColors.primary,
              child: Icon(Icons.chat, color: Colors.white),
            ),
            title: Text('Chat Room ${index + 1}'),
            subtitle: const Text('Creator: Unknown • Members: 1/8'),
            trailing: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.signal_wifi_4_bar,
                  color: AppColors.success,
                  size: 16,
                ),
                SizedBox(width: 8),
                Icon(Icons.chevron_right),
              ],
            ),
            onTap: () => _showJoinDialog(context, index),
          ),
        );
      },
    );
  }

  void _showJoinDialog(BuildContext context, int roomIndex) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<OfflineChatBloc>(),
        child: const JoinOfflineRoomDialog(
          room: null, // 暫時為 null，實際需要傳入 DiscoveredChatRoom
        ),
      ),
    );
  }

  void _stopDiscovery(BuildContext context) {
    context.read<OfflineChatBloc>().add(OfflineChatStopDiscoveryRequested());
  }
}

class _ErrorView extends StatelessWidget {
  final String message;

  const _ErrorView({required this.message});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Error Occurred',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            CustomButton(
              text: 'Retry',
              onPressed: () {
                context
                    .read<OfflineChatBloc>()
                    .add(OfflineChatInitializeRequested());
              },
            ),
          ],
        ),
      ),
    );
  }
}
