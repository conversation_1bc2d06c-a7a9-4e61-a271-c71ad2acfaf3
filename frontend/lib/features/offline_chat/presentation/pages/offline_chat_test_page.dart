import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../domain/services/cross_platform_chat_service.dart';
import '../../data/models/offline_chat_models.dart';

/// 離線聊天測試頁面
///
/// 用於測試新的跨平台離線聊天功能
class OfflineChatTestPage extends StatefulWidget {
  const OfflineChatTestPage({super.key});

  @override
  State<OfflineChatTestPage> createState() => _OfflineChatTestPageState();
}

class _OfflineChatTestPageState extends State<OfflineChatTestPage> {
  late CrossPlatformChatService _chatService;
  CrossPlatformChatStatus _status = CrossPlatformChatStatus.uninitialized;
  List<DiscoveredChatRoom> _discoveredRooms = [];
  final List<OfflineChatMessage> _messages = [];
  List<OfflineChatMember> _members = [];
  OfflineChatSession? _currentSession;

  final TextEditingController _roomNameController = TextEditingController();
  final TextEditingController _userNameController = TextEditingController();
  final TextEditingController _messageController = TextEditingController();
  final TextEditingController _roomCodeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  Future<void> _initializeService() async {
    _chatService = DependencyInjection.getIt<CrossPlatformChatService>();

    // 監聽狀態變化
    _chatService.statusStream.listen((status) {
      setState(() {
        _status = status;
      });
    });

    // 監聽發現的聊天室
    _chatService.discoveredRoomsStream.listen((rooms) {
      setState(() {
        _discoveredRooms = rooms;
      });
    });

    // 監聽消息
    _chatService.messageStream.listen((message) {
      setState(() {
        _messages.add(message);
      });
    });

    // 監聽成員變化
    _chatService.membersStream.listen((members) {
      setState(() {
        _members = members;
      });
    });

    // 初始化服務
    await _chatService.initialize();
  }

  @override
  void dispose() {
    _roomNameController.dispose();
    _userNameController.dispose();
    _messageController.dispose();
    _roomCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Offline Chat Test'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showServiceInfo,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildStatusCard(),
            const SizedBox(height: 16),
            _buildControlPanel(),
            const SizedBox(height: 16),
            _buildDiscoveredRooms(),
            const SizedBox(height: 16),
            _buildCurrentSession(),
            const SizedBox(height: 16),
            _buildMessages(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Service Status',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                if (_status == CrossPlatformChatStatus.connecting ||
                    _status == CrossPlatformChatStatus.creatingRoom ||
                    _status == CrossPlatformChatStatus.scanning)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: _getStatusColor().withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: _getStatusColor().withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: _getStatusColor(),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      StatusMapper.getStatusDisplayText(_status),
                      style: TextStyle(
                        color: _getStatusColor(),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // 適配器信息將在 HybridChatService 實現後顯示
          ],
        ),
      ),
    );
  }

  Widget _buildControlPanel() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Control Panel',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _roomNameController,
              decoration: const InputDecoration(
                labelText: 'Chat Room Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _userNameController,
              decoration: const InputDecoration(
                labelText: 'User Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _roomCodeController,
              decoration: const InputDecoration(
                labelText: 'Room Code (Optional)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                CustomButton(
                  text: 'Create Chat Room',
                  onPressed: _status == CrossPlatformChatStatus.ready
                      ? _createRoom
                      : null,
                  backgroundColor: AppColors.primary,
                ),
                CustomButton(
                  text: 'Scan Chat Rooms',
                  onPressed: _status == CrossPlatformChatStatus.ready
                      ? _scanRooms
                      : null,
                  backgroundColor: AppColors.secondary,
                ),
                CustomButton(
                  text: 'Disconnect',
                  onPressed: _status != CrossPlatformChatStatus.uninitialized
                      ? _disconnect
                      : null,
                  backgroundColor: Colors.red,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscoveredRooms() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Discovered Chat Rooms (${_discoveredRooms.length})',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            if (_discoveredRooms.isEmpty)
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 48,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'No chat rooms found',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 16,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Click "Scan Chat Rooms" to find nearby chat rooms',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              )
            else
              ...(_discoveredRooms.map((room) => _buildRoomTile(room))),
          ],
        ),
      ),
    );
  }

  Widget _buildRoomTile(DiscoveredChatRoom room) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Icon(
            Icons.wifi,
            color: AppColors.primary,
          ),
        ),
        title: Text(
          room.roomName,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Creator: ${room.creatorName}'),
            Row(
              children: [
                Icon(
                  Icons.people,
                  size: 14,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  '${room.memberCount}/${room.maxMembers}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 12),
                Icon(
                  Icons.signal_wifi_4_bar,
                  size: 14,
                  color: _getSignalColor(room.signalStrength),
                ),
                const SizedBox(width: 4),
                Text(
                  '${room.signalStrength}dBm',
                  style: TextStyle(
                    color: _getSignalColor(room.signalStrength),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: CustomButton(
          text: 'Join',
          onPressed: () => _joinRoom(room),
          backgroundColor: AppColors.success,
        ),
      ),
    );
  }

  Widget _buildCurrentSession() {
    if (_currentSession == null) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Session',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text('Chat Room: ${_currentSession!.roomName}'),
            Text('Room Code: ${_currentSession!.roomCode}'),
            Text('Members: ${_members.length}/${_currentSession!.maxMembers}'),
            const SizedBox(height: 16),
            TextField(
              controller: _messageController,
              decoration: InputDecoration(
                labelText: 'Enter Message',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.send),
                  onPressed: _sendMessage,
                ),
              ),
              onSubmitted: (_) => _sendMessage(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessages() {
    if (_messages.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Message History (${_messages.length})',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView.builder(
                itemCount: _messages.length,
                itemBuilder: (context, index) {
                  final message = _messages[index];
                  return ListTile(
                    dense: true,
                    title: Text(message.senderName),
                    subtitle: Text(message.content),
                    trailing: Text(
                      '${message.timestamp.hour}:${message.timestamp.minute.toString().padLeft(2, '0')}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (_status) {
      case CrossPlatformChatStatus.ready:
        return AppColors.success;
      case CrossPlatformChatStatus.connected:
      case CrossPlatformChatStatus.hosting:
        return AppColors.primary;
      case CrossPlatformChatStatus.connecting:
      case CrossPlatformChatStatus.scanning:
      case CrossPlatformChatStatus.creatingRoom:
        return AppColors.warning;
      case CrossPlatformChatStatus.error:
        return AppColors.error;
      default:
        return Colors.grey;
    }
  }

  Color _getSignalColor(int signalStrength) {
    if (signalStrength >= -50) {
      return Colors.green; // 強信號
    } else if (signalStrength >= -70) {
      return Colors.orange; // 中等信號
    } else {
      return Colors.red; // 弱信號
    }
  }

  Future<void> _createRoom() async {
    if (_roomNameController.text.isEmpty) {
      _showSnackBar('Please enter chat room name');
      return;
    }

    final session = OfflineChatSession(
      sessionId: 'test_${DateTime.now().millisecondsSinceEpoch}',
      roomName: _roomNameController.text,
      roomCode: _roomCodeController.text.isEmpty
          ? OfflineChatSession.generateRandomRoomCode()
          : _roomCodeController.text,
      creatorId: 'test_user',
      creatorName: _userNameController.text.isEmpty
          ? 'Test User'
          : _userNameController.text,
      createdAt: DateTime.now(),
      maxMembers: 8,
      isActive: true,
    );

    final success = await _chatService.createChatRoom(session);
    if (success) {
      setState(() {
        _currentSession = session;
      });
      _showSnackBar('Chat room created successfully');
    } else {
      _showSnackBar('Failed to create chat room');
    }
  }

  Future<void> _scanRooms() async {
    final success = await _chatService.scanForChatRooms();
    if (success) {
      _showSnackBar('Started scanning for chat rooms');
    } else {
      _showSnackBar('Scan failed');
    }
  }

  Future<void> _joinRoom(DiscoveredChatRoom room) async {
    final userName = _userNameController.text.isEmpty
        ? 'Test User'
        : _userNameController.text;
    final roomCode =
        _roomCodeController.text.isEmpty ? null : _roomCodeController.text;

    final success = await _chatService.joinChatRoom(room, userName, roomCode);
    if (success) {
      setState(() {
        _currentSession = OfflineChatSession(
          sessionId: room.sessionId,
          roomName: room.roomName,
          roomCode: roomCode ?? '',
          creatorId: room.deviceId,
          creatorName: room.creatorName,
          createdAt: DateTime.now(),
          maxMembers: room.maxMembers,
          isActive: true,
        );
      });
      _showSnackBar('Successfully joined chat room');
    } else {
      _showSnackBar('Failed to join chat room');
    }
  }

  Future<void> _sendMessage() async {
    if (_messageController.text.isEmpty) return;

    final message = OfflineChatMessage(
      id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
      senderId: 'test_user',
      senderName: _userNameController.text.isEmpty
          ? 'Test User'
          : _userNameController.text,
      content: _messageController.text,
      type: OfflineMessageType.text,
      timestamp: DateTime.now(),
    );

    final success = await _chatService.sendMessage(message);
    if (success) {
      _messageController.clear();
    } else {
      _showSnackBar('Failed to send message');
    }
  }

  Future<void> _disconnect() async {
    await _chatService.disconnect();
    setState(() {
      _currentSession = null;
      _messages.clear();
      _members.clear();
      _discoveredRooms.clear();
    });
    _showSnackBar('Disconnected');
  }

  void _showServiceInfo() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.info, color: Colors.blue),
                  const SizedBox(width: 8),
                  const Text(
                    'Service Information',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Service Type: ${_chatService.runtimeType}'),
                  const SizedBox(height: 8),
                  Text(
                      'Current Status: ${StatusMapper.getStatusDisplayText(_status)}'),
                  // 適配器信息將在 HybridChatService 實現後顯示
                  const SizedBox(height: 24),
                  // Actions
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('Close'),
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
