import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../data/models/offline_chat_models.dart';
import '../bloc/offline_chat_bloc.dart';

/// Join offline chat room dialog
class JoinOfflineRoomDialog extends StatefulWidget {
  final dynamic
      room; // Temporarily using dynamic, should be DiscoveredChatRoom?

  const JoinOfflineRoomDialog({
    super.key,
    required this.room,
  });

  @override
  State<JoinOfflineRoomDialog> createState() => _JoinOfflineRoomDialogState();
}

class _JoinOfflineRoomDialogState extends State<JoinOfflineRoomDialog> {
  final _formKey = GlobalKey<FormState>();
  final _userNameController = TextEditingController();
  final _roomCodeController = TextEditingController();

  bool _isJoining = false;

  @override
  void dispose() {
    _userNameController.dispose();
    _roomCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<OfflineChatBloc, OfflineChatState>(
      listener: (context, state) {
        if (state is OfflineChatInRoom) {
          // Successfully joined chat room, close dialog
          Navigator.of(context).pop();
        } else if (state is OfflineChatError) {
          setState(() {
            _isJoining = false;
          });
        }
      },
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.7,
          minChildSize: 0.5,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                // Drag handle
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                // Header
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.login,
                        color: AppColors.primary,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'Join Chat Room',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                        iconSize: 20,
                      ),
                    ],
                  ),
                ),
                // Content
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Chat Room Information
                          _buildRoomInfo(),

                          const SizedBox(height: 20),

                          // User Name
                          CustomTextField(
                            controller: _userNameController,
                            label: 'Your Name',
                            hintText: 'Name that other users will see',
                            prefixIcon: const Icon(Icons.person_outline),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter your name';
                              }
                              if (value.trim().length < 2) {
                                return 'Name must be at least 2 characters';
                              }
                              if (value.trim().length > 20) {
                                return 'Name cannot exceed 20 characters';
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 16),

                          // Room Code
                          CustomTextField(
                            controller: _roomCodeController,
                            label: 'Room Code (6 digits)',
                            hintText: 'Please enter the chat room code',
                            prefixIcon: const Icon(Icons.lock_outline),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(6),
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter room code';
                              }
                              if (!OfflineChatSession.isValidRoomCode(value)) {
                                return 'Room code must be 6 digits';
                              }
                              return null;
                            },
                            onChanged: (value) {
                              // Real-time validation of room code format
                              setState(() {});
                            },
                          ),

                          const SizedBox(height: 8),

                          // Room Code Status Indicator
                          _buildRoomCodeStatus(),

                          const SizedBox(height: 20),

                          // Description Text
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: AppColors.warning.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.security,
                                  color: AppColors.warning,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'Please get the correct room code from the chat room creator',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.copyWith(
                                          color: AppColors.warning,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                // Actions
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    border: Border(
                      top: BorderSide(color: Colors.grey[200]!),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: _isJoining
                              ? null
                              : () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: CustomButton(
                          text: 'Join Chat Room',
                          onPressed: _isJoining ? null : _joinRoom,
                          isLoading: _isJoining,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildRoomInfo() {
    // Temporarily display mock data, should actually use widget.room information
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const CircleAvatar(
                backgroundColor: AppColors.primary,
                radius: 20,
                child: Icon(
                  Icons.chat,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Chat Room Name', // Should actually be widget.room?.roomName ?? 'Unknown Chat Room'
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Creator: Unknown', // Should actually be widget.room?.creatorName ?? 'Unknown'
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildInfoChip(
                icon: Icons.people,
                label:
                    '1/8', // Should actually be '${widget.room?.memberCount ?? 0}/${widget.room?.maxMembers ?? 8}'
                color: AppColors.primary,
              ),
              const SizedBox(width: 8),
              _buildInfoChip(
                icon: Icons.signal_wifi_4_bar,
                label:
                    'Good Signal', // Should actually display based on widget.room?.signalQuality
                color: AppColors.success,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoomCodeStatus() {
    final roomCode = _roomCodeController.text;

    if (roomCode.isEmpty) {
      return const SizedBox.shrink();
    }

    final isValid = OfflineChatSession.isValidRoomCode(roomCode);

    return Row(
      children: [
        Icon(
          isValid ? Icons.check_circle : Icons.error,
          color: isValid ? AppColors.success : Colors.red,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(
          isValid
              ? 'Room code format is correct'
              : 'Room code must be 6 digits',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: isValid ? AppColors.success : Colors.red,
              ),
        ),
      ],
    );
  }

  void _joinRoom() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isJoining = true;
    });

    final userName = _userNameController.text.trim();
    final roomCode = _roomCodeController.text.trim();

    // Temporarily create a mock DiscoveredChatRoom
    // Should actually use widget.room
    final mockRoom = DiscoveredChatRoom(
      sessionId: 'mock_session',
      roomName: 'Chat Room Name',
      creatorName: 'Unknown',
      memberCount: 1,
      maxMembers: 8,
      deviceId: 'mock_device',
      signalStrength: -60,
      lastSeen: DateTime.now(),
    );

    context.read<OfflineChatBloc>().add(
          OfflineChatJoinRoomRequested(
            room: mockRoom,
            userName: userName,
            roomCode: roomCode,
          ),
        );
  }
}
