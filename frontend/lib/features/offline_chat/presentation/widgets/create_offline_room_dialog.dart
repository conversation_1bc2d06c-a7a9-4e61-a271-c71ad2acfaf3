import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../data/models/offline_chat_models.dart';
import '../bloc/offline_chat_bloc.dart';

/// Create offline chat room dialog
class CreateOfflineRoomDialog extends StatefulWidget {
  const CreateOfflineRoomDialog({super.key});

  @override
  State<CreateOfflineRoomDialog> createState() =>
      _CreateOfflineRoomDialogState();
}

class _CreateOfflineRoomDialogState extends State<CreateOfflineRoomDialog> {
  final _formKey = GlobalKey<FormState>();
  final _roomNameController = TextEditingController();
  final _creatorNameController = TextEditingController();
  final _roomCodeController = TextEditingController();

  bool _isCreating = false;

  @override
  void dispose() {
    _roomNameController.dispose();
    _creatorNameController.dispose();
    _roomCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<OfflineChatBloc, OfflineChatState>(
      listener: (context, state) {
        if (state is OfflineChatInRoom) {
          // Successfully created chat room, safely close dialog
          if (Navigator.of(context).canPop()) {
            Navigator.of(context).pop();
          }
        } else if (state is OfflineChatError) {
          setState(() {
            _isCreating = false;
          });
        }
      },
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.9,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                // Drag handle
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                // Header
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.add_circle_outline,
                        color: AppColors.primary,
                      ),
                      const SizedBox(width: 12),
                      const Text(
                        'Create Chat Room',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: _isCreating
                            ? null
                            : () {
                                if (Navigator.of(context).canPop()) {
                                  Navigator.of(context).pop();
                                }
                              },
                        icon: const Icon(Icons.close),
                        iconSize: 20,
                      ),
                    ],
                  ),
                ),
                // Content
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Room Name
                          CustomTextField(
                            controller: _roomNameController,
                            label: 'Chat Room Name',
                            hintText:
                                'e.g.: Friends Gathering, Work Discussion',
                            prefixIcon: const Icon(Icons.chat_bubble_outline),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter chat room name';
                              }
                              if (value.trim().length < 2) {
                                return 'Chat room name must be at least 2 characters';
                              }
                              if (value.trim().length > 30) {
                                return 'Chat room name cannot exceed 30 characters';
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 16),

                          // Creator Name
                          CustomTextField(
                            controller: _creatorNameController,
                            label: 'Your Name',
                            hintText: 'Name that other users will see',
                            prefixIcon: const Icon(Icons.person_outline),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please enter your name';
                              }
                              if (value.trim().length < 2) {
                                return 'Name must be at least 2 characters';
                              }
                              if (value.trim().length > 20) {
                                return 'Name cannot exceed 20 characters';
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 16),

                          // Room Code
                          Row(
                            children: [
                              Expanded(
                                child: CustomTextField(
                                  controller: _roomCodeController,
                                  label: 'Room Code (6 digits)',
                                  hintText: '123456',
                                  prefixIcon: const Icon(Icons.lock_outline),
                                  keyboardType: TextInputType.number,
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                    LengthLimitingTextInputFormatter(6),
                                  ],
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter room code';
                                    }
                                    if (!OfflineChatSession.isValidRoomCode(
                                        value)) {
                                      return 'Room code must be 6 digits';
                                    }
                                    return null;
                                  },
                                  onChanged: (value) {
                                    // Real-time validation of room code format
                                    setState(() {});
                                  },
                                ),
                              ),
                              const SizedBox(width: 12),
                              IconButton(
                                onPressed: _generateRandomRoomCode,
                                icon: const Icon(Icons.refresh),
                                tooltip: 'Generate Random Room Code',
                                style: IconButton.styleFrom(
                                  backgroundColor:
                                      AppColors.primary.withValues(alpha: 0.1),
                                  foregroundColor: AppColors.primary,
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 8),

                          // Room Code Status Indicator
                          _buildRoomCodeStatus(),

                          const SizedBox(height: 20),

                          // Description Text
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: AppColors.primary.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.info_outline,
                                  color: AppColors.primary,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'Other users need to enter the correct room code to join the chat room',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.copyWith(
                                          color: AppColors.primary,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                // Footer
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: _isCreating
                              ? null
                              : () {
                                  if (Navigator.of(context).canPop()) {
                                    Navigator.of(context).pop();
                                  }
                                },
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        flex: 2,
                        child: CustomButton(
                          text: 'Create Chat Room',
                          onPressed: _isCreating ? null : _createRoom,
                          isLoading: _isCreating,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildRoomCodeStatus() {
    final roomCode = _roomCodeController.text;

    if (roomCode.isEmpty) {
      return const SizedBox.shrink();
    }

    final isValid = OfflineChatSession.isValidRoomCode(roomCode);

    return Row(
      children: [
        Icon(
          isValid ? Icons.check_circle : Icons.error,
          color: isValid ? AppColors.success : Colors.red,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(
          isValid
              ? 'Room code format is correct'
              : 'Room code must be 6 digits',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: isValid ? AppColors.success : Colors.red,
              ),
        ),
      ],
    );
  }

  void _generateRandomRoomCode() {
    final randomCode = OfflineChatSession.generateRandomRoomCode();
    _roomCodeController.text = randomCode;
    setState(() {});
  }

  void _createRoom() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isCreating = true;
    });

    final roomName = _roomNameController.text.trim();
    final creatorName = _creatorNameController.text.trim();
    final roomCode = _roomCodeController.text.trim();

    context.read<OfflineChatBloc>().add(
          OfflineChatCreateRoomRequested(
            roomName: roomName,
            creatorName: creatorName,
            roomCode: roomCode,
          ),
        );
  }
}
