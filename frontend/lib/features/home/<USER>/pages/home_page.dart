﻿import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../../core/network/api_client.dart';
import '../../../../core/utils/responsive_utils.dart';
import '../../../../core/services/time_service.dart';
import '../../../../core/widgets/loading_state_manager.dart';
import '../../../../core/widgets/skeleton_loader.dart';
import '../../../../core/widgets/enhanced_calendar.dart';
import '../../../../core/widgets/calendar_controller.dart';
import '../../../../core/animations/animation_manager.dart';
import '../../../../core/animations/enhanced_page_transitions.dart';
import '../../../../core/animations/micro_interactions.dart';
import '../../../../core/animations/enhanced_list_animations.dart';
import '../../../../core/utils/back_button_handler.dart';
import '../bloc/timeline_bloc.dart';
import '../../data/models/post_models.dart';
import '../widgets/enhanced_calendar_post_card.dart';
import '../../../spaces/presentation/bloc/spaces_bloc.dart';
import '../../../spaces/presentation/widgets/space_card.dart';
import '../../../spaces/data/models/space_models.dart';
import '../widgets/responsive_timeline.dart';

class HomePage extends StatefulWidget {
  final int? initialTabIndex;

  const HomePage({super.key, this.initialTabIndex});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  late TabController _tabController;
  late DateTime _selectedDate;
  late DateTime _focusedDay;
  CalendarFormat _calendarFormat = CalendarFormat.month;
  late CalendarController _calendarController;
  bool _isTimelineInitialized =
      false; // Prevent duplicate Timeline initialization

  @override
  void initState() {
    super.initState();
    final today = DateTime.now();
    _selectedDate = DateTime(today.year, today.month, today.day);
    _focusedDay = _selectedDate;

    // Use passed initial tab index, default to 0 (Timeline)
    final initialIndex = widget.initialTabIndex ?? 0;
    _tabController = TabController(
      length: 3,
      vsync: this,
      initialIndex:
          initialIndex.clamp(0, 2), // Ensure index is within valid range
    );

    _calendarController = CalendarController(
      initialDate: _selectedDate,
      initialFormat: _calendarFormat,
    );

    // Add TabController listener to handle tab switching
    _tabController.addListener(() {
      if (mounted) {
        setState(() {
          // Trigger UI update to show correct FAB
        });
      }
    });

    // 延迟初始化Timeline数据，避免在build中重复触发
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_isTimelineInitialized) {
        context.read<TimelineBloc>().add(TimelineLoadRequested());
        _isTimelineInitialized = true;
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 当屏幕方向或尺寸变化时，延迟一帧再更新UI
    // 这有助于避免在布局变化过程中的渲染问题
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          // 触发重建以适应新的屏幕方向
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _calendarController.dispose();
    super.dispose();
  }

  void _refreshSpacesData() {
    try {
      final spacesBloc = DependencyInjection.getIt<SpacesBloc>();
      spacesBloc.add(SpacesRefreshRequested());
    } catch (e) {
      setState(() {});
    }
  }

  Widget _buildTimelineLayout(List<PostModel> posts, bool hasMore) {
    return ResponsiveTimeline(
      key: ValueKey(
        'timeline_${posts.length}_$hasMore',
      ), // 只有在posts或hasMore变化时才重建
      posts: posts,
      selectedDate: _selectedDate,
      onDateSelected: (DateTime date) {
        // 使用更轻量的状态更新，避免不必要的重建
        if (_selectedDate != date) {
          setState(() {
            _selectedDate = date;
          });
        }
      },
      onLoadMore: hasMore
          ? () {
              context.read<TimelineBloc>().add(TimelineLoadMoreRequested());
            }
          : null,
      hasMore: hasMore,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BackButtonHandler.wrapWithBackHandler(
      context: context,
      isMainPage: true,
      pageName: 'HomePage',
      child: MultiBlocProvider(
        providers: [
          BlocProvider.value(value: DependencyInjection.getIt<TimelineBloc>()),
          BlocProvider.value(value: DependencyInjection.getIt<SpacesBloc>()),
        ],
        child: ResponsiveBuilder(
          builder: (context, deviceType) {
            // 检查布局稳定性，避免在屏幕方向变化时渲染
            if (!ResponsiveUtils.isLayoutStable(context)) {
              return const Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }

            return Scaffold(
              appBar: _buildResponsiveAppBar(context, deviceType),
              body: _buildResponsiveBody(context, deviceType),
              floatingActionButton: _buildResponsiveFAB(context, deviceType),
            );
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildResponsiveAppBar(
    BuildContext context,
    DeviceType deviceType,
  ) {
    return AppBar(
      title: const Text(AppConstants.appName),
      actions: [
        IconButton(
          icon: const Icon(Icons.person),
          onPressed: () async {
            try {
              context.push('/profile');
            } catch (e) {
              // Handle navigation error silently
            }
          },
        ),
      ],
      bottom: ResponsiveUtils.shouldUseBottomNavigation(context)
          ? TabBar(
              controller: _tabController,
              tabs: const [
                Tab(icon: Icon(Icons.timeline), text: 'Timeline'),
                Tab(icon: Icon(Icons.calendar_month), text: 'Calendar'),
                Tab(icon: Icon(Icons.workspaces), text: 'Spaces'),
              ],
            )
          : null,
    );
  }

  Widget _buildResponsiveBody(BuildContext context, DeviceType deviceType) {
    if (ResponsiveUtils.shouldUseBottomNavigation(context)) {
      // 使用增强的Tab切换动画
      return AnimatedTabSwitcher(
        currentIndex: _tabController.index,
        duration: AnimationManager.normalDuration,
        curve: AnimationManager.smoothCurve,
        children: [
          _buildTimelineView(),
          _buildCalendarView(),
          _buildSpacesView(),
        ],
      );
    } else {
      return Row(
        children: [
          // 优化的NavigationRail，特别处理iPad横版
          SizedBox(
            width: ResponsiveUtils.getIPadNavigationWidth(context),
            child: NavigationRail(
              selectedIndex: _tabController.index,
              onDestinationSelected: (index) {
                if (mounted) {
                  setState(() {
                    _tabController.index = index;
                  });
                }
              },
              labelType: ResponsiveUtils.shouldUseIPadLandscapeLayout(context)
                  ? NavigationRailLabelType.all
                  : NavigationRailLabelType.selected,
              minWidth: ResponsiveUtils.shouldUseIPadLandscapeLayout(context)
                  ? 80.0
                  : 56.0,
              destinations: const [
                NavigationRailDestination(
                  icon: Icon(Icons.timeline),
                  label: Text('Timeline'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.calendar_month),
                  label: Text('Calendar'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.workspaces),
                  label: Text('Spaces'),
                ),
              ],
            ),
          ),
          const VerticalDivider(thickness: 1, width: 1),
          Expanded(
            child: IndexedStack(
              index: _tabController.index,
              children: [
                _buildTimelineView(),
                _buildCalendarView(),
                _buildSpacesView(),
              ],
            ),
          ),
        ],
      );
    }
  }

  Widget? _buildResponsiveFAB(BuildContext context, DeviceType deviceType) {
    if (_tabController.index == 0) {
      // Timeline页面添加测试按钮
      return FloatingActionButton(
        onPressed: () => context.push('/test-ios-gesture'),
        tooltip: 'Test iOS Gesture',
        child: const Icon(Icons.bug_report),
      );
    } else if (_tabController.index == 2) {
      return _SpacesFAB(
        onCreateSpace: () => context.go('/spaces/create'),
        onJoinSpace: () => _showJoinSpaceDialog(context),
      );
    }
    return null;
  }

  Widget _buildTimelineView() {
    return BlocBuilder<TimelineBloc, TimelineState>(
      // 优化重建条件：只有当TimelineState真正改变时才重建
      buildWhen: (previous, current) {
        // 如果状态类型不同，需要重建
        if (previous.runtimeType != current.runtimeType) return true;

        // 如果都是TimelineLoaded状态，比较关键属性
        if (previous is TimelineLoaded && current is TimelineLoaded) {
          return previous.posts != current.posts ||
              previous.hasMore != current.hasMore ||
              previous.isRefreshing != current.isRefreshing;
        }

        // 其他情况都需要重建
        return true;
      },
      builder: (context, state) {
        return LoadingStateManager(
          type: _getLoadingStateType(state),
          isLoading: _isLoadingState(state),
          hasError: state is TimelineError,
          errorMessage:
              state is TimelineError ? state.error.displayMessage : null,
          onRetry: state is TimelineError && state.canRetry
              ? () {
                  context.read<TimelineBloc>().add(TimelineLoadRequested());
                }
              : null,
          loadingWidget: state is TimelineLoading && state.showSkeleton
              ? const SkeletonTimeline()
              : null,
          child: state is TimelineLoaded
              ? _buildTimelineContentWithRefresh(state)
              : state is TimelineInitial
                  ? _buildInitialTimelineView()
                  : const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildInitialTimelineView() {
    // 显示空的Timeline布局，避免闪烁
    return _buildTimelineLayout([], false);
  }

  Widget _buildInitialCalendarView() {
    // 显示空的Calendar布局，避免闪烁
    return _buildExpandedCalendar([]);
  }

  Widget _buildTimelineContentWithRefresh(TimelineLoaded state) {
    return LoadingStateManager(
      type: LoadingStateType.refresh,
      isLoading: state.isRefreshing,
      child: RefreshIndicator(
        onRefresh: () async {
          context.read<TimelineBloc>().add(TimelineRefreshRequested());
        },
        child: _buildTimelineLayout(state.posts, state.hasMore),
      ),
    );
  }

  LoadingStateType _getLoadingStateType(TimelineState state) {
    if (state is TimelineLoading) {
      if (state.isRefresh) {
        return LoadingStateType.refresh;
      } else if (state.showSkeleton) {
        return LoadingStateType.skeleton;
      } else {
        return LoadingStateType.spinner;
      }
    } else if (state is TimelineLoadingMore) {
      return LoadingStateType.loadMore;
    }
    return LoadingStateType.initial;
  }

  bool _isLoadingState(TimelineState state) {
    return state is TimelineLoading || state is TimelineLoadingMore;
  }

  Widget _buildCalendarView() {
    return BlocBuilder<TimelineBloc, TimelineState>(
      // 优化重建条件：只有当TimelineState真正改变时才重建
      buildWhen: (previous, current) {
        // 如果状态类型不同，需要重建
        if (previous.runtimeType != current.runtimeType) return true;

        // 如果都是TimelineLoaded状态，比较关键属性
        if (previous is TimelineLoaded && current is TimelineLoaded) {
          return previous.posts != current.posts ||
              previous.hasMore != current.hasMore ||
              previous.isRefreshing != current.isRefreshing;
        }

        // 其他情况都需要重建
        return true;
      },
      builder: (context, state) {
        return LoadingStateManager(
          type: _getLoadingStateType(state),
          isLoading: _isLoadingState(state),
          hasError: state is TimelineError,
          errorMessage:
              state is TimelineError ? state.error.displayMessage : null,
          onRetry: state is TimelineError && state.canRetry
              ? () {
                  context.read<TimelineBloc>().add(TimelineLoadRequested());
                }
              : null,
          loadingWidget: state is TimelineLoading && state.showSkeleton
              ? _buildCalendarSkeleton()
              : null,
          child: state is TimelineLoaded
              ? _buildCalendarContentWithRefresh(state)
              : state is TimelineInitial
                  ? _buildInitialCalendarView()
                  : const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildCalendarContentWithRefresh(TimelineLoaded state) {
    return LoadingStateManager(
      type: LoadingStateType.refresh,
      isLoading: state.isRefreshing,
      child: RefreshIndicator(
        onRefresh: () async {
          context.read<TimelineBloc>().add(TimelineRefreshRequested());
        },
        child: _buildExpandedCalendar(state.posts),
      ),
    );
  }

  Widget _buildExpandedCalendar(List<PostModel> posts) {
    _calendarController.updatePosts(posts);

    return Column(
      children: [
        // Calendar widget
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
          ),
          child: EnhancedCalendar(
            posts: posts,
            selectedDate: _selectedDate,
            focusedDay: _focusedDay,
            onDateSelected: (selectedDay) {
              // Only update state without triggering data refresh
              setState(() {
                _selectedDate = selectedDay;
              });
              _calendarController.selectDate(selectedDay);
              // No need to refresh data - just update the UI
            },
            onPageChanged: (focusedDay) {
              setState(() {
                _focusedDay = focusedDay;
              });
              _calendarController.updateFocusedDay(focusedDay);
            },
            calendarFormat: _calendarFormat,
            onFormatChanged: (format) {
              setState(() {
                _calendarFormat = format;
              });
              _calendarController.updateCalendarFormat(format);
            },
          ),
        ),

        // Selected date posts section
        _buildSelectedDatePosts(posts),
      ],
    );
  }

  Widget _buildCalendarSkeleton() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildSpacesView() {
    return Container(color: AppColors.background, child: _buildSpacesContent());
  }

  Widget _buildSpacesContent() {
    return BlocBuilder<SpacesBloc, SpacesState>(
      bloc: DependencyInjection.getIt<SpacesBloc>(),
      builder: (context, state) {
        if (state is SpacesLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is SpacesError) {
          return _buildSpacesError(state.message);
        } else if (state is SpacesLoaded) {
          if (state.spaces.isEmpty) {
            return _buildEmptySpaces();
          } else {
            return _buildSpacesList(state.spaces);
          }
        } else {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            DependencyInjection.getIt<SpacesBloc>().add(SpacesLoadRequested());
          });
          return const Center(child: CircularProgressIndicator());
        }
      },
    );
  }

  Widget _buildSpacesError(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: AppColors.error),
          const SizedBox(height: 16),
          Text(
            'Error loading spaces',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              setState(() {});
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptySpaces() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Unified empty state icon design
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(60),
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.2),
                  width: 2,
                ),
              ),
              child: const Icon(
                Icons.workspaces_outlined,
                size: 64,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 32),
            Text(
              'No Spaces Yet',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
            ),
            const SizedBox(height: 16),
            Text(
              'Create your first space to start sharing memories\nwith friends and family.',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                    height: 1.5,
                  ),
            ),
            const SizedBox(height: 40),
            // Unified button design style
            Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => context.go('/spaces/create'),
                    icon: const Icon(Icons.add),
                    label: const Text('Create New Space'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: () => _showJoinSpaceDialog(context),
                    icon: const Icon(Icons.group_add),
                    label: const Text('Join Existing Space'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      side: const BorderSide(color: AppColors.primary),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpacesList(List<SpaceModel> spaces) {
    return RefreshIndicator(
      onRefresh: () async {
        _refreshSpacesData();
      },
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        itemCount: spaces.length,
        physics: const BouncingScrollPhysics(),
        itemBuilder: (context, index) {
          final space = spaces[index];
          return StaggeredListAnimation(
            index: index,
            baseDuration: AnimationManager.normalDuration,
            staggerDelay: 50,
            curve: AnimationManager.listItemCurve,
            child: InteractiveListItem(
              onTap: () => context.push('/spaces/${space.id}'),
              enableHaptic: true,
              child: SpaceCard(
                space: space,
                onTap: () => context.push('/spaces/${space.id}'),
                onAcceptInvite: () => _acceptSpaceInvite(space),
                onDeclineInvite: () => _declineSpaceInvite(space),
              ),
            ),
          );
        },
      ),
    );
  }

  void _showJoinSpaceDialog(BuildContext context) {
    final TextEditingController inviteCodeController = TextEditingController();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.5,
          minChildSize: 0.4,
          maxChildSize: 0.7,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                // Drag handle
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(top: 12, bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                // Header
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
                  child: Row(
                    children: [
                      const Icon(Icons.group_add, color: AppColors.primary),
                      const SizedBox(width: 8),
                      const Text(
                        'Join Space',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                        iconSize: 20,
                      ),
                    ],
                  ),
                ),
                // Content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Enter the invite code to join a space:',
                          style: TextStyle(fontSize: 16),
                        ),
                        const SizedBox(height: 16),
                        TextField(
                          controller: inviteCodeController,
                          autofocus: true,
                          decoration: const InputDecoration(
                            labelText: 'Invite Code',
                            hintText: 'Enter invite code here',
                            border: OutlineInputBorder(),
                          ),
                          textCapitalization: TextCapitalization.characters,
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
                // Actions
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      top: BorderSide(color: Colors.grey[200]!),
                    ),
                  ),
                  child: SafeArea(
                    child: Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: const Text('Cancel'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              final inviteCode =
                                  inviteCodeController.text.trim();
                              if (inviteCode.isNotEmpty) {
                                Navigator.of(context).pop();
                                // TODO: Implement join space logic
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                        'Joining space with code: $inviteCode'),
                                  ),
                                );
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primary,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Join'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildSelectedDatePosts(List<PostModel> posts) {
    final selectedDatePosts = _calendarController.getSelectedDatePosts();

    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              children: [
                const Icon(
                  Icons.event_note,
                  color: AppColors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Posts on ${_formatSelectedDate(_selectedDate)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.primary,
                      ),
                ),
                const Spacer(),
                Text(
                  '${selectedDatePosts.length} posts',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                ),
              ],
            ),
          ),

          // Posts list
          Expanded(
            child: selectedDatePosts.isEmpty
                ? _buildEmptyPostsState()
                : _buildPostsList(selectedDatePosts),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyPostsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.event_busy, size: 48, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No posts on this date',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Select a different date or create a new post',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPostsList(List<PostModel> posts) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
      ),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        final post = posts[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: EnhancedCalendarPostCard(
            post: post,
            onTap: () => _onPostTap(post),
          ),
        );
      },
    );
  }

  String _formatSelectedDate(DateTime date) {
    final now = TimeService.instance.now;
    final today = DateTime(now.year, now.month, now.day);
    final selectedDay = DateTime(date.year, date.month, date.day);

    if (selectedDay == today) {
      return 'Today';
    } else if (selectedDay == today.subtract(const Duration(days: 1))) {
      return 'Yesterday';
    } else if (selectedDay == today.add(const Duration(days: 1))) {
      return 'Tomorrow';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _onPostTap(PostModel post) {
    // Navigate to post detail page
    context.go('/spaces/${post.spaceId}/posts/${post.id}');
  }

  Future<void> _acceptSpaceInvite(SpaceModel space) async {
    try {
      final apiClient = DependencyInjection.getIt<ApiClient>();
      final response = await apiClient.acceptSpaceInvite(space.id);

      if (response.success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Joined space: ${space.name}'),
              backgroundColor: Colors.green,
            ),
          );
        }
        // Refresh spaces data to update the UI
        _refreshSpacesData();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Failed to join space: $e'),
              backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _declineSpaceInvite(SpaceModel space) async {
    try {
      final apiClient = DependencyInjection.getIt<ApiClient>();
      final response = await apiClient.declineSpaceInvite(space.id);

      if (response.success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Declined invitation: ${space.name}'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        // Refresh spaces data to update the UI
        _refreshSpacesData();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Failed to decline invitation: $e'),
              backgroundColor: Colors.red),
        );
      }
    }
  }
}

class _SpacesFAB extends StatefulWidget {
  final VoidCallback onCreateSpace;
  final VoidCallback onJoinSpace;

  const _SpacesFAB({required this.onCreateSpace, required this.onJoinSpace});

  @override
  State<_SpacesFAB> createState() => _SpacesFABState();
}

class _SpacesFABState extends State<_SpacesFAB> {
  bool _isExpanded = false;

  void _toggle() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Expanded options
        if (_isExpanded) ...[
          // Join Space option
          _buildFABOption(
            label: 'Join Space',
            icon: Icons.group_add,
            onPressed: () {
              _toggle();
              widget.onJoinSpace();
            },
          ),
          const SizedBox(height: 8),

          // Create Space option
          _buildFABOption(
            label: 'Create Space',
            icon: Icons.add_circle_outline,
            onPressed: () {
              _toggle();
              widget.onCreateSpace();
            },
          ),
          const SizedBox(height: 12),
        ],

        // Main FAB with enhanced animation
        TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 250),
          tween: Tween(begin: 0.0, end: _isExpanded ? 1.0 : 0.0),
          curve: Curves.easeInOutBack,
          builder: (context, value, child) {
            // Clamp value to ensure it stays within valid range
            final clampedValue = value.clamp(0.0, 1.0);

            return Transform.scale(
              scale: 1.0 + (clampedValue * 0.1),
              child: Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: Color.lerp(
                    AppColors.primary,
                    AppColors.primaryDark,
                    clampedValue,
                  ),
                  borderRadius: BorderRadius.circular(28),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withValues(
                        alpha: 0.3 + (clampedValue * 0.2),
                      ),
                      blurRadius: 12 + (clampedValue * 8),
                      offset: Offset(0, 4 + (clampedValue * 2)),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(28),
                    onTap: _toggle,
                    child: Transform.rotate(
                      angle: clampedValue * 0.785, // 45 degrees in radians
                      child: Icon(
                        _isExpanded ? Icons.close : Icons.add,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildFABOption({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 300),
      tween: Tween(begin: 0.0, end: _isExpanded ? 1.0 : 0.0),
      curve: Curves.easeOutBack,
      builder: (context, value, child) {
        // Clamp value to ensure it stays within valid range
        final clampedValue = value.clamp(0.0, 1.0);

        return Transform.translate(
          offset: Offset(0, (1 - clampedValue) * 20),
          child: Transform.scale(
            scale: 0.8 + (clampedValue * 0.2),
            child: Opacity(
              opacity: clampedValue,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Label with enhanced animation
                  Transform.translate(
                    offset: Offset((1 - clampedValue) * 30, 0),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.surface,
                        borderRadius: BorderRadius.circular(14),
                        border: Border.all(color: AppColors.border, width: 1),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withValues(alpha: 0.15),
                            blurRadius: 12,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Text(
                        label,
                        style: const TextStyle(
                          color: AppColors.textPrimary,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.2,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),

                  // Mini FAB with bounce animation
                  Transform.rotate(
                    angle: (1 - clampedValue) * 0.5,
                    child: Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(22),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withValues(alpha: 0.4),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(22),
                          onTap: onPressed,
                          child: Icon(icon, color: Colors.white, size: 20),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
