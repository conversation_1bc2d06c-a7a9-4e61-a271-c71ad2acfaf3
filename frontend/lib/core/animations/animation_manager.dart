import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';
import 'micro_interactions.dart';
import 'enhanced_page_transitions.dart';
import 'enhanced_list_animations.dart';
import 'enhanced_loading_animations.dart';

/// 动画管理器
/// 统一管理应用中的所有动画效果和时长
class AnimationManager {
  // 动画时长配置
  static const Duration ultraFastDuration = Duration(milliseconds: 100);
  static const Duration fastDuration = Duration(milliseconds: 150);
  static const Duration normalDuration = Duration(milliseconds: 300);
  static const Duration slowDuration = Duration(milliseconds: 500);
  static const Duration extraSlowDuration = Duration(milliseconds: 800);

  // 缓动曲线配置
  static const Curve defaultCurve = Curves.easeInOut;
  static const Curve fastCurve = Curves.easeOut;
  static const Curve slowCurve = Curves.easeInOutCubic;
  static const Curve bounceCurve = Curves.elasticOut;
  static const Curve springCurve = Curves.bounceOut;

  // 微交互专用曲线
  static const Curve buttonPressCurve = Curves.easeInQuart;
  static const Curve buttonReleaseCurve = Curves.easeOutBack;
  static const Curve listItemCurve = Curves.easeOutCubic;
  static const Curve pageTransitionCurve = Curves.easeInOutCubic;
  static const Curve smoothCurve = Curves.easeInOutCubic;
  static const Curve sharpCurve = Curves.easeInOutQuart;

  /// 获取响应式动画时长
  static Duration getResponsiveDuration(
    BuildContext context, {
    Duration baseDuration = normalDuration,
  }) {
    return ResponsiveUtils.getResponsiveAnimationDuration(context);
  }

  /// 获取页面过渡动画
  static PageRouteBuilder<T> createPageRoute<T>({
    required Widget page,
    PageTransitionType type = PageTransitionType.slideFromRight,
    Duration? duration,
    Curve? curve,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration ?? normalDuration,
      reverseTransitionDuration: duration ?? normalDuration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return _buildPageTransition(
          type: type,
          animation: animation,
          secondaryAnimation: secondaryAnimation,
          child: child,
          curve: curve ?? defaultCurve,
        );
      },
    );
  }

  /// 构建页面过渡动画
  static Widget _buildPageTransition({
    required PageTransitionType type,
    required Animation<double> animation,
    required Animation<double> secondaryAnimation,
    required Widget child,
    required Curve curve,
  }) {
    final curvedAnimation = CurvedAnimation(
      parent: animation,
      curve: curve,
    );

    switch (type) {
      case PageTransitionType.fade:
        return FadeTransition(
          opacity: curvedAnimation,
          child: child,
        );

      case PageTransitionType.slideFromRight:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );

      case PageTransitionType.slideFromLeft:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(-1.0, 0.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );

      case PageTransitionType.slideFromBottom:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.0, 1.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );

      case PageTransitionType.slideFromTop:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.0, -1.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );

      case PageTransitionType.scale:
        return ScaleTransition(
          scale: curvedAnimation,
          child: child,
        );

      case PageTransitionType.rotation:
        return RotationTransition(
          turns: curvedAnimation,
          child: child,
        );

      case PageTransitionType.slideAndFade:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.3, 0.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: FadeTransition(
            opacity: curvedAnimation,
            child: child,
          ),
        );

      case PageTransitionType.scaleAndFade:
        return ScaleTransition(
          scale: Tween<double>(
            begin: 0.8,
            end: 1.0,
          ).animate(curvedAnimation),
          child: FadeTransition(
            opacity: curvedAnimation,
            child: child,
          ),
        );

      case PageTransitionType.smoothSlide:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );

      case PageTransitionType.scaleFade:
        return ScaleTransition(
          scale: Tween<double>(
            begin: 0.8,
            end: 1.0,
          ).animate(curvedAnimation),
          child: FadeTransition(
            opacity: curvedAnimation,
            child: child,
          ),
        );

      case PageTransitionType.sharedAxis:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.3, 0.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: FadeTransition(
            opacity: curvedAnimation,
            child: child,
          ),
        );
    }
  }

  /// 创建列表项动画
  static Widget createListItemAnimation({
    required Widget child,
    required int index,
    Duration? delay,
    Duration? duration,
    Curve? curve,
  }) {
    return AnimatedListItem(
      index: index,
      delay: delay ?? Duration(milliseconds: index * 50),
      duration: duration ?? normalDuration,
      curve: curve ?? defaultCurve,
      child: child,
    );
  }

  /// 创建悬浮动画
  static Widget createHoverAnimation({
    required Widget child,
    double scale = 1.05,
    Duration? duration,
    Curve? curve,
  }) {
    return HoverAnimationWidget(
      scale: scale,
      duration: duration ?? fastDuration,
      curve: curve ?? fastCurve,
      child: child,
    );
  }

  /// 创建脉冲动画
  static Widget createPulseAnimation({
    required Widget child,
    double minScale = 0.95,
    double maxScale = 1.05,
    Duration? duration,
  }) {
    return PulseAnimationWidget(
      minScale: minScale,
      maxScale: maxScale,
      duration: duration ?? slowDuration,
      child: child,
    );
  }

  /// 创建摇摆动画
  static Widget createShakeAnimation({
    required Widget child,
    double offset = 10.0,
    Duration? duration,
  }) {
    return ShakeAnimationWidget(
      offset: offset,
      duration: duration ?? fastDuration,
      child: child,
    );
  }

  /// 创建弹跳动画
  static Widget createBounceAnimation({
    required Widget child,
    Duration? duration,
    Curve? curve,
  }) {
    return BounceAnimationWidget(
      duration: duration ?? normalDuration,
      curve: curve ?? bounceCurve,
      child: child,
    );
  }

  /// 创建按钮点击动画
  static Widget createButtonPressAnimation({
    required Widget child,
    VoidCallback? onTap,
    double pressScale = 0.95,
    Duration? duration,
  }) {
    return ButtonPressAnimation(
      onTap: onTap,
      pressScale: pressScale,
      duration: duration ?? ultraFastDuration,
      child: child,
    );
  }

  /// 创建触觉反馈按钮
  static Widget createHapticButton({
    required Widget child,
    VoidCallback? onTap,
    HapticFeedbackType feedbackType = HapticFeedbackType.light,
    double pressScale = 0.95,
  }) {
    return HapticButton(
      onTap: onTap,
      feedbackType: feedbackType,
      pressScale: pressScale,
      child: child,
    );
  }

  /// 创建列表项进入动画
  static Widget createStaggeredListAnimation({
    required Widget child,
    required int index,
    Duration? baseDuration,
    int? staggerDelay,
  }) {
    return StaggeredListAnimation(
      index: index,
      baseDuration: baseDuration ?? normalDuration,
      staggerDelay: staggerDelay ?? 50,
      child: child,
    );
  }

  /// 创建加载状态动画
  static Widget createLoadingAnimation({
    required Widget child,
    bool isLoading = false,
    Widget? loadingWidget,
  }) {
    return LoadingStateAnimation(
      isLoading: isLoading,
      loadingWidget: loadingWidget,
      child: child,
    );
  }

  /// 创建增强的加载指示器
  static Widget createEnhancedLoadingIndicator({
    LoadingAnimationType type = LoadingAnimationType.pulse,
    Color? color,
    double size = 40.0,
    Duration? duration,
  }) {
    return EnhancedLoadingIndicator(
      type: type,
      color: color,
      size: size,
      duration: duration ?? slowDuration,
    );
  }

  /// 创建内容加载动画
  static Widget createContentLoadingAnimation({
    required Widget child,
    bool isLoading = false,
    Widget? loadingWidget,
    Duration? duration,
  }) {
    return ContentLoadingAnimation(
      isLoading: isLoading,
      loadingWidget: loadingWidget,
      duration: duration ?? normalDuration,
      child: child,
    );
  }

  /// 创建增强的骨架屏
  static Widget createEnhancedSkeleton({
    required Widget child,
    bool isLoading = true,
    Color? baseColor,
    Color? highlightColor,
    Duration? duration,
  }) {
    return EnhancedSkeletonLoader(
      isLoading: isLoading,
      baseColor: baseColor,
      highlightColor: highlightColor,
      duration: duration ?? const Duration(milliseconds: 1500),
      child: child,
    );
  }

  /// 创建增强的动画列表
  static Widget createEnhancedAnimatedList({
    required List<Widget> children,
    Duration? staggerDelay,
    Duration? itemDuration,
    Curve? curve,
    bool enableStagger = true,
    ScrollPhysics? physics,
    EdgeInsetsGeometry? padding,
  }) {
    return EnhancedAnimatedList(
      staggerDelay: staggerDelay ?? const Duration(milliseconds: 50),
      itemDuration: itemDuration ?? normalDuration,
      curve: curve ?? listItemCurve,
      enableStagger: enableStagger,
      physics: physics,
      padding: padding,
      children: children,
    );
  }

  /// 创建可交互列表项
  static Widget createInteractiveListItem({
    required Widget child,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    bool enableHover = true,
    bool enablePress = true,
    bool enableHaptic = true,
    Duration? animationDuration,
  }) {
    return InteractiveListItem(
      onTap: onTap,
      onLongPress: onLongPress,
      enableHover: enableHover,
      enablePress: enablePress,
      enableHaptic: enableHaptic,
      animationDuration: animationDuration ?? fastDuration,
      child: child,
    );
  }

  /// 创建滑动删除项
  static Widget createSwipeToDeleteItem({
    required Widget child,
    VoidCallback? onDelete,
    Color deleteColor = Colors.red,
    IconData deleteIcon = Icons.delete,
    String deleteText = 'Delete',
    double deleteThreshold = 0.4,
  }) {
    return SwipeToDeleteItem(
      onDelete: onDelete,
      deleteColor: deleteColor,
      deleteIcon: deleteIcon,
      deleteText: deleteText,
      deleteThreshold: deleteThreshold,
      child: child,
    );
  }

  /// 创建页面转场动画
  static PageRouteBuilder<T> createPageTransition<T>({
    required Widget page,
    PageTransitionType type = PageTransitionType.smoothSlide,
    SlideDirection direction = SlideDirection.fromRight,
    Duration? duration,
    Curve? curve,
  }) {
    switch (type) {
      case PageTransitionType.smoothSlide:
        return EnhancedPageTransitions.createSmoothSlideTransition<T>(
          page: page,
          direction: direction,
          duration: duration,
          curve: curve,
        );
      case PageTransitionType.scaleFade:
        return EnhancedPageTransitions.createScaleFadeTransition<T>(
          page: page,
          duration: duration,
          curve: curve,
        );
      case PageTransitionType.sharedAxis:
        return EnhancedPageTransitions.createSharedAxisTransition<T>(
          page: page,
          duration: duration,
          curve: curve,
        );
      default:
        // 对于其他类型，使用默认的滑动转场
        return EnhancedPageTransitions.createSmoothSlideTransition<T>(
          page: page,
          direction: direction,
          duration: duration,
          curve: curve,
        );
    }
  }

  /// 创建Tab切换动画
  static Widget createAnimatedTabSwitcher({
    required int currentIndex,
    required List<Widget> children,
    Duration? duration,
    Curve? curve,
  }) {
    return AnimatedTabSwitcher(
      currentIndex: currentIndex,
      duration: duration ?? normalDuration,
      curve: curve ?? smoothCurve,
      children: children,
    );
  }
}

/// 页面转场类型枚举
enum PageTransitionType {
  fade,
  slideFromRight,
  slideFromLeft,
  slideFromBottom,
  slideFromTop,
  scale,
  rotation,
  slideAndFade,
  scaleAndFade,
  smoothSlide,
  scaleFade,
  sharedAxis,
}

/// 动画列表项组件
class AnimatedListItem extends StatefulWidget {
  final Widget child;
  final int index;
  final Duration delay;
  final Duration duration;
  final Curve curve;

  const AnimatedListItem({
    super.key,
    required this.child,
    required this.index,
    required this.delay,
    required this.duration,
    required this.curve,
  });

  @override
  State<AnimatedListItem> createState() => _AnimatedListItemState();
}

class _AnimatedListItemState extends State<AnimatedListItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));

    // 延迟启动动画
    Future.delayed(widget.delay, () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: widget.child,
      ),
    );
  }
}

/// 悬浮动画组件
class HoverAnimationWidget extends StatefulWidget {
  final Widget child;
  final double scale;
  final Duration duration;
  final Curve curve;

  const HoverAnimationWidget({
    super.key,
    required this.child,
    required this.scale,
    required this.duration,
    required this.curve,
  });

  @override
  State<HoverAnimationWidget> createState() => _HoverAnimationWidgetState();
}

class _HoverAnimationWidgetState extends State<HoverAnimationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.scale,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    if (isHovered) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: widget.child,
      ),
    );
  }
}

/// 脉冲动画组件
class PulseAnimationWidget extends StatefulWidget {
  final Widget child;
  final double minScale;
  final double maxScale;
  final Duration duration;

  const PulseAnimationWidget({
    super.key,
    required this.child,
    required this.minScale,
    required this.maxScale,
    required this.duration,
  });

  @override
  State<PulseAnimationWidget> createState() => _PulseAnimationWidgetState();
}

class _PulseAnimationWidgetState extends State<PulseAnimationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: widget.minScale,
      end: widget.maxScale,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: widget.child,
    );
  }
}

/// 摇摆动画组件
class ShakeAnimationWidget extends StatefulWidget {
  final Widget child;
  final double offset;
  final Duration duration;

  const ShakeAnimationWidget({
    super.key,
    required this.child,
    required this.offset,
    required this.duration,
  });

  @override
  State<ShakeAnimationWidget> createState() => _ShakeAnimationWidgetState();
}

class _ShakeAnimationWidgetState extends State<ShakeAnimationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _offsetAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _offsetAnimation = Tween<double>(
      begin: -widget.offset,
      end: widget.offset,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticIn,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void shake() {
    _controller.reset();
    _controller.forward();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _offsetAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(_offsetAnimation.value, 0),
          child: widget.child,
        );
      },
    );
  }
}

/// 弹跳动画组件
class BounceAnimationWidget extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Curve curve;

  const BounceAnimationWidget({
    super.key,
    required this.child,
    required this.duration,
    required this.curve,
  });

  @override
  State<BounceAnimationWidget> createState() => _BounceAnimationWidgetState();
}

class _BounceAnimationWidgetState extends State<BounceAnimationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _bounceAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _bounceAnimation,
      child: widget.child,
    );
  }
}
