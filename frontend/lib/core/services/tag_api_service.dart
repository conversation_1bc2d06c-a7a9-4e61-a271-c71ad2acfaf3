import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../network/api_response.dart';
import '../network/error_handler.dart';
import '../models/tag_model.dart';

class TagApiService {
  final Dio _dio;

  TagApiService(this._dio);

  /// Create a new tag
  Future<ApiResponse<TagData>> createTag(
    String spaceId,
    CreateTagRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '/spaces/$spaceId/tags',
        data: request.toJson(),
      );

      return ApiResponse.fromJson(
        response.data,
        (json) => TagData.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      throw ErrorHandler.handleError(e);
    }
  }

  /// Get all tags in a space
  Future<ApiResponse<List<TagData>>> getTags(
    String spaceId, {
    GetTagsRequest? request,
  }) async {
    try {
      final queryParams = request?.toQueryParams() ?? {};

      final response = await _dio.get(
        '/spaces/$spaceId/tags',
        queryParameters: queryParams,
      );

      return ApiResponse.fromJson(
        response.data,
        (json) => (json as List<dynamic>)
            .map((tag) => TagData.fromJson(tag as Map<String, dynamic>))
            .toList(),
      );
    } catch (e) {
      throw ErrorHandler.handleError(e);
    }
  }

  /// Get tag statistics for a space
  Future<ApiResponse<TagStats>> getTagStats(String spaceId) async {
    try {
      final response = await _dio.get('/spaces/$spaceId/tags/stats');

      return ApiResponse.fromJson(
        response.data,
        (json) => TagStats.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      throw ErrorHandler.handleError(e);
    }
  }

  /// Get a specific tag
  Future<ApiResponse<TagData>> getTag(String spaceId, String tagId) async {
    try {
      final response = await _dio.get('/spaces/$spaceId/tags/$tagId');

      return ApiResponse.fromJson(
        response.data,
        (json) => TagData.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      throw ErrorHandler.handleError(e);
    }
  }

  /// Update a tag
  Future<ApiResponse<TagData>> updateTag(
    String spaceId,
    String tagId,
    UpdateTagRequest request,
  ) async {
    try {
      final response = await _dio.patch(
        '/spaces/$spaceId/tags/$tagId',
        data: request.toJson(),
      );

      return ApiResponse.fromJson(
        response.data,
        (json) => TagData.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      throw ErrorHandler.handleError(e);
    }
  }

  /// Delete a tag
  Future<ApiResponse<void>> deleteTag(String spaceId, String tagId) async {
    try {
      final response = await _dio.delete('/spaces/$spaceId/tags/$tagId');

      return ApiResponse.fromJson(
        response.data,
        (json) {},
      );
    } catch (e) {
      throw ErrorHandler.handleError(e);
    }
  }

  /// Get all tags (convenience method)
  Future<List<TagData>> getAllTags(
    String spaceId,
  ) async {
    try {
      const request = GetTagsRequest(
        sortBy: 'usageCount',
        sortOrder: 'DESC',
        limit: 100,
      );

      final response = await getTags(spaceId, request: request);
      return response.data ?? [];
    } catch (e) {
      debugPrint('Error getting all tags: $e');
      return [];
    }
  }

  /// Get tag names only (for backward compatibility)
  Future<List<String>> getTagNames(
    String spaceId,
  ) async {
    try {
      const request = GetTagsRequest(
        sortBy: 'usageCount',
        sortOrder: 'DESC',
        limit: 100,
      );

      final response = await getTags(spaceId, request: request);
      return response.data?.map((tag) => tag.name).toList() ?? [];
    } catch (e) {
      debugPrint('Error getting tag names: $e');
      return [];
    }
  }

  /// Search tags by name
  Future<List<TagData>> searchTags(
    String spaceId,
    String searchTerm,
  ) async {
    try {
      final request = GetTagsRequest(
        search: searchTerm,
        sortBy: 'usageCount',
        sortOrder: 'DESC',
        limit: 50,
      );

      final response = await getTags(spaceId, request: request);
      return response.data ?? [];
    } catch (e) {
      debugPrint('Error searching tags: $e');
      return [];
    }
  }

  /// Get most used tags
  Future<List<TagData>> getMostUsedTags(
    String spaceId, {
    int limit = 10,
  }) async {
    try {
      final request = GetTagsRequest(
        sortBy: 'usageCount',
        sortOrder: 'DESC',
        limit: limit,
      );

      final response = await getTags(spaceId, request: request);
      return response.data ?? [];
    } catch (e) {
      debugPrint('Error getting most used tags: $e');
      return [];
    }
  }

  /// Get recent tags
  Future<List<TagData>> getRecentTags(
    String spaceId, {
    int limit = 10,
  }) async {
    try {
      final request = GetTagsRequest(
        sortBy: 'createdAt',
        sortOrder: 'DESC',
        limit: limit,
      );

      final response = await getTags(spaceId, request: request);
      return response.data ?? [];
    } catch (e) {
      debugPrint('Error getting recent tags: $e');
      return [];
    }
  }

  /// Get tag relations (related TODOs and ToGos)
  Future<ApiResponse<TagRelations>> getTagRelations(
    String spaceId,
    String tagId,
  ) async {
    try {
      final response = await _dio.get('/spaces/$spaceId/tags/$tagId/relations');

      return ApiResponse.fromJson(
        response.data,
        (json) => TagRelations.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      throw ErrorHandler.handleError(e);
    }
  }
}
