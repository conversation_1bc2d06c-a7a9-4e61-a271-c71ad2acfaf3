class AppConstants {
  // App Information
  static const String appName = 'MeLo';
  static const String appVersion = '1.0.0';
  static const String appDescription =
      'A collaborative memory sharing platform';

  // API Configuration (will be overridden by PlatformConfig for mobile)
  static const String baseUrl = 'http://*************:3000/api';
  static const String apiVersion = 'v1';

  // Debug Configuration
  static const bool isDebugMode = true; // Set to false for production

  // Storage Keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String onboardingCompletedKey = 'onboarding_completed';

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;

  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;

  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);

  // Image & Media
  static const int maxImageSizeMB = 10;
  static const int maxImageSizeBytes = maxImageSizeMB * 1024 * 1024;
  static const List<String> supportedImageFormats = [
    'jpg',
    'jpeg',
    'png',
    'webp'
  ];

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;

  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int maxDisplayNameLength = 50;
  static const int maxSpaceNameLength = 100;
  static const int maxSpaceDescriptionLength = 500;
  static const int maxSnapTextLength = 500;
  static const int maxDiaryTitleLength = 200;
  static const int maxTagLength = 30;
  static const int maxTagsPerPost = 10;

  // Regular Expressions
  static const String emailRegex =
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String tagRegex = r'^[a-zA-Z0-9_-]+$';

  // Error Messages
  static const String genericErrorMessage =
      'Something went wrong. Please try again.';
  static const String networkErrorMessage =
      'Network error. Please check your connection.';
  static const String timeoutErrorMessage =
      'Request timeout. Please try again.';
  static const String unauthorizedErrorMessage =
      'Session expired. Please log in again.';

  // Success Messages
  static const String loginSuccessMessage = 'Welcome back!';
  static const String registerSuccessMessage = 'Account created successfully!';
  static const String postCreatedMessage = 'Post created successfully!';
  static const String spaceCreatedMessage = 'Space created successfully!';
  static const String inviteSentMessage = 'Invitation sent successfully!';
}
