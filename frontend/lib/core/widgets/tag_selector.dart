import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../theme/app_colors.dart';
import '../models/tag_model.dart';
import '../blocs/tag/tag_bloc.dart';
import '../blocs/tag/tag_event.dart';
import '../blocs/tag/tag_state.dart';
import '../di/dependency_injection.dart';
import '../../features/tags/presentation/widgets/tag_create_dialog.dart';

class TagSelector extends StatefulWidget {
  final String spaceId;
  final List<String> selectedTags;
  final Function(List<String>) onTagsChanged;
  final String? hintText;
  final bool allowCustomTags;
  final int maxTags;

  const TagSelector({
    super.key,
    required this.spaceId,
    required this.selectedTags,
    required this.onTagsChanged,
    this.hintText,
    this.allowCustomTags = true,
    this.maxTags = 10,
  });

  @override
  State<TagSelector> createState() => _TagSelectorState();
}

class _TagSelectorState extends State<TagSelector> {
  List<TagData> _availableTags = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _addExistingTag(String tagName) {
    if (widget.selectedTags.contains(tagName)) return;
    if (widget.selectedTags.length >= widget.maxTags) return;

    final updatedTags = [...widget.selectedTags, tagName];
    widget.onTagsChanged(updatedTags);
  }

  void _removeTag(String tagName) {
    final updatedTags =
        widget.selectedTags.where((tag) => tag != tagName).toList();
    widget.onTagsChanged(updatedTags);
  }

  void _showCreateTagDialog(BuildContext context) {
    final tagBloc = context.read<TagBloc>();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (dialogContext) => TagCreateDialog(
        spaceId: widget.spaceId,
        tagBloc: tagBloc,
        onTagCreated: () {
          // Refresh the tag list
          tagBloc.add(TagRefreshRequested(
            spaceId: widget.spaceId,
          ));
        },
      ),
    );
  }

  Color _getTagColor(String tagName) {
    final tag = _availableTags.firstWhere(
      (t) => t.name == tagName,
      orElse: () => TagData(
        id: '',
        spaceId: '',
        name: tagName,
        color: '#2196F3',
        usageCount: 0,
        createdByUserId: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    try {
      return Color(int.parse(tag.color.replaceFirst('#', '0xFF')));
    } catch (e) {
      return AppColors.primary;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => DependencyInjection.getIt<TagBloc>()
        ..add(TagLoadRequested(
          spaceId: widget.spaceId,
        )),
      child: BlocListener<TagBloc, TagState>(
        listener: (context, state) {
          if (state is TagLoaded) {
            setState(() {
              _availableTags = state.tags;
            });
          }
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Selected tags display
            if (widget.selectedTags.isNotEmpty) ...[
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: widget.selectedTags.map((tag) {
                  return Chip(
                    label: Text(
                      tag,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.white,
                      ),
                    ),
                    backgroundColor: _getTagColor(tag),
                    deleteIcon: const Icon(
                      Icons.close,
                      size: 16,
                      color: Colors.white,
                    ),
                    onDeleted: () => _removeTag(tag),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  );
                }).toList(),
              ),
              const SizedBox(height: 8),
            ],

            // Available tags section
            BlocBuilder<TagBloc, TagState>(
              builder: (context, state) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section header with create button
                    Row(
                      children: [
                        Text(
                          'Available Tags',
                          style:
                              Theme.of(context).textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                        ),
                        const Spacer(),
                        if (widget.allowCustomTags)
                          IconButton(
                            icon: const Icon(Icons.add_circle_outline),
                            onPressed: () => _showCreateTagDialog(context),
                            tooltip: 'Create new tag',
                            iconSize: 20,
                          ),
                      ],
                    ),

                    // Available tags list
                    if (_availableTags.isEmpty)
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.border),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          children: [
                            const Icon(
                              Icons.label_outline,
                              size: 32,
                              color: AppColors.textSecondary,
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'No tags available',
                              style: TextStyle(
                                color: AppColors.textSecondary,
                                fontSize: 14,
                              ),
                            ),
                            if (widget.allowCustomTags) ...[
                              const SizedBox(height: 4),
                              const Text(
                                'Create your first tag',
                                style: TextStyle(
                                  color: AppColors.textSecondary,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ],
                        ),
                      )
                    else
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: _availableTags.map((tag) {
                          final isSelected =
                              widget.selectedTags.contains(tag.name);
                          final tagColor = _getTagColor(tag.name);

                          return GestureDetector(
                            onTap: isSelected
                                ? () => _removeTag(tag.name)
                                : () => _addExistingTag(tag.name),
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? tagColor
                                    : tagColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    tag.name,
                                    style: TextStyle(
                                      color:
                                          isSelected ? Colors.white : tagColor,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  if (tag.usageCount > 0) ...[
                                    const SizedBox(width: 4),
                                    Text(
                                      '${tag.usageCount}',
                                      style: TextStyle(
                                        color: isSelected
                                            ? Colors.white
                                                .withValues(alpha: 0.8)
                                            : tagColor.withValues(alpha: 0.7),
                                        fontSize: 11,
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                  ],
                );
              },
            ),

            // Helper text
            if (widget.selectedTags.length >= widget.maxTags)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  'Maximum ${widget.maxTags} tags allowed',
                  style: const TextStyle(
                    color: AppColors.error,
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
