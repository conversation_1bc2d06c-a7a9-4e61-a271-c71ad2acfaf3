import 'dart:io';

import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../network/api_client.dart';
import '../network/dio_client.dart';
import '../storage/local_storage.dart';
import '../storage/secure_storage.dart';
import '../../features/auth/presentation/bloc/auth_bloc.dart';
import '../../features/auth/domain/usecases/login_usecase.dart';
import '../../features/auth/domain/usecases/register_usecase.dart';
import '../../features/auth/domain/usecases/logout_usecase.dart';
import '../../features/auth/domain/usecases/get_current_user_usecase.dart';
import '../../features/auth/domain/services/auth_service.dart';
import '../../features/auth/domain/services/profile_storage_service.dart';
import '../../features/profile/domain/usecases/get_user_stats_usecase.dart';
import '../../features/spaces/domain/usecases/get_spaces_usecase.dart';
import '../../features/spaces/domain/usecases/create_space_usecase.dart';
import '../../features/spaces/domain/usecases/update_space_usecase.dart';
import '../../features/spaces/domain/usecases/leave_space_usecase.dart';
import '../../features/spaces/domain/usecases/delete_space_usecase.dart';
import '../../features/spaces/domain/usecases/get_space_detail_usecase.dart';
import '../../features/spaces/domain/usecases/get_space_members_usecase.dart';
import '../../features/spaces/domain/usecases/update_member_role_usecase.dart';
import '../../features/spaces/domain/usecases/remove_member_usecase.dart';
import '../../features/spaces/domain/usecases/get_space_stats_usecase.dart';
import '../../features/spaces/domain/usecases/invite_management_usecase.dart';
import '../../features/spaces/domain/usecases/clear_space_data_usecase.dart';
import '../../features/spaces/domain/services/space_permissions_service.dart';
import '../../features/spaces/presentation/bloc/spaces_bloc.dart';
import '../../features/spaces/presentation/bloc/space_detail_bloc.dart';
import '../../features/home/<USER>/usecases/get_timeline_usecase.dart';
import '../../features/home/<USER>/bloc/timeline_bloc.dart';
import '../../features/chat/data/datasources/chat_api_client.dart';
import '../../features/chat/data/datasources/chat_websocket_service.dart';
import '../../features/chat/data/datasources/todo_api_service.dart';
import '../../features/chat/data/datasources/event_api_service.dart';

import '../services/tag_api_service.dart';
import '../blocs/tag/tag_bloc.dart';
import '../../features/chat/data/repositories/chat_repository_impl.dart';
import '../../features/chat/domain/repositories/chat_repository.dart';
import '../../features/chat/domain/usecases/get_messages_usecase.dart';
import '../../features/chat/domain/usecases/send_message_usecase.dart';
import '../../features/chat/domain/usecases/chat_connection_usecase.dart';
import '../../features/chat/domain/services/unread_message_service.dart';
import '../../features/chat/domain/services/location_service.dart';
import '../../features/chat/domain/services/poll_service.dart';
import '../../features/chat/domain/services/dice_service.dart';
import '../../features/chat/domain/services/lucky_draw_service.dart';
import '../../features/chat/presentation/bloc/chat_bloc.dart';
import '../../features/spaces/domain/services/online_members_service.dart';
import '../../features/balance/data/services/balance_service.dart';
import '../services/currency_service.dart';
import '../../features/offline_chat/domain/services/cross_platform_chat_service.dart';
import '../../features/offline_chat/domain/services/android_unified_service.dart';
import '../../features/offline_chat/domain/services/ios_unified_service.dart';
import '../../features/offline_chat/data/models/offline_chat_models.dart';
import '../platform/platform_config.dart';
import '../platform/permission_manager.dart';
import '../platform/camera_service.dart';
import '../../features/togo/data/services/togo_service.dart';
import '../../features/togo/presentation/bloc/togo_bloc.dart';

class DependencyInjection {
  static final GetIt getIt = GetIt.instance;

  static Future<void> init() async {
    // External dependencies
    final sharedPreferences = await SharedPreferences.getInstance();
    const secureStorage = FlutterSecureStorage();

    getIt.registerLazySingleton<SharedPreferences>(() => sharedPreferences);
    getIt.registerLazySingleton<FlutterSecureStorage>(() => secureStorage);

    // Storage
    getIt.registerLazySingleton<LocalStorage>(
      () => LocalStorageImpl(getIt<SharedPreferences>()),
    );
    getIt.registerLazySingleton<SecureStorage>(
      () => SecureStorageImpl(getIt<FlutterSecureStorage>()),
    );

    // Network
    getIt.registerLazySingleton<Dio>(() => DioClient.createDio());
    getIt.registerLazySingleton<ApiClient>(
      () => ApiClient(getIt<Dio>()),
    );

    // Platform Services
    getIt.registerLazySingleton<PlatformConfig>(() => PlatformConfig());
    getIt.registerLazySingleton<PermissionManager>(() => PermissionManager());
    getIt.registerLazySingleton<CameraService>(() => CameraService());

    // Core Services
    getIt.registerLazySingleton<CurrencyService>(() => CurrencyService());

    // Auth Use Cases
    getIt.registerLazySingleton<LoginUseCase>(
      () => LoginUseCase(
        apiClient: getIt<ApiClient>(),
        secureStorage: getIt<SecureStorage>(),
      ),
    );

    getIt.registerLazySingleton<RegisterUseCase>(
      () => RegisterUseCase(
        apiClient: getIt<ApiClient>(),
        secureStorage: getIt<SecureStorage>(),
      ),
    );

    getIt.registerLazySingleton<LogoutUseCase>(
      () => LogoutUseCase(
        apiClient: getIt<ApiClient>(),
        secureStorage: getIt<SecureStorage>(),
      ),
    );

    getIt.registerLazySingleton<GetCurrentUserUseCase>(
      () => GetCurrentUserUseCase(
        apiClient: getIt<ApiClient>(),
        secureStorage: getIt<SecureStorage>(),
      ),
    );

    // Auth Service
    getIt.registerLazySingleton<AuthService>(
      () => AuthService(
        getCurrentUserUseCase: getIt<GetCurrentUserUseCase>(),
        secureStorage: getIt<SecureStorage>(),
      ),
    );

    // Profile Storage Service
    getIt.registerLazySingleton<ProfileStorageService>(
      () => ProfileStorageService(
        secureStorage: getIt<SecureStorage>(),
      ),
    );

    // Profile Use Cases
    getIt.registerLazySingleton<GetUserStatsUseCase>(
      () => GetUserStatsUseCase(getIt<ApiClient>()),
    );

    // Spaces Use Cases
    getIt.registerLazySingleton<GetSpacesUseCase>(
      () => GetSpacesUseCase(
        apiClient: getIt<ApiClient>(),
      ),
    );

    getIt.registerLazySingleton<CreateSpaceUseCase>(
      () => CreateSpaceUseCase(
        apiClient: getIt<ApiClient>(),
      ),
    );

    getIt.registerLazySingleton<UpdateSpaceUseCase>(
      () => UpdateSpaceUseCase(
        apiClient: getIt<ApiClient>(),
      ),
    );

    getIt.registerLazySingleton<LeaveSpaceUseCase>(
      () => LeaveSpaceUseCase(
        apiClient: getIt<ApiClient>(),
      ),
    );

    getIt.registerLazySingleton<DeleteSpaceUseCase>(
      () => DeleteSpaceUseCase(
        apiClient: getIt<ApiClient>(),
      ),
    );

    getIt.registerLazySingleton<GetSpaceDetailUseCase>(
      () => GetSpaceDetailUseCase(
        apiClient: getIt<ApiClient>(),
      ),
    );

    getIt.registerLazySingleton<GetSpaceMembersUseCase>(
      () => GetSpaceMembersUseCase(getIt<ApiClient>()),
    );

    getIt.registerLazySingleton<UpdateMemberRoleUseCase>(
      () => UpdateMemberRoleUseCase(getIt<ApiClient>()),
    );

    getIt.registerLazySingleton<RemoveMemberUseCase>(
      () => RemoveMemberUseCase(getIt<ApiClient>()),
    );

    getIt.registerLazySingleton<GetSpaceStatsUseCase>(
      () => GetSpaceStatsUseCase(getIt<ApiClient>()),
    );

    getIt.registerLazySingleton<InviteManagementUseCase>(
      () => InviteManagementUseCase(getIt<ApiClient>()),
    );

    getIt.registerLazySingleton<ClearSpaceDataUseCase>(
      () => ClearSpaceDataUseCase(
        apiClient: getIt<ApiClient>(),
      ),
    );

    // Space Permissions Service
    getIt.registerLazySingleton<SpacePermissionsService>(
      () => SpacePermissionsService(
        authService: getIt<AuthService>(),
      ),
    );

    // Timeline Use Case
    getIt.registerLazySingleton<GetTimelineUseCase>(
      () => GetTimelineUseCase(),
    );

    // Auth Bloc
    getIt.registerFactory<AuthBloc>(
      () => AuthBloc(
        loginUseCase: getIt<LoginUseCase>(),
        registerUseCase: getIt<RegisterUseCase>(),
        logoutUseCase: getIt<LogoutUseCase>(),
        getCurrentUserUseCase: getIt<GetCurrentUserUseCase>(),
      ),
    );

    // Spaces Bloc
    getIt.registerLazySingleton<SpacesBloc>(
      () => SpacesBloc(
        getSpacesUseCase: getIt<GetSpacesUseCase>(),
        createSpaceUseCase: getIt<CreateSpaceUseCase>(),
        updateSpaceUseCase: getIt<UpdateSpaceUseCase>(),
        leaveSpaceUseCase: getIt<LeaveSpaceUseCase>(),
        deleteSpaceUseCase: getIt<DeleteSpaceUseCase>(),
      ),
    );

    // Space Detail Bloc
    getIt.registerFactory<SpaceDetailBloc>(
      () => SpaceDetailBloc(
        getSpaceDetailUseCase: getIt<GetSpaceDetailUseCase>(),
        getSpaceMembersUseCase: getIt<GetSpaceMembersUseCase>(),
      ),
    );

    // Timeline Bloc - 使用单例确保状态一致性
    getIt.registerLazySingleton<TimelineBloc>(
      () => TimelineBloc(
        getTimelineUseCase: getIt<GetTimelineUseCase>(),
      ),
    );

    // Chat Services
    getIt.registerLazySingleton<ChatApiClient>(
      () => ChatApiClient(getIt<Dio>()),
    );

    getIt.registerLazySingleton<TodoApiService>(
      () => TodoApiService(getIt<Dio>()),
    );

    getIt.registerLazySingleton<TagApiService>(
      () => TagApiService(getIt<Dio>()),
    );

    getIt.registerLazySingleton<EventApiService>(
      () => EventApiService(getIt<AuthService>()),
    );

    getIt.registerLazySingleton<ChatWebSocketService>(
      () => ChatWebSocketService(),
    );

    getIt.registerLazySingleton<ChatRepository>(
      () => ChatRepositoryImpl(
        getIt<ChatApiClient>(),
        getIt<ChatWebSocketService>(),
      ),
    );

    // Chat Use Cases
    getIt.registerLazySingleton<GetMessagesUseCase>(
      () => GetMessagesUseCase(getIt<ChatRepository>()),
    );

    getIt.registerLazySingleton<SendMessageUseCase>(
      () => SendMessageUseCase(getIt<ChatRepository>()),
    );

    getIt.registerLazySingleton<ChatConnectionUseCase>(
      () => ChatConnectionUseCase(getIt<ChatRepository>()),
    );

    // Chat Bloc
    getIt.registerLazySingleton<ChatBloc>(
      () => ChatBloc(
        repository: getIt<ChatRepository>(),
        getMessagesUseCase: getIt<GetMessagesUseCase>(),
        connectionUseCase: getIt<ChatConnectionUseCase>(),
      ),
    );

    // Unread Message Service
    getIt.registerLazySingleton<UnreadMessageService>(
      () => UnreadMessageService(),
    );

    // Online Members Service
    getIt.registerLazySingleton<OnlineMembersService>(
      () => OnlineMembersService(),
    );

    // Location Service
    getIt.registerLazySingleton<LocationService>(
      () => LocationService(),
    );

    // Poll Service
    getIt.registerLazySingleton<PollService>(
      () => PollService(),
    );

    // Dice Service
    getIt.registerLazySingleton<DiceService>(
      () => DiceService(),
    );

    // Lucky Draw Service
    getIt.registerLazySingleton<LuckyDrawService>(
      () => LuckyDrawService(),
    );

    // Balance Service
    getIt.registerLazySingleton<BalanceService>(
      () => BalanceService(
          getIt<ApiClient>(), getIt<Dio>(), getIt<ChatRepository>()),
    );

    // ToGo Service
    getIt.registerLazySingleton<ToGoService>(
      () => ToGoService(getIt<SecureStorage>()),
    );

    // ToGo Bloc
    getIt.registerFactory<ToGoBloc>(
      () => ToGoBloc(getIt<ToGoService>()),
    );

    // Tag Bloc
    getIt.registerFactory<TagBloc>(
      () => TagBloc(getIt<TagApiService>()),
    );

    // Cross Platform Chat Service
    getIt.registerLazySingleton<CrossPlatformChatService>(
      () {
        // 根據平台選擇合適的實現
        if (Platform.isAndroid) {
          return AndroidUnifiedService();
        } else if (Platform.isIOS) {
          return IOSUnifiedService();
        } else {
          return _MockChatService(); // Web 和其他平台使用 Mock
        }
      },
    );

    // Initialize Platform Services
    await getIt<PlatformConfig>().initialize();
    await getIt<CameraService>().initialize();

    // Initialize AuthService to load current user
    await getIt<AuthService>().initialize();
  }
}

/// 臨時的 Mock 實現，用於測試
class _MockChatService implements CrossPlatformChatService {
  CrossPlatformChatStatus _status = CrossPlatformChatStatus.ready;
  OfflineChatSession? _currentSession;
  final List<OfflineChatMember> _connectedMembers = [];
  final List<DiscoveredChatRoom> _discoveredRooms = [];

  @override
  CrossPlatformChatStatus get status => _status;

  @override
  OfflineChatSession? get currentSession => _currentSession;

  @override
  List<OfflineChatMember> get connectedMembers =>
      List.unmodifiable(_connectedMembers);

  @override
  List<DiscoveredChatRoom> get discoveredRooms =>
      List.unmodifiable(_discoveredRooms);

  @override
  Stream<CrossPlatformChatStatus> get statusStream => Stream.value(_status);

  @override
  Stream<List<DiscoveredChatRoom>> get discoveredRoomsStream =>
      Stream.value(_discoveredRooms);

  @override
  Stream<OfflineChatMessage> get messageStream => const Stream.empty();

  @override
  Stream<List<OfflineChatMember>> get membersStream =>
      Stream.value(_connectedMembers);

  @override
  Future<bool> initialize() async {
    // print('🔧 Mock Service: Initialization successful');
    return true;
  }

  @override
  Future<bool> createChatRoom(OfflineChatSession session) async {
    // print('🔧 Mock Service: Creating chat room - ${session.roomName}');
    _currentSession = session;
    _status = CrossPlatformChatStatus.hosting;

    // Simulate successful creation
    await Future.delayed(const Duration(milliseconds: 500));
    // print('🔧 Mock Service: Chat room created successfully');
    return true;
  }

  @override
  Future<bool> scanForChatRooms() async {
    // print('🔧 Mock Service: Starting chat room scan');
    _status = CrossPlatformChatStatus.scanning;

    // Simulate scanning process
    await Future.delayed(const Duration(seconds: 2));

    // Add some mock chat rooms
    _discoveredRooms.clear();
    _discoveredRooms.addAll([
      DiscoveredChatRoom(
        sessionId: 'mock-room-1',
        roomName: 'Test Chat Room 1',
        creatorName: 'Mock Host',
        memberCount: 2,
        maxMembers: 8,
        deviceId: 'mock-device-1',
        signalStrength: -45,
        lastSeen: DateTime.now(),
      ),
      DiscoveredChatRoom(
        sessionId: 'mock-room-2',
        roomName: 'Private Chat Room',
        creatorName: 'Another Host',
        memberCount: 1,
        maxMembers: 8,
        deviceId: 'mock-device-2',
        signalStrength: -60,
        lastSeen: DateTime.now().subtract(const Duration(minutes: 1)),
      ),
    ]);

    _status = CrossPlatformChatStatus.ready;
    // print(
    //     '🔧 Mock Service: Scan completed, found ${_discoveredRooms.length} chat rooms');
    return true;
  }

  @override
  Future<bool> joinChatRoom(
    DiscoveredChatRoom room,
    String userNickname, [
    String? roomCode,
  ]) async {
    // print('🔧 Mock Service: Joining chat room - ${room.roomName}');
    _status = CrossPlatformChatStatus.connecting;

    // Simulate connection process
    await Future.delayed(const Duration(seconds: 1));

    // Simulate successful connection
    _status = CrossPlatformChatStatus.connected;
    _currentSession = OfflineChatSession(
      sessionId: room.sessionId,
      roomName: room.roomName,
      roomCode: '123456', // Mock room code
      creatorId: room.deviceId,
      creatorName: room.creatorName,
      createdAt: DateTime.now(),
    );

    // Add mock members
    _connectedMembers.clear();
    _connectedMembers.addAll([
      OfflineChatMember(
        deviceId: 'host-device',
        deviceName: 'Host Device',
        userName: room.creatorName,
        joinedAt: DateTime.now().subtract(const Duration(minutes: 5)),
      ),
      OfflineChatMember(
        deviceId: 'user-device',
        deviceName: 'User Device',
        userName: userNickname,
        joinedAt: DateTime.now(),
      ),
    ]);

    // print('🔧 Mock Service: Successfully joined chat room');
    return true;
  }

  @override
  Future<bool> sendMessage(OfflineChatMessage message) async {
    // print('🔧 Mock Service: Sending message - ${message.content}');
    // Simulate successful sending
    await Future.delayed(const Duration(milliseconds: 200));
    return true;
  }

  @override
  Future<void> disconnect() async {
    // print('🔧 Mock Service: Disconnecting');
    _status = CrossPlatformChatStatus.disconnected;
    _currentSession = null;
    _connectedMembers.clear();
    await Future.delayed(const Duration(milliseconds: 300));
    _status = CrossPlatformChatStatus.ready;
  }

  @override
  Future<void> dispose() async {
    // print('🔧 Mock Service: Releasing resources');
    await disconnect();
  }
}
