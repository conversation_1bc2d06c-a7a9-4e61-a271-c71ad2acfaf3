import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// 平台配置管理
class PlatformConfig {
  static final PlatformConfig _instance = PlatformConfig._internal();
  factory PlatformConfig() => _instance;
  PlatformConfig._internal();

  DeviceInfoPlugin? _deviceInfo;
  PackageInfo? _packageInfo;

  // 平台信息
  bool get isAndroid => !kIsWeb && Platform.isAndroid;
  bool get isIOS => !kIsWeb && Platform.isIOS;
  bool get isMobile => isAndroid || isIOS;
  bool get isWeb => kIsWeb;
  bool get isDesktop =>
      !kIsWeb && (Platform.isWindows || Platform.isMacOS || Platform.isLinux);

  /// 初始化平台配置
  Future<void> initialize() async {
    _deviceInfo = DeviceInfoPlugin();
    _packageInfo = await PackageInfo.fromPlatform();
  }

  /// 获取设备信息
  Future<Map<String, dynamic>> getDeviceInfo() async {
    if (_deviceInfo == null) await initialize();

    final Map<String, dynamic> deviceData = {};

    try {
      if (isAndroid) {
        final androidInfo = await _deviceInfo!.androidInfo;
        deviceData.addAll({
          'platform': 'Android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'version': androidInfo.version.release,
          'sdkInt': androidInfo.version.sdkInt,
          'brand': androidInfo.brand,
          'device': androidInfo.device,
          'id': androidInfo.id,
        });
      } else if (isIOS) {
        final iosInfo = await _deviceInfo!.iosInfo;
        deviceData.addAll({
          'platform': 'iOS',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'systemName': iosInfo.systemName,
          'systemVersion': iosInfo.systemVersion,
          'identifierForVendor': iosInfo.identifierForVendor,
          'isPhysicalDevice': iosInfo.isPhysicalDevice,
        });
      } else if (isWeb) {
        final webInfo = await _deviceInfo!.webBrowserInfo;
        deviceData.addAll({
          'platform': 'Web',
          'browserName': webInfo.browserName.name,
          'userAgent': webInfo.userAgent,
          'language': webInfo.language,
          'browserPlatform': webInfo.platform,
        });
      }
    } catch (e) {
      deviceData['error'] = e.toString();
    }

    return deviceData;
  }

  /// 获取应用信息
  Map<String, dynamic> getAppInfo() {
    if (_packageInfo == null) return {};

    return {
      'appName': _packageInfo!.appName,
      'packageName': _packageInfo!.packageName,
      'version': _packageInfo!.version,
      'buildNumber': _packageInfo!.buildNumber,
    };
  }

  /// 获取平台特定的API基础URL
  String getApiBaseUrl() {
    if (isWeb) {
      return 'http://*************:3000/api';
    } else if (isAndroid) {
      // Android真实设备使用实际IP地址进行无线调试
      return 'http://*************:3000/api';
    } else if (isIOS) {
      // iOS设备使用实际IP地址
      return 'http://*************:3000/api';
    }
    return 'http://*************:3000/api';
  }

  /// 获取平台特定的图片质量设置
  int getImageQuality() {
    if (isMobile) {
      return 80; // Lower quality for mobile to save bandwidth
    } else {
      return 95; // Higher quality for web/desktop
    }
  }

  /// 检查是否支持生物识别
  bool get supportsBiometrics => isMobile;

  /// 检查是否支持推送通知
  bool get supportsPushNotifications => isMobile;

  /// 检查是否支持相机
  bool get supportsCamera => isMobile;

  /// 检查是否支持文件选择
  bool get supportsFilePicker => true;

  /// 获取平台特定的字体大小缩放
  double getFontScale() {
    if (isMobile) {
      return 1.0; // Standard scale for mobile
    } else {
      return 1.1; // Slightly larger for web/desktop
    }
  }

  /// 获取平台特定的动画持续时间
  Duration getAnimationDuration() {
    if (isMobile) {
      return const Duration(milliseconds: 250); // Faster for mobile
    } else {
      return const Duration(milliseconds: 300); // Standard for web/desktop
    }
  }

  /// 获取平台特定的网络超时设置
  Duration getNetworkTimeout() {
    if (isMobile) {
      return const Duration(seconds: 15); // Shorter timeout for mobile
    } else {
      return const Duration(seconds: 30); // Longer timeout for web/desktop
    }
  }

  /// 检查是否为调试模式
  bool get isDebugMode => kDebugMode;

  /// 检查是否为发布模式
  bool get isReleaseMode => kReleaseMode;

  /// 检查是否为性能分析模式
  bool get isProfileMode => kProfileMode;
}
