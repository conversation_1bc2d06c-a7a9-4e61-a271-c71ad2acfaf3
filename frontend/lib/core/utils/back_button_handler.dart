import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

/// 通用的返回键处理工具类
class BackButtonHandler {
  /// 显示退出确认对话框
  static Future<bool?> showExitConfirmDialog(BuildContext context) {
    return showModalBottomSheet<bool>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.exit_to_app, color: Colors.orange),
                  const SizedBox(width: 8),
                  const Text(
                    'Exit Snapthought',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Text(
                'Are you sure you want to exit the app?',
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ),
            // Actions
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  top: BorderSide(color: Colors.grey[200]!),
                ),
              ),
              child: SafeArea(
                child: Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('Cancel'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Exit'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 处理主页面的返回键逻辑
  static Future<void> handleMainPageBack(BuildContext context) async {
    // 检查是否可以返回上一页
    if (GoRouter.of(context).canPop()) {
      GoRouter.of(context).pop();
    } else {
      // 无法返回，显示退出确认对话框
      final shouldExit = await showExitConfirmDialog(context);
      if (shouldExit == true && context.mounted) {
        // 用户确认退出，使用SystemNavigator退出应用而不是Navigator.pop()
        SystemNavigator.pop();
      }
    }
  }

  /// 处理子页面的返回键逻辑
  static Future<void> handleSubPageBack(BuildContext context,
      {String? pageName}) async {
    // 子页面总是尝试返回上一页
    if (GoRouter.of(context).canPop()) {
      GoRouter.of(context).pop();
    } else {
      // 如果无法返回，根据页面类型决定跳转目标
      if (pageName != null && _isSpaceRelatedPage(pageName)) {
        // Space相关页面返回到Spaces标签页
        GoRouter.of(context).go('/home?tab=2');
      } else {
        // 其他页面跳转到主页默认标签
        GoRouter.of(context).go('/home');
      }
    }
  }

  /// 检查是否为Space相关页面
  static bool _isSpaceRelatedPage(String pageName) {
    return pageName.toLowerCase().contains('space') ||
        pageName.toLowerCase().contains('post') ||
        pageName.toLowerCase().contains('invite');
  }

  /// 创建一个包装了返回键处理的组件
  /// 针对iOS返回手势进行特殊优化
  static Widget wrapWithBackHandler({
    required Widget child,
    required BuildContext context,
    bool isMainPage = false,
    String? pageName,
  }) {
    // 对于iOS，我们需要特殊处理以确保手势正常工作
    if (Theme.of(context).platform == TargetPlatform.iOS) {
      return _buildIOSBackHandler(
        child: child,
        context: context,
        isMainPage: isMainPage,
        pageName: pageName,
      );
    }

    // Android和其他平台使用PopScope
    return PopScope(
      canPop: !isMainPage && GoRouter.of(context).canPop(),
      onPopInvokedWithResult: (bool didPop, dynamic result) async {
        if (didPop) return;

        if (isMainPage) {
          await handleMainPageBack(context);
        } else {
          await handleSubPageBack(context, pageName: pageName);
        }
      },
      child: child,
    );
  }

  /// iOS专用的返回处理器
  /// 避免使用PopScope，让iOS手势自然工作
  static Widget _buildIOSBackHandler({
    required Widget child,
    required BuildContext context,
    bool isMainPage = false,
    String? pageName,
  }) {
    if (isMainPage) {
      // 主页面仍然需要拦截返回操作
      return PopScope(
        canPop: false,
        onPopInvokedWithResult: (bool didPop, dynamic result) async {
          if (didPop) return;
          await handleMainPageBack(context);
        },
        child: child,
      );
    }

    // 子页面完全不使用PopScope，让iOS手势自然工作
    // 但是我们需要监听路由变化来处理特殊情况
    return _IOSGestureWrapper(
      pageName: pageName,
      child: child,
    );
  }
}

/// iOS手势包装器，不拦截手势但提供必要的路由处理
class _IOSGestureWrapper extends StatelessWidget {
  const _IOSGestureWrapper({
    required this.child,
    this.pageName,
  });

  final Widget child;
  final String? pageName;

  @override
  Widget build(BuildContext context) {
    // 完全不使用PopScope，让iOS手势自然工作
    // 如果需要特殊的返回逻辑，可以在这里添加
    return child;
  }
}
