import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'app_error.dart';
import '../theme/app_colors.dart';
import '../utils/responsive_utils.dart';

/// Error handling service
/// Unified handling of various errors in the application, providing user-friendly feedback
class ErrorHandler {
  static final Logger _logger = Logger();
  static final Map<String, int> _retryAttempts = {};

  /// Handle error and return AppError
  static AppError handleError(dynamic error) {
    AppError appError;

    if (error is AppError) {
      appError = error;
    } else if (error is DioException) {
      appError = AppError.fromDioException(error);
    } else if (error is Exception) {
      appError = AppError.fromException(error);
    } else {
      // For unknown errors, do not directly expose raw error information
      appError = AppError(
        type: AppErrorType.unknown,
        message: 'Unknown error occurred',
        userMessage: 'An unexpected error occurred. Please try again.',
        severity: AppErrorSeverity.medium,
      );
    }

    // Log error
    _logError(appError);

    return appError;
  }

  /// Show error message to user
  static void showError(BuildContext context, dynamic error) {
    final appError = handleError(error);

    switch (appError.severity) {
      case AppErrorSeverity.low:
        _showSnackBar(context, appError);
        break;
      case AppErrorSeverity.medium:
        _showSnackBar(context, appError);
        break;
      case AppErrorSeverity.high:
        _showErrorDialog(context, appError);
        break;
      case AppErrorSeverity.critical:
        _showCriticalErrorDialog(context, appError);
        break;
    }
  }

  /// 显示带重试功能的错误
  static void showErrorWithRetry(
    BuildContext context,
    dynamic error,
    VoidCallback onRetry,
  ) {
    final appError = handleError(error);

    if (appError.canRetry) {
      _showRetryDialog(context, appError, onRetry);
    } else {
      showError(context, error);
    }
  }

  /// 检查是否应该自动重试
  static bool shouldAutoRetry(AppError error, String operationId) {
    if (!error.canRetry) return false;

    final attempts = _retryAttempts[operationId] ?? 0;
    return attempts < error.maxRetryAttempts;
  }

  /// 增加重试计数
  static void incrementRetryCount(String operationId) {
    _retryAttempts[operationId] = (_retryAttempts[operationId] ?? 0) + 1;
  }

  /// 重置重试计数
  static void resetRetryCount(String operationId) {
    _retryAttempts.remove(operationId);
  }

  /// 获取重试延迟
  static Duration getRetryDelay(AppError error, String operationId) {
    final attempts = _retryAttempts[operationId] ?? 0;
    final baseDelay = error.retryDelayDuration;

    // 指数退避策略
    return Duration(
      milliseconds: (baseDelay.inMilliseconds * (1 << attempts)).clamp(
        baseDelay.inMilliseconds,
        30000, // 最大30秒
      ),
    );
  }

  /// 记录错误日志
  static void _logError(AppError error) {
    // 使用用戶友好的消息進行日誌記錄，避免暴露敏感信息
    final logMessage = error.userMessage ?? error.message;

    switch (error.severity) {
      case AppErrorSeverity.low:
        _logger.i('Low severity error: $logMessage');
        break;
      case AppErrorSeverity.medium:
        _logger.w('Medium severity error: $logMessage');
        break;
      case AppErrorSeverity.high:
        _logger.e('High severity error: $logMessage');
        break;
      case AppErrorSeverity.critical:
        _logger.e('Critical error: $logMessage');
        // 對於關鍵錯誤，只記錄錯誤類型和狀態碼，不記錄可能包含敏感信息的堆棧跟踪
        if (error.statusCode != null) {
          _logger.e('Status code: ${error.statusCode}');
        }
        _logger.e('Error type: ${error.type}');
        break;
    }
  }

  /// 显示SnackBar错误消息
  static void _showSnackBar(BuildContext context, AppError error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              _getErrorIcon(error.type),
              color: Colors.white,
              size: ResponsiveUtils.getResponsiveValue(
                context,
                mobile: 20,
                tablet: 22,
                desktop: 24,
              ),
            ),
            SizedBox(
                width: ResponsiveUtils.getResponsiveSpacing(context,
                    baseSpacing: 8)),
            Expanded(
              child: Text(
                error.displayMessage,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: ResponsiveUtils.getResponsiveFontSize(context, 14),
                ),
              ),
            ),
          ],
        ),
        backgroundColor: _getErrorColor(error.severity),
        duration: _getSnackBarDuration(error.severity),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            ResponsiveUtils.getResponsiveCardRadius(context) * 0.5,
          ),
        ),
        action: error.canRetry
            ? SnackBarAction(
                label: 'Retry',
                textColor: Colors.white,
                onPressed: () {
                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                  // 这里需要回调来处理重试
                },
              )
            : SnackBarAction(
                label: 'Dismiss',
                textColor: Colors.white,
                onPressed: () {
                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                },
              ),
      ),
    );
  }

  /// 显示错误对话框
  static void _showErrorDialog(BuildContext context, AppError error) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      enableDrag: false,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  Icon(
                    _getErrorIcon(error.type),
                    color: _getErrorColor(error.severity),
                    size: ResponsiveUtils.getResponsiveValue(
                      context,
                      mobile: 24,
                      tablet: 28,
                      desktop: 32,
                    ),
                  ),
                  SizedBox(
                      width: ResponsiveUtils.getResponsiveSpacing(context)),
                  Expanded(
                    child: Text(
                      'Error',
                      style: TextStyle(
                        fontSize:
                            ResponsiveUtils.getResponsiveFontSize(context, 18),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  Text(
                    error.displayMessage,
                    style: TextStyle(
                      fontSize:
                          ResponsiveUtils.getResponsiveFontSize(context, 14),
                    ),
                  ),
                  const SizedBox(height: 24),
                  // Actions
                  Row(
                    children: [
                      if (error.canRetry) ...[
                        Expanded(
                          child: TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                              // 这里需要回调来处理重试
                            },
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(color: Colors.grey[300]!),
                              ),
                            ),
                            child: const Text('Retry'),
                          ),
                        ),
                        const SizedBox(width: 12),
                      ],
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('OK'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示严重错误对话框
  static void _showCriticalErrorDialog(BuildContext context, AppError error) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      enableDrag: false,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  Icon(
                    Icons.error,
                    color: AppColors.error,
                    size: ResponsiveUtils.getResponsiveValue(
                      context,
                      mobile: 28,
                      tablet: 32,
                      desktop: 36,
                    ),
                  ),
                  SizedBox(
                      width: ResponsiveUtils.getResponsiveSpacing(context)),
                  Expanded(
                    child: Text(
                      'Critical Error',
                      style: TextStyle(
                        fontSize:
                            ResponsiveUtils.getResponsiveFontSize(context, 20),
                        fontWeight: FontWeight.bold,
                        color: AppColors.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    error.displayMessage,
                    style: TextStyle(
                      fontSize:
                          ResponsiveUtils.getResponsiveFontSize(context, 14),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Please restart the application or contact support if the problem persists.',
                    style: TextStyle(
                      fontSize:
                          ResponsiveUtils.getResponsiveFontSize(context, 12),
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 24),
                  // Actions
                  Row(
                    children: [
                      if (error.canRetry) ...[
                        Expanded(
                          child: TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                              // 这里需要回调来处理重试
                            },
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(color: Colors.grey[300]!),
                              ),
                            ),
                            child: const Text('Retry'),
                          ),
                        ),
                        const SizedBox(width: 12),
                      ],
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.error,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('OK'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示重试对话框
  static void _showRetryDialog(
    BuildContext context,
    AppError error,
    VoidCallback onRetry,
  ) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  Icon(
                    _getErrorIcon(error.type),
                    color: _getErrorColor(error.severity),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Operation Failed',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  Text(
                    error.displayMessage,
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  // Actions
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            onRetry();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Retry'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取错误图标
  static IconData _getErrorIcon(AppErrorType type) {
    switch (type) {
      case AppErrorType.networkError:
      case AppErrorType.connectionError:
        return Icons.wifi_off;
      case AppErrorType.connectionTimeout:
      case AppErrorType.receiveTimeout:
      case AppErrorType.sendTimeout:
        return Icons.access_time;
      case AppErrorType.unauthorized:
      case AppErrorType.authenticationError:
        return Icons.lock;
      case AppErrorType.forbidden:
      case AppErrorType.authorizationError:
        return Icons.block;
      case AppErrorType.notFound:
        return Icons.search_off;
      case AppErrorType.validationError:
        return Icons.warning;
      default:
        return Icons.error_outline;
    }
  }

  /// 获取错误颜色
  static Color _getErrorColor(AppErrorSeverity severity) {
    switch (severity) {
      case AppErrorSeverity.low:
        return Colors.orange;
      case AppErrorSeverity.medium:
        return AppColors.error;
      case AppErrorSeverity.high:
        return Colors.red.shade700;
      case AppErrorSeverity.critical:
        return Colors.red.shade900;
    }
  }

  /// 获取SnackBar显示时长
  static Duration _getSnackBarDuration(AppErrorSeverity severity) {
    switch (severity) {
      case AppErrorSeverity.low:
        return const Duration(seconds: 3);
      case AppErrorSeverity.medium:
        return const Duration(seconds: 4);
      case AppErrorSeverity.high:
        return const Duration(seconds: 6);
      case AppErrorSeverity.critical:
        return const Duration(seconds: 8);
    }
  }
}

/// 重试机制组件
class RetryMechanism {
  static Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    String? operationId,
    int maxAttempts = 3,
    Duration baseDelay = const Duration(seconds: 2),
    bool useExponentialBackoff = true,
    bool Function(dynamic error)? shouldRetry,
  }) async {
    final id = operationId ?? DateTime.now().millisecondsSinceEpoch.toString();
    int attempts = 0;

    while (attempts < maxAttempts) {
      try {
        final result = await operation();
        ErrorHandler.resetRetryCount(id);
        return result;
      } catch (error) {
        attempts++;
        ErrorHandler.incrementRetryCount(id);

        final appError = ErrorHandler.handleError(error);

        // 检查是否应该重试
        final shouldRetryError = shouldRetry?.call(error) ?? appError.canRetry;

        if (attempts >= maxAttempts || !shouldRetryError) {
          ErrorHandler.resetRetryCount(id);
          rethrow;
        }

        // 计算延迟时间
        Duration delay = baseDelay;
        if (useExponentialBackoff) {
          delay = Duration(
            milliseconds:
                (baseDelay.inMilliseconds * (1 << (attempts - 1))).clamp(
              baseDelay.inMilliseconds,
              30000, // 最大30秒
            ),
          );
        }

        await Future.delayed(delay);
      }
    }

    throw Exception('Max retry attempts reached');
  }
}
