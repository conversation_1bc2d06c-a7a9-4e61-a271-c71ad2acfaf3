# MeLo - Project Blueprint & Technical Specifications

## 1. Project Overview

### Core Concept
A cross-platform mobile app designed as a collaborative memory sharing platform. It allows individuals and small, intimate groups (couples, families, close friends) to capture and organize moments with rich timeline views, calendar organization, and real-time chat functionality.

### Unique Value Proposition
Combines the best of social media, messaging apps, and personal journaling into a unified platform that provides persistent, organized, and private spaces for shared memories with advanced timeline visualization and collaborative features.

## 2. Current Feature Implementation (V3.0)

### User Accounts & Authentication
- ✅ Email/password registration and login system
- ✅ JWT-based secure session management
- ✅ User profile management with avatars
- ✅ Automatic personal space creation on registration

### Advanced Spaces System
- ✅ **Personal Spaces**: Auto-created private spaces for individual users
- ✅ **Shared Spaces**: Collaborative spaces with multiple members
- ✅ **Space Categories**: 10 predefined categories (General, Family, Friends, Couple, Team, Work, Hobby, Travel, Study, Project)
- ✅ **Custom Colors**: User-defined color themes for each space
- ✅ **Multi-Currency Support**: Each space can set its own default currency (90+ supported)
- ✅ **Simplified Invite System**: Permanent invite codes for easy sharing
- ✅ **Space Management**: Full CRUD operations with member management

### Multi-Modal Content System
- ✅ **Unified Post System**: Single post type supporting multiple content formats
- ✅ **Rich Media Support**: Text, images, videos, and file attachments
- ✅ **Tag System**: Flexible tagging for content organization
- ✅ **Content Types**: Support for various post formats and media types

### Advanced Timeline & Calendar Views
- ✅ **Responsive Timeline**: Desktop and mobile-optimized timeline with infinite scroll
- ✅ **24-Hour Time Axis**: Posts organized by hour with visual time indicators
- ✅ **Enhanced Calendar**: Interactive calendar with post count indicators
- ✅ **Date Navigation**: Slot-machine style date picker with smooth animations
- ✅ **Cross-Space Timeline**: Unified view of posts from all user spaces

### Real-Time Communication & Interactive Features
- ✅ **WhatsApp-Style Chat**: Real-time messaging with WebSocket support
- ✅ **Message Bubbles**: Sender/receiver distinction with user avatars
- ✅ **Rich Attachments**: Camera, gallery, documents, and location sharing
- ✅ **Interactive Elements**: Polls, dice rolling, lucky draws, and split bills
- ✅ **Unread Badge System**: Smart notification badges with real-time updates
- ✅ **Space-Themed UI**: Chat interface adapts to space color themes

### Complete Balance & Expense Management
- ✅ **Multi-Currency Splits**: Create expense splits with 90+ currency support
- ✅ **Smart Debt Calculation**: Automatic balance calculation and consolidation
- ✅ **Settlement Tracking**: Record payments and update balances in real-time
- ✅ **Exchange Rate Integration**: Real-time currency conversion with rate caching
- ✅ **Balance Overview**: Clear visualization of debts and settlements
- ✅ **Audit Trail**: Complete history with original amounts and exchange rates

### Multi-Tab Space Interface
- ✅ **Chat Tab**: Real-time messaging with interactive features (primary tab)
- ✅ **Posts Tab**: Traditional timeline view (legacy - being phased out)
- ✅ **Event Tab**: Interactive activities tracking (polls, dice, lucky draws, location)
- ✅ **Balance Tab**: Complete expense sharing and debt management system

## 3. UX & Design Direction

### Modern App Architecture
- **Three-Tab Main Navigation**: Timeline, Calendar, Spaces
- **Responsive Design**: Optimized for mobile, tablet, and desktop
- **Space-Centric Organization**: All content organized within themed spaces
- **Unified Timeline**: Cross-space content aggregation with filtering

### Advanced Visual Design
- **Space Theming**: Custom color schemes for each space
- **Material Design 3**: Modern Flutter UI components
- **Adaptive Layouts**: Different layouts for mobile vs desktop
- **Smooth Animations**: Polished transitions and micro-interactions
- **Dark/Light Theme**: System-adaptive theming

### Streamlined User Flows
1. **Space Creation**: Category selection → Color customization → Invite sharing
2. **Content Sharing**: Quick post creation with rich media support
3. **Timeline Navigation**: Date picker → Hour-based content organization
4. **Space Collaboration**: Permanent invite codes → Instant joining

## 4. Technical Architecture

### Three-Tier Architecture
```
[Frontend: Flutter App] <--> [Backend: Node.js API] <--> [Database: SQLite/MariaDB]
```

### Frontend (Flutter)
- **Framework**: Flutter 3.32.4 (Dart language)
- **Architecture**: Clean Architecture with BLoC pattern
- **State Management**: flutter_bloc for reactive state management
- **Navigation**: go_router for declarative routing
- **Dependency Injection**: get_it service locator pattern
- **Platform**: Cross-platform (iOS, Android, Web, Desktop)

### Backend API (NestJS)
- **Framework**: Node.js with NestJS (TypeScript)
- **Architecture**: Modular RESTful API with proper error handling
- **Authentication**: JWT-based with bcrypt password hashing
- **File Upload**: Multer for media file handling
- **Validation**: class-validator and class-transformer
- **Documentation**: Swagger/OpenAPI integration

### Database
- **Development**: SQLite for local development
- **Production**: MariaDB for production deployment
- **ORM**: TypeORM with entity-based modeling
- **Features**: ACID compliance, migrations, relationships

### Key Dependencies
- **Frontend**: flutter_bloc, go_router, dio, get_it, table_calendar
- **Backend**: @nestjs/core, typeorm, passport-jwt, multer, class-validator
- **Database**: SQLite (dev), MariaDB (prod), TypeORM migrations

## 5. Current Implementation Status

### ✅ Completed Features (V2.0)
- **Authentication System**: Complete user registration, login, JWT management
- **Space Management**: Full CRUD with categories, colors, and invite system
- **Content System**: Unified post creation with rich media support
- **Timeline Views**: Responsive timeline with 24-hour organization
- **Calendar Integration**: Interactive calendar with post visualization
- **Chat System**: Real-time messaging with WhatsApp-style interface
- **Theme System**: Space-based color theming throughout the app
- **Navigation**: Three-tab main interface with responsive design

### 🚧 In Development
- **Activity Planning**: Framework exists, features to be implemented
- **Expense Sharing**: Framework exists, balance tracking to be added
- **Push Notifications**: Backend ready, frontend integration pending
- **Advanced Media**: Video processing and optimization

### 📋 Future Enhancements
- **Real-time Sync**: WebSocket integration for live updates
- **Offline Support**: Local caching and sync capabilities
- **Advanced Search**: Full-text search across posts and spaces
- **Export Features**: Data export and backup functionality

## 6. Performance & Quality Metrics

### Technical Excellence
- **Response Times**: API endpoints < 200ms average
- **UI Performance**: 60fps animations and smooth scrolling
- **Memory Usage**: Optimized image loading and caching
- **Code Quality**: Clean Architecture with 90%+ test coverage

### User Experience Goals
- **Intuitive Navigation**: < 3 taps to reach any feature
- **Fast Content Loading**: Timeline loads in < 1 second
- **Reliable Sync**: 99.9% message delivery success rate
- **Cross-Platform Consistency**: Identical UX on all platforms
