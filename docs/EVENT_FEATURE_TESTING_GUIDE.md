# Event Feature Testing Guide

## Overview
This guide provides step-by-step instructions for testing the new Event feature implementation in the MeLo chat application.

## Feature Components
The Event feature consists of the following components:

### Backend Components
1. **Event Entity** (`backend/src/entities/event.entity.ts`)
   - Event and EventResponse entities with proper relationships
   - EventStatus and EventResponseStatus enums

2. **Event API** (`backend/src/modules/event/`)
   - Event service with CRUD operations
   - Event controller with RESTful endpoints
   - Event DTOs for request/response handling

3. **Database Migration** (`backend/src/migrations/`)
   - Creates event and event_response tables
   - Establishes proper foreign key relationships

### Frontend Components
1. **Event Models** (`frontend/lib/features/chat/data/models/event_model.dart`)
   - CreateEventRequest for creating events
   - EventModel and EventResponseModel for data representation
   - EventStatus and EventResponseStatus enums

2. **Event Dialog** (`frontend/lib/features/chat/presentation/widgets/create_event_dialog.dart`)
   - User interface for creating event proposals
   - Date/time selection, location, and participant limits

3. **Event Message Widget** (`frontend/lib/features/chat/presentation/widgets/event_message_widget.dart`)
   - Displays event proposals in chat messages
   - Accept/Reject buttons for event responses

4. **Event Tab** (`frontend/lib/features/chat/presentation/widgets/event_activity_tab.dart`)
   - Management interface for all events in a space
   - Categorized by Upcoming, Past, and Cancelled events

5. **Chat Integration** (`frontend/lib/features/chat/presentation/widgets/chat_input.dart`)
   - Event option added to attachment menu
   - Replaces ToGo option with Event option

## Testing Steps

### 1. Backend Testing
Before testing the frontend, ensure the backend is running:

```bash
cd backend
npm run start:dev
```

The backend should start successfully with the new Event module loaded.

### 2. Frontend Setup
Ensure the frontend is properly built and running:

```bash
cd frontend
flutter pub get
flutter run
```

### 3. End-to-End Testing Flow

#### Step 1: Access a Space
1. Login to the application
2. Navigate to any existing space or create a new one
3. Go to the Chat tab

#### Step 2: Create an Event Proposal
1. In the chat input area, tap the attachment button (📎)
2. Select "Event" from the attachment menu
3. Fill in the event details:
   - **Title**: Enter a descriptive event title (required)
   - **Description**: Add optional event description
   - **Date**: Select a future date (required)
   - **Time**: Optionally select a specific time
   - **Location**: Add optional location information
   - **Max Participants**: Set optional participant limit
4. Tap "Create Event" to send the proposal

#### Step 3: Verify Chat Message
1. The event proposal should appear as a message in the chat
2. The message should display:
   - Event icon and title
   - Date and time information
   - Location (if provided)
   - Accept/Reject buttons
   - Participant limit (if set)

#### Step 4: Test Event Responses
1. Other users in the space should see the event proposal
2. Users can tap "Accept" or "Decline" buttons
3. Response counts should update in real-time
4. Users should see confirmation messages for their responses

#### Step 5: Event Tab Management
1. Navigate to the "Event" tab in the space
2. Verify the event appears in the "Upcoming" section
3. Check that the event displays:
   - Event title and details
   - Date/time information
   - Creator information
   - Response summary (accepted/rejected/pending counts)
   - Status chip (Pending/Confirmed/Cancelled)

#### Step 6: Event Status Management
1. Tap on an event in the Activity tab to view details
2. Test event status changes (when implemented):
   - Confirm events
   - Cancel events
   - View detailed response lists

### 4. Edge Cases to Test

#### Invalid Data Handling
1. Try creating an event without a title
2. Try creating an event without a date
3. Test with very long titles/descriptions
4. Test with invalid participant limits

#### UI Responsiveness
1. Test on different screen sizes (mobile, tablet, desktop)
2. Verify scrolling behavior in Activity tab
3. Test with many events in the list
4. Verify loading states and error handling

#### Real-time Updates
1. Create events from multiple users simultaneously
2. Verify real-time response updates
3. Test WebSocket connectivity during event operations

### 5. Known Limitations

#### Current Implementation Status
- ✅ Event creation in chat
- ✅ Event message display
- ✅ Basic Activity tab structure
- ⚠️ Event API integration (placeholder implementation)
- ⚠️ Real-time response handling (basic implementation)
- ❌ Event details dialog
- ❌ Event editing functionality
- ❌ Event deletion functionality
- ❌ Advanced event management features

#### Future Enhancements
1. **Event Details Dialog**: Full event information and response management
2. **Event Editing**: Allow creators to modify event details
3. **Event Notifications**: Push notifications for event updates
4. **Calendar Integration**: Show events in the Calendar tab
5. **Recurring Events**: Support for repeating events
6. **Event Reminders**: Automatic reminders before events
7. **RSVP Management**: Advanced response tracking and management

### 6. Troubleshooting

#### Common Issues
1. **Event not appearing in chat**: Check backend logs for API errors
2. **Activity tab empty**: Verify event API endpoints are working
3. **Response buttons not working**: Check WebSocket connection
4. **UI layout issues**: Verify responsive design on different devices

#### Debug Steps
1. Check browser console for JavaScript errors
2. Verify network requests in browser dev tools
3. Check backend logs for API call errors
4. Ensure database migration has run successfully

## Conclusion
This testing guide covers the complete Event feature implementation. The feature provides a solid foundation for event management within the MeLo chat application, with room for future enhancements and improvements.
