# MeLo - Feature Overview (V3.0)

## 📱 Application Structure

### Main Navigation (Three-Tab Interface)
1. **Timeline Tab**: Cross-space content aggregation with 24-hour organization
2. **Calendar Tab**: Interactive calendar with post visualization and date navigation
3. **Spaces Tab**: Space management, creation, and member collaboration

### Space-Based Organization
Each space contains four functional tabs:
1. **Chat Tab**: Real-time messaging with rich media and interactive features (primary tab)
2. **Posts Tab**: Content creation and timeline view (legacy - being phased out)
3. **Event Tab**: Interactive activities including polls, dice, lucky draws, and location sharing
4. **Balance Tab**: Complete expense sharing system with multi-currency support and split/settle functionality

## 🎯 Core Features

### 1. User Authentication & Profile Management
- **Registration/Login**: Email and password-based authentication
- **JWT Security**: Secure session management with token-based auth
- **User Profiles**: Display name, avatar, and profile customization
- **Personal Space**: Auto-created private space for each user

### 2. Advanced Space System

#### Space Types & Categories
- **Personal Spaces**: Private, individual memory containers
- **Shared Spaces**: Collaborative spaces with multiple members
- **10 Categories**: General, Family, Friends, Couple, Team, Work, Hobby, Travel, Study, Project

#### Space Customization
- **Custom Colors**: 24 preset colors + HSV color picker for unlimited customization
- **Multi-Currency Support**: Each space can set its own default currency (90+ supported currencies)
- **Category Icons**: Unique icons for each space category
- **Theme Integration**: Space colors applied throughout the interface

#### Invitation System
- **Permanent Invite Codes**: 8-character codes that never expire
- **Simple Joining**: Users enter invite code to join spaces instantly
- **Member Management**: View members, manage permissions

### 3. Content Creation & Management

#### Unified Post System
- **Rich Text**: Multi-line text content with formatting
- **Media Support**: Images, videos, and file attachments
- **Tag System**: Flexible tagging for content organization
- **Content Types**: Support for various post formats

#### Media Handling
- **Image Upload**: Optimized image processing and storage
- **File Attachments**: Document and media file support
- **Thumbnail Generation**: Automatic preview generation

### 4. Timeline & Calendar Views

#### Advanced Timeline
- **24-Hour Organization**: Posts grouped by hour with visual time indicators
- **Infinite Scroll**: Smooth pagination with automatic loading
- **Date Navigation**: Slot-machine style date picker with smooth animations
- **Cross-Space View**: Unified timeline showing posts from all user spaces
- **Responsive Design**: Optimized layouts for mobile, tablet, and desktop

#### Interactive Calendar
- **Post Visualization**: Calendar cells show post count indicators
- **Date Selection**: Click any date to view posts from that day
- **Month Navigation**: Smooth month-to-month navigation
- **Content Preview**: Selected date posts displayed below calendar

### 5. Real-Time Communication & Interactive Features

#### WhatsApp-Style Chat
- **Message Bubbles**: Distinct sender/receiver styling with user avatars
- **Real-Time Messaging**: Instant message delivery and display with WebSocket support
- **Space Theming**: Chat interface adapts to space color schemes
- **Message History**: Persistent chat history within spaces
- **Unread Badge System**: Smart notification badges with real-time updates

#### Rich Attachment Support
- **Camera Integration**: Direct photo capture and sharing
- **Gallery Access**: Select and share existing photos
- **Document Sharing**: Upload and share various file types
- **Location Sharing**: Share current location with map integration

#### Interactive Chat Features
- **Split Bills**: Create and manage expense splits with multi-currency support
- **Polls**: Create interactive polls with real-time voting and results
- **Dice Rolling**: Virtual dice with customizable sides and animations
- **Lucky Draws**: Random selection from participant lists with invite-only options
- **Activity Tracking**: All interactive elements are tracked in the Event tab

### 6. Complete Balance & Expense Management System

#### Multi-Currency Split System
- **Smart Splitting**: Equal, custom, and no-split options for flexible expense sharing
- **Multi-Currency Support**: 90+ international currencies with real-time exchange rates
- **Automatic Conversion**: Expenses automatically converted to space default currency
- **Original Amount Tracking**: Preserves original input amounts and currencies for history

#### Debt Management
- **Balance Overview**: Clear visualization of who owes whom and how much
- **Debt Consolidation**: Smart calculation of net balances between users
- **Settlement Tracking**: Record payments and automatically update balances
- **Currency Consistency**: All calculations unified in space default currency

#### Advanced Features
- **Recent Activity**: Track recent splits and settlements with detailed history
- **Space Total**: Overview of total expenses across all splits in the space
- **Long-Press Actions**: Intuitive edit and delete operations for splits and settlements
- **Real-Time Updates**: Instant balance updates when splits or payments are made
- **Data Integrity**: Complete audit trail with original amounts, exchange rates, and timestamps

## 🎨 Design & User Experience

### Visual Design System
- **Material Design 3**: Modern Flutter UI components and patterns
- **Space Theming**: Custom color schemes applied consistently
- **Responsive Layouts**: Adaptive design for all screen sizes
- **Smooth Animations**: Polished transitions and micro-interactions

### Navigation Patterns
- **Bottom Navigation**: Primary three-tab interface for main features
- **Tab Navigation**: Secondary tabs within spaces for different functions
- **Floating Action Buttons**: Context-aware action buttons per tab
- **System Back Button**: Proper Android back button handling

### Accessibility & Performance
- **Touch Optimization**: Appropriate touch targets for mobile devices
- **Keyboard Navigation**: Support for keyboard navigation on desktop
- **Performance Optimization**: Efficient rendering and memory management
- **Cross-Platform**: Consistent experience across all platforms

## 🔧 Technical Implementation

### Frontend Architecture
- **Clean Architecture**: Separation of concerns with data, domain, and presentation layers
- **BLoC Pattern**: Reactive state management with flutter_bloc
- **Dependency Injection**: Service locator pattern with get_it
- **Routing**: Declarative navigation with go_router

### Backend Architecture
- **Modular Design**: NestJS modules for auth, spaces, posts, users, media
- **TypeORM Integration**: Entity-based database modeling with relationships
- **JWT Authentication**: Secure token-based authentication system
- **File Upload**: Multer integration for media file handling

### Database Design
- **Relational Model**: Proper entity relationships and foreign keys
- **Migration System**: Version-controlled database schema changes
- **Development/Production**: SQLite for dev, MariaDB for production
- **Data Integrity**: ACID compliance and proper constraints

## 🚀 Future Enhancements

### Enhanced Activity System
- **Event Scheduling**: Calendar integration for activity planning
- **RSVP System**: Member availability and response tracking
- **Activity Notifications**: Real-time reminders and updates
- **Recurring Activities**: Support for repeating events and activities

### Advanced Balance Features
- **Expense Categories**: Detailed categorization and reporting
- **Budget Tracking**: Set and monitor spending limits per space
- **Export Reports**: Generate detailed expense reports and summaries
- **Receipt Scanning**: OCR integration for automatic expense entry

### Technical Enhancements (Planned)
- **Enhanced WebSocket**: More real-time features beyond current chat implementation
- **Offline Support**: Local caching and sync capabilities
- **Push Notifications**: System-wide notification infrastructure
- **Advanced Search**: Full-text search across all content types
- **Data Export**: Comprehensive backup and export functionality
- **API Versioning**: Support for multiple API versions and backward compatibility
